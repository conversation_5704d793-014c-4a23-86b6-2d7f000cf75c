"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Department } from "@/lib/types"
import { calculateCapacityUtilization } from "@/lib/utils"
import { Building2, Users, Plus } from "lucide-react"

interface DepartmentCardProps {
  department: Department
  onAddEmployee?: () => void
  onViewDetails?: () => void
}

export function DepartmentCard({ 
  department, 
  onAddEmployee, 
  onViewDetails 
}: DepartmentCardProps) {
  const employeeCount = department.employees?.length || 0
  const utilization = calculateCapacityUtilization(employeeCount, department.capacity)
  
  const getUtilizationColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500"
    if (percentage >= 75) return "bg-yellow-500"
    return "bg-green-500"
  }

  return (
    <Card className="shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-white to-slate-50/50">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-foreground">
          {department.name}
        </CardTitle>
        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
          <Building2 className="h-5 w-5 text-white" />
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-5">
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-white rounded-lg border border-border/50">
            <span className="text-sm font-medium text-muted-foreground">Department Code:</span>
            <span className="font-mono text-sm font-semibold bg-primary/10 text-primary px-2 py-1 rounded">{department.code}</span>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Users className="h-4 w-4" />
                Employee Count
              </span>
              <span className="font-semibold text-lg">
                {employeeCount} / {department.capacity}
              </span>
            </div>

            <div className="space-y-2">
              <div className="relative">
                <Progress
                  value={utilization}
                  className="h-3 bg-slate-200"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full"></div>
              </div>

              <div className="flex justify-between text-sm">
                <span className="font-medium text-muted-foreground">Capacity Utilization</span>
                <span className={`font-bold ${
                  utilization >= 90 ? 'text-red-600' :
                  utilization >= 75 ? 'text-orange-600' :
                  'text-green-600'
                }`}>
                  {utilization}%
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex gap-3 pt-3">
            {onAddEmployee && (
              <Button
                size="sm"
                variant="outline"
                onClick={onAddEmployee}
                className="flex-1 h-9 border-2 border-border/50 hover:border-primary hover:bg-primary/5 transition-all"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Employee
              </Button>
            )}

            {onViewDetails && (
              <Button
                size="sm"
                variant="secondary"
                onClick={onViewDetails}
                className="flex-1 h-9 bg-gradient-to-r from-slate-100 to-slate-200 hover:from-slate-200 hover:to-slate-300 transition-all"
              >
                View Details
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
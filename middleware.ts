import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl

  console.log(`[MIDDLEWARE] Processing request for: ${pathname}`)

  // Public routes that don't require authentication
  const publicRoutes = ['/', '/auth/signin', '/auth/signup', '/auth/error']

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    console.log(`[MIDDLEWARE] Allowing public route: ${pathname}`)
    return NextResponse.next()
  }

  // Get the token
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET
  })

  console.log(`[MIDDLEWARE] Token exists: ${!!token}`)

  // Redirect to signin if not authenticated
  if (!token) {
    console.log(`[MIDDLEWARE] Redirecting to signin from: ${pathname}`)
    const signInUrl = new URL('/auth/signin', req.url)
    signInUrl.searchParams.set('callbackUrl', req.url)
    return NextResponse.redirect(signInUrl)
  }

  // Role-based access control
  const userRole = token.role as string

  // Admin routes
  if (pathname.startsWith('/admin') && userRole !== 'ADMIN') {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // HR Manager routes
  const hrRoutes = ['/employees/create', '/employees/bulk', '/departments/create']
  if (hrRoutes.some(route => pathname.startsWith(route)) &&
      !['ADMIN', 'HR_MANAGER'].includes(userRole)) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
}
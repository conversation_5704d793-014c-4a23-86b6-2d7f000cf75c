/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsdUdBQTRDO0FBQzVDLCtHQUFvRDtBQUNwRCwyR0FBK0MiLCJzb3VyY2VzIjpbIkc6XFxBdWdtZW50IGNvZGVcXG5vZGVfbW9kdWxlc1xcb2F1dGhcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydHMuT0F1dGggPSByZXF1aXJlKFwiLi9saWIvb2F1dGhcIikuT0F1dGg7XG5leHBvcnRzLk9BdXRoRWNobyA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aEVjaG87XG5leHBvcnRzLk9BdXRoMiA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aDJcIikuT0F1dGgyOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

eval("// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost= function( hostName ) {\n  return hostName && hostName.match(\".*google(apis)?.com$\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBIiwic291cmNlcyI6WyJHOlxcQXVnbWVudCBjb2RlXFxub2RlX21vZHVsZXNcXG9hdXRoXFxsaWJcXF91dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXR1cm5zIHRydWUgaWYgdGhpcyBpcyBhIGhvc3QgdGhhdCBjbG9zZXMgKmJlZm9yZSogaXQgZW5kcz8hPyFcbm1vZHVsZS5leHBvcnRzLmlzQW5FYXJseUNsb3NlSG9zdD0gZnVuY3Rpb24oIGhvc3ROYW1lICkge1xuICByZXR1cm4gaG9zdE5hbWUgJiYgaG9zdE5hbWUubWF0Y2goXCIuKmdvb2dsZShhcGlzKT8uY29tJFwiKVxufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    sha1= __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth= function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = false;\n\n  this._requestUrl= requestUrl;\n  this._accessUrl= accessUrl;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n  if( authorize_callback === undefined ) {\n    this._authorize_callback= \"oob\";\n  }\n  else {\n    this._authorize_callback= authorize_callback;\n  }\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod )\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"}\n  this._clientOptions= this._defaultClientOptions= {\"requestTokenHttpMethod\": \"POST\",\n                                                    \"accessTokenHttpMethod\": \"POST\",\n                                                    \"followRedirects\": true};\n  this._oauthParameterSeperator = \",\";\n};\n\nexports.OAuthEcho= function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = true;\n\n  this._realm= realm;\n  this._verifyCredentials = verify_credentials;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod );\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"};\n  this._oauthParameterSeperator = \",\";\n}\n\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\n\nexports.OAuth.prototype._getTimestamp= function() {\n  return Math.floor( (new Date()).getTime() / 1000 );\n}\n\nexports.OAuth.prototype._encodeData= function(toEncode){\n if( toEncode == null || toEncode == \"\" ) return \"\"\n else {\n    var result= encodeURIComponent(toEncode);\n    // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n    return result.replace(/\\!/g, \"%21\")\n                 .replace(/\\'/g, \"%27\")\n                 .replace(/\\(/g, \"%28\")\n                 .replace(/\\)/g, \"%29\")\n                 .replace(/\\*/g, \"%2A\");\n }\n}\n\nexports.OAuth.prototype._decodeData= function(toDecode) {\n  if( toDecode != null ) {\n    toDecode = toDecode.replace(/\\+/g, \" \");\n  }\n  return decodeURIComponent( toDecode);\n}\n\nexports.OAuth.prototype._getSignature= function(method, url, parameters, tokenSecret) {\n  var signatureBase= this._createSignatureBase(method, url, parameters);\n  return this._createSignature( signatureBase, tokenSecret );\n}\n\nexports.OAuth.prototype._normalizeUrl= function(url) {\n  var parsedUrl= URL.parse(url, true)\n   var port =\"\";\n   if( parsedUrl.port ) {\n     if( (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" ) ||\n         (parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") ) {\n           port= \":\" + parsedUrl.port;\n         }\n   }\n\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n\n  return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n}\n\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter= function(parameter) {\n  var m = parameter.match('^oauth_');\n  if( m && ( m[0] === \"oauth_\" ) ) {\n    return true;\n  }\n  else {\n    return false;\n  }\n};\n\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders= function(orderedParameters) {\n  var authHeader=\"OAuth \";\n  if( this._isEcho ) {\n    authHeader += 'realm=\"' + this._realm + '\",';\n  }\n\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n     // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n     // should appear within the authorization header.\n     if( this._isParameterNameAnOAuthParameter(orderedParameters[i][0]) ) {\n      authHeader+= \"\" + this._encodeData(orderedParameters[i][0])+\"=\\\"\"+ this._encodeData(orderedParameters[i][1])+\"\\\"\"+ this._oauthParameterSeperator;\n     }\n  }\n\n  authHeader= authHeader.substring(0, authHeader.length-this._oauthParameterSeperator.length);\n  return authHeader;\n}\n\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash= function(argumentsHash) {\n  var argument_pairs= [];\n  for(var key in argumentsHash ) {\n    if (argumentsHash.hasOwnProperty(key)) {\n       var value= argumentsHash[key];\n       if( Array.isArray(value) ) {\n         for(var i=0;i<value.length;i++) {\n           argument_pairs[argument_pairs.length]= [key, value[i]];\n         }\n       }\n       else {\n         argument_pairs[argument_pairs.length]= [key, value];\n       }\n    }\n  }\n  return argument_pairs;\n}\n\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams= function(argument_pairs) {\n  // Sort by name, then value.\n  argument_pairs.sort(function(a,b) {\n      if ( a[0]== b[0] )  {\n        return a[1] < b[1] ? -1 : 1;\n      }\n      else return a[0] < b[0] ? -1 : 1;\n  });\n\n  return argument_pairs;\n}\n\nexports.OAuth.prototype._normaliseRequestParams= function(args) {\n  var argument_pairs= this._makeArrayOfArgumentsHash(args);\n  // First encode them #3.4.1.3.2 .1\n  for(var i=0;i<argument_pairs.length;i++) {\n    argument_pairs[i][0]= this._encodeData( argument_pairs[i][0] );\n    argument_pairs[i][1]= this._encodeData( argument_pairs[i][1] );\n  }\n\n  // Then sort them #3.4.1.3.2 .2\n  argument_pairs= this._sortRequestParams( argument_pairs );\n\n  // Then concatenate together #3.4.1.3.2 .3 & .4\n  var args= \"\";\n  for(var i=0;i<argument_pairs.length;i++) {\n      args+= argument_pairs[i][0];\n      args+= \"=\"\n      args+= argument_pairs[i][1];\n      if( i < argument_pairs.length-1 ) args+= \"&\";\n  }\n  return args;\n}\n\nexports.OAuth.prototype._createSignatureBase= function(method, url, parameters) {\n  url= this._encodeData( this._normalizeUrl(url) );\n  parameters= this._encodeData( parameters );\n  return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n}\n\nexports.OAuth.prototype._createSignature= function(signatureBase, tokenSecret) {\n   if( tokenSecret === undefined ) var tokenSecret= \"\";\n   else tokenSecret= this._encodeData( tokenSecret );\n   // consumerSecret is already encoded\n   var key= this._consumerSecret + \"&\" + tokenSecret;\n\n   var hash= \"\"\n   if( this._signatureMethod == \"PLAINTEXT\" ) {\n     hash= key;\n   }\n   else if (this._signatureMethod == \"RSA-SHA1\") {\n     key = this._privateKey || \"\";\n     hash= crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, 'base64');\n   }\n   else {\n       if( crypto.Hmac ) {\n         hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n       }\n       else {\n         hash= sha1.HMACSHA1(key, signatureBase);\n       }\n   }\n   return hash;\n}\nexports.OAuth.prototype.NONCE_CHARS= ['a','b','c','d','e','f','g','h','i','j','k','l','m','n',\n              'o','p','q','r','s','t','u','v','w','x','y','z','A','B',\n              'C','D','E','F','G','H','I','J','K','L','M','N','O','P',\n              'Q','R','S','T','U','V','W','X','Y','Z','0','1','2','3',\n              '4','5','6','7','8','9'];\n\nexports.OAuth.prototype._getNonce= function(nonceSize) {\n   var result = [];\n   var chars= this.NONCE_CHARS;\n   var char_pos;\n   var nonce_chars_length= chars.length;\n\n   for (var i = 0; i < nonceSize; i++) {\n       char_pos= Math.floor(Math.random() * nonce_chars_length);\n       result[i]=  chars[char_pos];\n   }\n   return result.join('');\n}\n\nexports.OAuth.prototype._createClient= function( port, hostname, method, path, headers, sslEnabled ) {\n  var options = {\n    host: hostname,\n    port: port,\n    path: path,\n    method: method,\n    headers: headers\n  };\n  var httpModel;\n  if( sslEnabled ) {\n    httpModel= https;\n  } else {\n    httpModel= http;\n  }\n  return httpModel.request(options);\n}\n\nexports.OAuth.prototype._prepareParameters= function( oauth_token, oauth_token_secret, method, url, extra_params ) {\n  var oauthParameters= {\n      \"oauth_timestamp\":        this._getTimestamp(),\n      \"oauth_nonce\":            this._getNonce(this._nonceSize),\n      \"oauth_version\":          this._version,\n      \"oauth_signature_method\": this._signatureMethod,\n      \"oauth_consumer_key\":     this._consumerKey\n  };\n\n  if( oauth_token ) {\n    oauthParameters[\"oauth_token\"]= oauth_token;\n  }\n\n  var sig;\n  if( this._isEcho ) {\n    sig = this._getSignature( \"GET\",  this._verifyCredentials,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n  else {\n    if( extra_params ) {\n      for( var key in extra_params ) {\n        if (extra_params.hasOwnProperty(key)) oauthParameters[key]= extra_params[key];\n      }\n    }\n    var parsedUrl= URL.parse( url, false );\n\n    if( parsedUrl.query ) {\n      var key2;\n      var extraParameters= querystring.parse(parsedUrl.query);\n      for(var key in extraParameters ) {\n        var value= extraParameters[key];\n          if( typeof value == \"object\" ){\n            // TODO: This probably should be recursive\n            for(key2 in value){\n              oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n            }\n          } else {\n            oauthParameters[key]= value;\n          }\n        }\n    }\n\n    sig = this._getSignature( method,  url,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n\n  var orderedParameters= this._sortRequestParams( this._makeArrayOfArgumentsHash(oauthParameters) );\n  orderedParameters[orderedParameters.length]= [\"oauth_signature\", sig];\n  return orderedParameters;\n}\n\nexports.OAuth.prototype._performSecureRequest= function( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type,  callback ) {\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n\n  if( !post_content_type ) {\n    post_content_type= \"application/x-www-form-urlencoded\";\n  }\n  var parsedUrl= URL.parse( url, false );\n  if( parsedUrl.protocol == \"http:\" && !parsedUrl.port ) parsedUrl.port= 80;\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) parsedUrl.port= 443;\n\n  var headers= {};\n  var authorization = this._buildAuthorizationHeaders(orderedParameters);\n  if ( this._isEcho ) {\n    headers[\"X-Verify-Credentials-Authorization\"]= authorization;\n  }\n  else {\n    headers[\"Authorization\"]= authorization;\n  }\n\n  headers[\"Host\"] = parsedUrl.host\n\n  for( var key in this._headers ) {\n    if (this._headers.hasOwnProperty(key)) {\n      headers[key]= this._headers[key];\n    }\n  }\n\n  // Filter out any passed extra_params that are really to do with OAuth\n  for(var key in extra_params) {\n    if( this._isParameterNameAnOAuthParameter( key ) ) {\n      delete extra_params[key];\n    }\n  }\n\n  if( (method == \"POST\" || method == \"PUT\")  && ( post_body == null && extra_params != null) ) {\n    // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n    post_body= querystring.stringify(extra_params)\n                       .replace(/\\!/g, \"%21\")\n                       .replace(/\\'/g, \"%27\")\n                       .replace(/\\(/g, \"%28\")\n                       .replace(/\\)/g, \"%29\")\n                       .replace(/\\*/g, \"%2A\");\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          headers[\"Content-length\"]= post_body.length;\n      } else {\n          headers[\"Content-length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      headers[\"Content-length\"]= 0;\n  }\n\n  headers[\"Content-Type\"]= post_content_type;\n\n  var path;\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n  if( parsedUrl.query ) path= parsedUrl.pathname + \"?\"+ parsedUrl.query ;\n  else path= parsedUrl.pathname;\n\n  var request;\n  if( parsedUrl.protocol == \"https:\" ) {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n  }\n  else {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n  }\n\n  var clientOptions = this._clientOptions;\n  if( callback ) {\n    var data=\"\";\n    var self= this;\n\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost( parsedUrl.hostname );\n    var callbackCalled= false;\n    var passBackControl = function( response ) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        if ( response.statusCode >= 200 && response.statusCode <= 299 ) {\n          callback(null, data, response);\n        } else {\n          // Follow 301 or 302 redirects with Location HTTP header\n          if((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n            self._performSecureRequest( oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type,  callback);\n          }\n          else {\n            callback({ statusCode: response.statusCode, data: data }, data, response);\n          }\n        }\n      }\n    }\n\n    request.on('response', function (response) {\n      response.setEncoding('utf8');\n      response.on('data', function (chunk) {\n        data+=chunk;\n      });\n      response.on('end', function () {\n        passBackControl( response );\n      });\n      response.on('close', function () {\n        if( allowEarlyClose ) {\n          passBackControl( response );\n        }\n      });\n    });\n\n    request.on(\"error\", function(err) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        callback( err )\n      }\n    });\n\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    request.end();\n  }\n  else {\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    return request;\n  }\n\n  return;\n}\n\nexports.OAuth.prototype.setClientOptions= function(options) {\n  var key,\n      mergedOptions= {},\n      hasOwnProperty= Object.prototype.hasOwnProperty;\n\n  for( key in this._defaultClientOptions ) {\n    if( !hasOwnProperty.call(options, key) ) {\n      mergedOptions[key]= this._defaultClientOptions[key];\n    } else {\n      mergedOptions[key]= options[key];\n    }\n  }\n\n  this._clientOptions= mergedOptions;\n};\n\nexports.OAuth.prototype.getOAuthAccessToken= function(oauth_token, oauth_token_secret, oauth_verifier,  callback) {\n  var extraParams= {};\n  if( typeof oauth_verifier == \"function\" ) {\n    callback= oauth_verifier;\n  } else {\n    extraParams.oauth_verifier= oauth_verifier;\n  }\n\n   this._performSecureRequest( oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n         if( error ) callback(error);\n         else {\n           var results= querystring.parse( data );\n           var oauth_access_token= results[\"oauth_token\"];\n           delete results[\"oauth_token\"];\n           var oauth_access_token_secret= results[\"oauth_token_secret\"];\n           delete results[\"oauth_token_secret\"];\n           callback(null, oauth_access_token, oauth_access_token_secret, results );\n         }\n   })\n}\n\n// Deprecated\nexports.OAuth.prototype.getProtectedResource= function(url, method, oauth_token, oauth_token_secret, callback) {\n  this._performSecureRequest( oauth_token, oauth_token_secret, method, url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype[\"delete\"]= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype.get= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype._putOrPost= function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  var extra_params= null;\n  if( typeof post_content_type == \"function\" ) {\n    callback= post_content_type;\n    post_content_type= null;\n  }\n  if ( typeof post_body != \"string\" && !Buffer.isBuffer(post_body) ) {\n    post_content_type= \"application/x-www-form-urlencoded\"\n    extra_params= post_body;\n    post_body= null;\n  }\n  return this._performSecureRequest( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback );\n}\n\n\nexports.OAuth.prototype.put= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\nexports.OAuth.prototype.post= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/\nexports.OAuth.prototype.getOAuthRequestToken= function( extraParams, callback ) {\n   if( typeof extraParams == \"function\" ){\n     callback = extraParams;\n     extraParams = {};\n   }\n  // Callbacks are 1.0A related\n  if( this._authorize_callback ) {\n    extraParams[\"oauth_callback\"]= this._authorize_callback;\n  }\n  this._performSecureRequest( null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n    if( error ) callback(error);\n    else {\n      var results= querystring.parse(data);\n\n      var oauth_token= results[\"oauth_token\"];\n      var oauth_token_secret= results[\"oauth_token_secret\"];\n      delete results[\"oauth_token\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_token, oauth_token_secret,  results );\n    }\n  });\n}\n\nexports.OAuth.prototype.signUrl= function(url, oauth_token, oauth_token_secret, method) {\n\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  var parsedUrl= URL.parse( url, false );\n\n  var query=\"\";\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n    query+= orderedParameters[i][0]+\"=\"+ this._encodeData(orderedParameters[i][1]) + \"&\";\n  }\n  query= query.substring(0, query.length-1);\n\n  return parsedUrl.protocol + \"//\"+ parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\n\nexports.OAuth.prototype.authHeader= function(url, oauth_token, oauth_token_secret, method) {\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth2= function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n  this._clientId= clientId;\n  this._clientSecret= clientSecret;\n  this._baseSite= baseSite;\n  this._authorizeUrl= authorizePath || \"/oauth/authorize\";\n  this._accessTokenUrl= accessTokenPath || \"/oauth/access_token\";\n  this._accessTokenName= \"access_token\";\n  this._authMethod= \"Bearer\";\n  this._customHeaders = customHeaders || {};\n  this._useAuthorizationHeaderForGET= false;\n\n  //our agent\n  this._agent = undefined;\n};\n\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n  this._agent = agent;\n};\n\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName= function ( name ) {\n  this._accessTokenName= name;\n}\n\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function ( authMethod ) {\n  this._authMethod = authMethod;\n};\n\n\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n  this._useAuthorizationHeaderForGET= useIt;\n}\n\nexports.OAuth2.prototype._getAccessTokenUrl= function() {\n  return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */\n}\n\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader= function(token) {\n  return this._authMethod + ' ' + token;\n};\n\nexports.OAuth2.prototype._chooseHttpLibrary= function( parsedUrl ) {\n  var http_library= https;\n  // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n  if( parsedUrl.protocol != \"https:\" ) {\n    http_library= http;\n  }\n  return http_library;\n};\n\nexports.OAuth2.prototype._request= function(method, url, headers, post_body, access_token, callback) {\n\n  var parsedUrl= URL.parse( url, true );\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) {\n    parsedUrl.port= 443;\n  }\n\n  var http_library= this._chooseHttpLibrary( parsedUrl );\n\n\n  var realHeaders= {};\n  for( var key in this._customHeaders ) {\n    realHeaders[key]= this._customHeaders[key];\n  }\n  if( headers ) {\n    for(var key in headers) {\n      realHeaders[key] = headers[key];\n    }\n  }\n  realHeaders['Host']= parsedUrl.host;\n\n  if (!realHeaders['User-Agent']) {\n    realHeaders['User-Agent'] = 'Node-oauth';\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          realHeaders[\"Content-Length\"]= post_body.length;\n      } else {\n          realHeaders[\"Content-Length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      realHeaders[\"Content-length\"]= 0;\n  }\n\n  if( access_token && !('Authorization' in realHeaders)) {\n    if( ! parsedUrl.query ) parsedUrl.query= {};\n    parsedUrl.query[this._accessTokenName]= access_token;\n  }\n\n  var queryStr= querystring.stringify(parsedUrl.query);\n  if( queryStr ) queryStr=  \"?\" + queryStr;\n  var options = {\n    host:parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname + queryStr,\n    method: method,\n    headers: realHeaders\n  };\n\n  this._executeRequest( http_library, options, post_body, callback );\n}\n\nexports.OAuth2.prototype._executeRequest= function( http_library, options, post_body, callback ) {\n  // Some hosts *cough* google appear to close the connection early / send no content-length header\n  // allow this behaviour.\n  var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost(options.host);\n  var callbackCalled= false;\n  function passBackControl( response, result ) {\n    if(!callbackCalled) {\n      callbackCalled=true;\n      if( !(response.statusCode >= 200 && response.statusCode <= 299) && (response.statusCode != 301) && (response.statusCode != 302) ) {\n        callback({ statusCode: response.statusCode, data: result });\n      } else {\n        callback(null, result, response);\n      }\n    }\n  }\n\n  var result= \"\";\n\n  //set the agent on the request options\n  if (this._agent) {\n    options.agent = this._agent;\n  }\n\n  var request = http_library.request(options);\n  request.on('response', function (response) {\n    response.on(\"data\", function (chunk) {\n      result+= chunk\n    });\n    response.on(\"close\", function (err) {\n      if( allowEarlyClose ) {\n        passBackControl( response, result );\n      }\n    });\n    response.addListener(\"end\", function () {\n      passBackControl( response, result );\n    });\n  });\n  request.on('error', function(e) {\n    callbackCalled= true;\n    callback(e);\n  });\n\n  if( (options.method == 'POST' || options.method == 'PUT') && post_body ) {\n     request.write(post_body);\n  }\n  request.end();\n}\n\nexports.OAuth2.prototype.getAuthorizeUrl= function( params ) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n}\n\nexports.OAuth2.prototype.getOAuthAccessToken= function(code, params, callback) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  params['client_secret'] = this._clientSecret;\n  var codeParam = (params.grant_type === 'refresh_token') ? 'refresh_token' : 'code';\n  params[codeParam]= code;\n\n  var post_data= querystring.stringify( params );\n  var post_headers= {\n       'Content-Type': 'application/x-www-form-urlencoded'\n   };\n\n\n  this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n    if( error )  callback(error);\n    else {\n      var results;\n      try {\n        // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n        // responses should be in JSON\n        results= JSON.parse( data );\n      }\n      catch(e) {\n        // .... However both Facebook + Github currently use rev05 of the spec\n        // and neither seem to specify a content-type correctly in their response headers :(\n        // clients of these services will suffer a *minor* performance cost of the exception\n        // being thrown\n        results= querystring.parse( data );\n      }\n      var access_token= results[\"access_token\"];\n      var refresh_token= results[\"refresh_token\"];\n      delete results[\"refresh_token\"];\n      callback(null, access_token, refresh_token, results); // callback results =-=\n    }\n  });\n}\n\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource= function(url, access_token, callback) {\n  this._request(\"GET\", url, {}, \"\", access_token, callback );\n}\n\nexports.OAuth2.prototype.get= function(url, access_token, callback) {\n  if( this._useAuthorizationHeaderForGET ) {\n    var headers= {'Authorization': this.buildAuthHeader(access_token) }\n    access_token= null;\n  }\n  else {\n    headers= {};\n  }\n  this._request(\"GET\", url, headers, \"\", access_token, callback );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 1;  /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad  = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha1(s)    { return rstr2hex(rstr_sha1(str2rstr_utf8(s))); }\nfunction b64_sha1(s)    { return rstr2b64(rstr_sha1(str2rstr_utf8(s))); }\nfunction any_sha1(s, e) { return rstr2any(rstr_sha1(str2rstr_utf8(s)), e); }\nfunction hex_hmac_sha1(k, d)\n  { return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction b64_hmac_sha1(k, d)\n  { return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction any_hmac_sha1(k, d, e)\n  { return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e); }\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha1_vm_test()\n{\n  return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n\n/*\n * Calculate the SHA1 of a raw string\n */\nfunction rstr_sha1(s)\n{\n  return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha1(key, data)\n{\n  var bkey = rstr2binb(key);\n  if(bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n\n  var ipad = Array(16), opad = Array(16);\n  for(var i = 0; i < 16; i++)\n  {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n\n  var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input)\n{\n  try { hexcase } catch(e) { hexcase=0; }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for(var i = 0; i < input.length; i++)\n  {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt((x >>> 4) & 0x0F)\n           +  hex_tab.charAt( x        & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input)\n{\n  try { b64pad } catch(e) { b64pad=''; }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for(var i = 0; i < len; i += 3)\n  {\n    var triplet = (input.charCodeAt(i) << 16)\n                | (i + 1 < len ? input.charCodeAt(i+1) << 8 : 0)\n                | (i + 2 < len ? input.charCodeAt(i+2)      : 0);\n    for(var j = 0; j < 4; j++)\n    {\n      if(i * 8 + j * 6 > input.length * 8) output += b64pad;\n      else output += tab.charAt((triplet >>> 6*(3-j)) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding)\n{\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for(i = 0; i < dividend.length; i++)\n  {\n    dividend[i] = (input.charCodeAt(i * 2) << 8) | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while(dividend.length > 0)\n  {\n    quotient = Array();\n    x = 0;\n    for(i = 0; i < dividend.length; i++)\n    {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if(quotient.length > 0 || q > 0)\n        quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for(i = remainders.length - 1; i >= 0; i--)\n    output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 /\n                                    (Math.log(encoding.length) / Math.log(2)))\n  for(i = output.length; i < full_length; i++)\n    output = encoding[0] + output;\n\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input)\n{\n  var output = \"\";\n  var i = -1;\n  var x, y;\n\n  while(++i < input.length)\n  {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if(0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF)\n    {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if(x <= 0x7F)\n      output += String.fromCharCode(x);\n    else if(x <= 0x7FF)\n      output += String.fromCharCode(0xC0 | ((x >>> 6 ) & 0x1F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0xFFFF)\n      output += String.fromCharCode(0xE0 | ((x >>> 12) & 0x0F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0x1FFFFF)\n      output += String.fromCharCode(0xF0 | ((x >>> 18) & 0x07),\n                                    0x80 | ((x >>> 12) & 0x3F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode( input.charCodeAt(i)        & 0xFF,\n                                  (input.charCodeAt(i) >>> 8) & 0xFF);\n  return output;\n}\n\nfunction str2rstr_utf16be(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode((input.charCodeAt(i) >>> 8) & 0xFF,\n                                   input.charCodeAt(i)        & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input)\n{\n  var output = Array(input.length >> 2);\n  for(var i = 0; i < output.length; i++)\n    output[i] = 0;\n  for(var i = 0; i < input.length * 8; i += 8)\n    output[i>>5] |= (input.charCodeAt(i / 8) & 0xFF) << (24 - i % 32);\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length * 32; i += 8)\n    output += String.fromCharCode((input[i>>5] >>> (24 - i % 32)) & 0xFF);\n  return output;\n}\n\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */\nfunction binb_sha1(x, len)\n{\n  /* append padding */\n  x[len >> 5] |= 0x80 << (24 - len % 32);\n  x[((len + 64 >> 9) << 4) + 15] = len;\n\n  var w = Array(80);\n  var a =  1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d =  271733878;\n  var e = -1009589776;\n\n  for(var i = 0; i < x.length; i += 16)\n  {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    var olde = e;\n\n    for(var j = 0; j < 80; j++)\n    {\n      if(j < 16) w[j] = x[i + j];\n      else w[j] = bit_rol(w[j-3] ^ w[j-8] ^ w[j-14] ^ w[j-16], 1);\n      var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)),\n                       safe_add(safe_add(e, w[j]), sha1_kt(j)));\n      e = d;\n      d = c;\n      c = bit_rol(b, 30);\n      b = a;\n      a = t;\n    }\n\n    a = safe_add(a, olda);\n    b = safe_add(b, oldb);\n    c = safe_add(c, oldc);\n    d = safe_add(d, oldd);\n    e = safe_add(e, olde);\n  }\n  return Array(a, b, c, d, e);\n\n}\n\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */\nfunction sha1_ft(t, b, c, d)\n{\n  if(t < 20) return (b & c) | ((~b) & d);\n  if(t < 40) return b ^ c ^ d;\n  if(t < 60) return (b & c) | (b & d) | (c & d);\n  return b ^ c ^ d;\n}\n\n/*\n * Determine the appropriate additive constant for the current iteration\n */\nfunction sha1_kt(t)\n{\n  return (t < 20) ?  1518500249 : (t < 40) ?  1859775393 :\n         (t < 60) ? -1894007588 : -899497514;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safe_add(x, y)\n{\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return (msw << 16) | (lsw & 0xFFFF);\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bit_rol(num, cnt)\n{\n  return (num << cnt) | (num >>> (32 - cnt));\n}\n\nexports.HMACSHA1= function(key, data) {\n  return b64_hmac_sha1(key, data);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;
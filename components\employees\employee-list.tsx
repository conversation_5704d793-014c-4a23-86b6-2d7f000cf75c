"use client"

import React, { useState, useMemo } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useHRStore } from '@/lib/store/hr-store'
import { Employee } from '@/lib/types'
import { 
  Users, 
  Search, 
  Filter,
  Edit,
  Archive,
  Shuffle,
  Download,
  Upload,
  MoreHorizontal,
  Eye,
  Calendar,
  Mail,
  Building2
} from 'lucide-react'
import { toast } from 'sonner'

interface EmployeeListProps {
  onEditEmployee?: (employee: Employee) => void
  onViewEmployee?: (employee: Employee) => void
  showBulkActions?: boolean
}

export function EmployeeList({ 
  onEditEmployee, 
  onViewEmployee,
  showBulkActions = true 
}: EmployeeListProps) {
  const { data: session } = useSession()
  const {
    employees,
    departments,
    selectedEmployees,
    searchQuery,
    isLoading,
    setSearchQuery,
    toggleEmployeeSelection,
    selectAllEmployees,
    clearSelection,
    archiveEmployee
  } = useHRStore()

  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [departmentFilter, setDepartmentFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'hireDate' | 'department'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // Filtered and sorted employees
  const filteredEmployees = useMemo(() => {
    let filtered = employees.filter(emp => {
      // Search filter
      const matchesSearch = !searchQuery || 
        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.id.toLowerCase().includes(searchQuery.toLowerCase())

      // Status filter
      const matchesStatus = statusFilter === 'all' || emp.status === statusFilter

      // Department filter
      const matchesDepartment = departmentFilter === 'all' || 
        (departmentFilter === 'unassigned' && !emp.departmentId) ||
        emp.departmentId === departmentFilter

      return matchesSearch && matchesStatus && matchesDepartment
    })

    // Sort employees
    filtered.sort((a, b) => {
      let aValue: string | Date
      let bValue: string | Date

      switch (sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'hireDate':
          aValue = new Date(a.hireDate)
          bValue = new Date(b.hireDate)
          break
        case 'department':
          const aDept = departments.find(d => d.id === a.departmentId)
          const bDept = departments.find(d => d.id === b.departmentId)
          aValue = aDept?.name || 'غير مخصص'
          bValue = bDept?.name || 'غير مخصص'
          break
        default:
          aValue = a.name
          bValue = b.name
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [employees, departments, searchQuery, statusFilter, departmentFilter, sortBy, sortOrder])

  const handleSelectAll = () => {
    if (selectedEmployees.length === filteredEmployees.length) {
      clearSelection()
    } else {
      selectAllEmployees(filteredEmployees.map(emp => emp.id))
    }
  }

  const handleArchiveSelected = async () => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    if (selectedEmployees.length === 0) {
      toast.warning('يرجى اختيار موظفين للأرشفة')
      return
    }

    const confirmed = window.confirm(`هل أنت متأكد من أرشفة ${selectedEmployees.length} موظف؟`)
    if (!confirmed) return

    try {
      for (const employeeId of selectedEmployees) {
        await archiveEmployee(employeeId, session.user.id)
      }
      clearSelection()
      toast.success(`تم أرشفة ${selectedEmployees.length} موظف بنجاح`)
    } catch (error) {
      toast.error('حدث خطأ أثناء الأرشفة')
    }
  }

  const getStatusBadge = (status: Employee['status']) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', variant: 'default' as const },
      TRANSFERRED: { label: 'منقول', variant: 'secondary' as const },
      PENDING_REMOVAL: { label: 'في انتظار الإزالة', variant: 'destructive' as const },
      ARCHIVED: { label: 'مؤرشف', variant: 'outline' as const }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return 'غير مخصص'
    const department = departments.find(d => d.id === departmentId)
    return department?.name || 'قسم محذوف'
  }

  return (
    <div className="container-spaced">
      {/* Header */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
            قائمة الموظفين ({filteredEmployees.length})
          </CardTitle>
        </CardHeader>
        
        <CardContent className="container-spaced">
          {/* Search and Filters */}
          <div className="grid-spaced md:grid-cols-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="البحث في الموظفين..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="ACTIVE">نشط</SelectItem>
                <SelectItem value="TRANSFERRED">منقول</SelectItem>
                <SelectItem value="PENDING_REMOVAL">في انتظار الإزالة</SelectItem>
                <SelectItem value="ARCHIVED">مؤرشف</SelectItem>
              </SelectContent>
            </Select>

            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية بالقسم" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأقسام</SelectItem>
                <SelectItem value="unassigned">غير مخصص</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-')
              setSortBy(field as typeof sortBy)
              setSortOrder(order as typeof sortOrder)
            }}>
              <SelectTrigger>
                <SelectValue placeholder="ترتيب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name-asc">الاسم (أ-ي)</SelectItem>
                <SelectItem value="name-desc">الاسم (ي-أ)</SelectItem>
                <SelectItem value="hireDate-desc">تاريخ التوظيف (الأحدث)</SelectItem>
                <SelectItem value="hireDate-asc">تاريخ التوظيف (الأقدم)</SelectItem>
                <SelectItem value="department-asc">القسم (أ-ي)</SelectItem>
                <SelectItem value="department-desc">القسم (ي-أ)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {showBulkActions && selectedEmployees.length > 0 && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-blue-900">
                    تم اختيار {selectedEmployees.length} موظف
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleArchiveSelected}
                      className="text-red-700 border-red-300 hover:bg-red-50"
                    >
                      <Archive className="h-4 w-4 ml-1" />
                      أرشفة
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={clearSelection}
                      className="text-blue-700 border-blue-300 hover:bg-blue-50"
                    >
                      إلغاء التحديد
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Employee Table */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                {showBulkActions && (
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                )}
                <TableHead>معرف الموظف</TableHead>
                <TableHead>الاسم</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>القسم</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>تاريخ التوظيف</TableHead>
                <TableHead className="w-24">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmployees.map((employee) => (
                <TableRow key={employee.id} className="hover:bg-muted/50">
                  {showBulkActions && (
                    <TableCell>
                      <Checkbox
                        checked={selectedEmployees.includes(employee.id)}
                        onCheckedChange={() => toggleEmployeeSelection(employee.id)}
                      />
                    </TableCell>
                  )}
                  <TableCell className="font-mono text-sm">{employee.id}</TableCell>
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell className="text-muted-foreground">{employee.email}</TableCell>
                  <TableCell>{getDepartmentName(employee.departmentId)}</TableCell>
                  <TableCell>{getStatusBadge(employee.status)}</TableCell>
                  <TableCell>
                    {new Date(employee.hireDate).toLocaleDateString('ar-SA')}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {onViewEmployee && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onViewEmployee(employee)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      {onEditEmployee && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onEditEmployee(employee)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredEmployees.length === 0 && (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-muted-foreground">
                لا توجد موظفين
              </p>
              <p className="text-sm text-muted-foreground">
                {searchQuery || statusFilter !== 'all' || departmentFilter !== 'all'
                  ? 'لا توجد نتائج تطابق المرشحات المحددة'
                  : 'لم يتم إضافة أي موظفين بعد'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

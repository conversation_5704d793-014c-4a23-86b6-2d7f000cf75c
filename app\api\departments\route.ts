import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/departments - Fetch all departments
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includeEmployees = searchParams.get('includeEmployees') === 'true'

    const departments = await prisma.department.findMany({
      include: {
        employees: includeEmployees ? {
          where: {
            status: 'ACTIVE'
          },
          orderBy: {
            name: 'asc'
          }
        } : false,
        _count: {
          select: {
            employees: {
              where: {
                status: 'ACTIVE'
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // Transform to include employee count
    const departmentsWithStats = departments.map(dept => ({
      ...dept,
      employeeCount: dept._count.employees,
      utilizationPercentage: Math.round((dept._count.employees / dept.capacity) * 100),
      availableSlots: dept.capacity - dept._count.employees,
      isOverCapacity: dept._count.employees > dept.capacity
    }))

    return NextResponse.json(departmentsWithStats)
  } catch (error) {
    console.error('Error fetching departments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch departments' },
      { status: 500 }
    )
  }
}

// POST /api/departments - Create new department
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, code, capacity } = body

    // Validate required fields
    if (!name || !code || !capacity) {
      return NextResponse.json(
        { error: 'Name, code, and capacity are required' },
        { status: 400 }
      )
    }

    // Validate capacity
    if (capacity < 1 || capacity > 1000) {
      return NextResponse.json(
        { error: 'Capacity must be between 1 and 1000' },
        { status: 400 }
      )
    }

    // Validate code format
    const codeRegex = /^[A-Z]{2,5}$/
    if (!codeRegex.test(code.toUpperCase())) {
      return NextResponse.json(
        { error: 'Code must be 2-5 uppercase letters' },
        { status: 400 }
      )
    }

    // Check if name or code already exists
    const existingDepartment = await prisma.department.findFirst({
      where: {
        OR: [
          { name: name.trim() },
          { code: code.toUpperCase().trim() }
        ]
      }
    })

    if (existingDepartment) {
      const field = existingDepartment.name === name.trim() ? 'name' : 'code'
      return NextResponse.json(
        { error: `Department with this ${field} already exists` },
        { status: 409 }
      )
    }

    // Create department
    const department = await prisma.department.create({
      data: {
        name: name.trim(),
        code: code.toUpperCase().trim(),
        capacity: parseInt(capacity)
      },
      include: {
        _count: {
          select: {
            employees: {
              where: {
                status: 'ACTIVE'
              }
            }
          }
        }
      }
    })

    // Transform to include stats
    const departmentWithStats = {
      ...department,
      employeeCount: department._count.employees,
      utilizationPercentage: 0,
      availableSlots: department.capacity,
      isOverCapacity: false
    }

    return NextResponse.json(departmentWithStats, { status: 201 })
  } catch (error) {
    console.error('Error creating department:', error)
    return NextResponse.json(
      { error: 'Failed to create department' },
      { status: 500 }
    )
  }
}

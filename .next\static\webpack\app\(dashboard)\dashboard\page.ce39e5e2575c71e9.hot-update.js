"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/compiler-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/compiler-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/department-card */ \"(app-pages-browser)/./components/dashboard/department-card.tsx\");\n/* harmony import */ var _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/store/hr-store */ \"(app-pages-browser)/./lib/store/hr-store.ts\");\n/* harmony import */ var _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/use-api-data */ \"(app-pages-browser)/./lib/hooks/use-api-data.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage(t0) {\n    var _session_user;\n    _s();\n    const $ = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__.c)(65);\n    if ($[0] !== \"c378d7e8571b0000566f048dfaea15102702c6e5cd051eaea52acdfa4d3137ac\") {\n        for(let $i = 0; $i < 65; $i += 1){\n            $[$i] = Symbol.for(\"react.memo_cache_sentinel\");\n        }\n        $[0] = \"c378d7e8571b0000566f048dfaea15102702c6e5cd051eaea52acdfa4d3137ac\";\n    }\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { departments, employees, freeBucket } = (0,_lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,_lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData)();\n    let t1;\n    let t2;\n    if ($[1] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t1 = ()=>{\n            const loadData = async ()=>{\n                ;\n                try {\n                    await new Promise(_temp);\n                    setIsLoading(false);\n                } catch (t3) {\n                    const error = t3;\n                    console.error(\"Failed to load dashboard data:\", error);\n                    setIsLoading(false);\n                }\n            };\n            loadData();\n        };\n        t2 = [];\n        $[1] = t1;\n        $[2] = t2;\n    } else {\n        t1 = $[1];\n        t2 = $[2];\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(t1, t2);\n    const totalEmployees = employees.length;\n    const totalDepartments = departments.length;\n    const freeBucketCount = freeBucket.length;\n    let t3;\n    if ($[3] !== departments) {\n        t3 = departments.length > 0 ? Math.round(departments.reduce(_temp2, 0) / departments.length) : 0;\n        $[3] = departments;\n        $[4] = t3;\n    } else {\n        t3 = $[4];\n    }\n    const averageUtilization = t3;\n    let t4;\n    if ($[5] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t4 = [\n            {\n                action: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0645\\u0648\\u0638\\u0641\",\n                details: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u0647\\u0646\\u062F\\u0633\\u0629\",\n                time: \"\\u0645\\u0646\\u0630 \\u0633\\u0627\\u0639\\u062A\\u064A\\u0646\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0638\\u0641 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0627\\u0646\\u0636\\u0645\\u062A \\u0641\\u0627\\u0637\\u0645\\u0629 \\u0623\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642\",\n                time: \"\\u0645\\u0646\\u0630 4 \\u0633\\u0627\\u0639\\u0627\\u062A\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0642\\u0633\\u0645 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0648\\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631\",\n                time: \"\\u0645\\u0646\\u0630 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F\"\n            }\n        ];\n        $[5] = t4;\n    } else {\n        t4 = $[5];\n    }\n    const recentActivity = t4;\n    if (isLoading) {\n        let t5;\n        if ($[6] === Symbol.for(\"react.memo_cache_sentinel\")) {\n            t5 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        ...Array(4)\n                    ].map(_temp3)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 39\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 12\n            }, this);\n            $[6] = t5;\n        } else {\n            t5 = $[6];\n        }\n        return t5;\n    }\n    let t5;\n    if ($[7] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t5 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-4xl font-bold gradient-primary bg-clip-text text-transparent\",\n            children: \"لوحة التحكم\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 10\n        }, this);\n        $[7] = t5;\n    } else {\n        t5 = $[7];\n    }\n    const t6 = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name;\n    let t7;\n    if ($[8] !== t6) {\n        t7 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                t5,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground\",\n                    children: [\n                        \"أهلاً بك، \",\n                        t6,\n                        \". إليك ما يحدث في مؤسستك.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 41\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 10\n        }, this);\n        $[8] = t6;\n        $[9] = t7;\n    } else {\n        t7 = $[9];\n    }\n    let t8;\n    if ($[10] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t8 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            variant: \"outline\",\n            className: \"shadow-soft hover-lift border-2 border-border/50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 ml-2\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 97\n                }, this),\n                \"تصدير التقرير\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 10\n        }, this);\n        $[10] = t8;\n    } else {\n        t8 = $[10];\n    }\n    let t9;\n    if ($[11] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t9 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3\",\n            children: [\n                t8,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    className: \"gradient-primary hover:opacity-90 shadow-medium hover-lift\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4 ml-2\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 121\n                        }, this),\n                        \"إضافة موظف\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 42\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 10\n        }, this);\n        $[11] = t9;\n    } else {\n        t9 = $[11];\n    }\n    let t10;\n    if ($[12] !== t7) {\n        t10 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                t7,\n                t9\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 11\n        }, this);\n        $[12] = t7;\n        $[13] = t10;\n    } else {\n        t10 = $[13];\n    }\n    let t11;\n    if ($[14] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t11 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-primary\",\n            children: \"إجمالي الموظفين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, this);\n        $[14] = t11;\n    } else {\n        t11 = $[14];\n    }\n    let t12;\n    if ($[15] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t12 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t11,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 196\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 11\n        }, this);\n        $[15] = t12;\n    } else {\n        t12 = $[15];\n    }\n    let t13;\n    if ($[16] !== totalEmployees) {\n        t13 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-primary\",\n            children: totalEmployees\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 11\n        }, this);\n        $[16] = totalEmployees;\n        $[17] = t13;\n    } else {\n        t13 = $[17];\n    }\n    let t14;\n    if ($[18] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t14 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-primary/70 font-medium mt-1\",\n            children: \"+12% من الشهر الماضي\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 11\n        }, this);\n        $[18] = t14;\n    } else {\n        t14 = $[18];\n    }\n    let t15;\n    if ($[19] !== t13) {\n        t15 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-primary\",\n            children: [\n                t12,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t13,\n                        t14\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 11\n        }, this);\n        $[19] = t13;\n        $[20] = t15;\n    } else {\n        t15 = $[20];\n    }\n    let t16;\n    if ($[21] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t16 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-secondary\",\n            children: \"الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 11\n        }, this);\n        $[21] = t16;\n    } else {\n        t16 = $[21];\n    }\n    let t17;\n    if ($[22] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t17 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t16,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-secondary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 192\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 11\n        }, this);\n        $[22] = t17;\n    } else {\n        t17 = $[22];\n    }\n    let t18;\n    if ($[23] !== totalDepartments) {\n        t18 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-secondary\",\n            children: totalDepartments\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, this);\n        $[23] = totalDepartments;\n        $[24] = t18;\n    } else {\n        t18 = $[24];\n    }\n    let t19;\n    if ($[25] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t19 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-secondary/70 font-medium mt-1\",\n            children: \"الأقسام النشطة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 11\n        }, this);\n        $[25] = t19;\n    } else {\n        t19 = $[25];\n    }\n    let t20;\n    if ($[26] !== t18) {\n        t20 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-secondary\",\n            children: [\n                t17,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t18,\n                        t19\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 168\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 11\n        }, this);\n        $[26] = t18;\n        $[27] = t20;\n    } else {\n        t20 = $[27];\n    }\n    let t21;\n    if ($[28] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t21 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-accent\",\n            children: \"السلة المؤقتة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 11\n        }, this);\n        $[28] = t21;\n    } else {\n        t21 = $[28];\n    }\n    let t22;\n    if ($[29] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t22 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t21,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-accent rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 189\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 219,\n            columnNumber: 11\n        }, this);\n        $[29] = t22;\n    } else {\n        t22 = $[29];\n    }\n    let t23;\n    if ($[30] !== freeBucketCount) {\n        t23 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-accent\",\n            children: freeBucketCount\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 11\n        }, this);\n        $[30] = freeBucketCount;\n        $[31] = t23;\n    } else {\n        t23 = $[31];\n    }\n    let t24;\n    if ($[32] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t24 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-accent/70 font-medium mt-1\",\n            children: \"موظفون غير مخصصين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 11\n        }, this);\n        $[32] = t24;\n    } else {\n        t24 = $[32];\n    }\n    let t25;\n    if ($[33] !== t23) {\n        t25 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-accent\",\n            children: [\n                t22,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t23,\n                        t24\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 165\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 11\n        }, this);\n        $[33] = t23;\n        $[34] = t25;\n    } else {\n        t25 = $[34];\n    }\n    let t26;\n    if ($[35] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t26 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-success\",\n            children: \"متوسط الاستخدام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 11\n        }, this);\n        $[35] = t26;\n    } else {\n        t26 = $[35];\n    }\n    let t27;\n    if ($[36] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t27 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t26,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-success rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 190\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 11\n        }, this);\n        $[36] = t27;\n    } else {\n        t27 = $[36];\n    }\n    let t28;\n    if ($[37] !== averageUtilization) {\n        t28 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-success\",\n            children: [\n                averageUtilization,\n                \"%\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 263,\n            columnNumber: 11\n        }, this);\n        $[37] = averageUtilization;\n        $[38] = t28;\n    } else {\n        t28 = $[38];\n    }\n    let t29;\n    if ($[39] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t29 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-success/70 font-medium mt-1\",\n            children: \"سعة الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 271,\n            columnNumber: 11\n        }, this);\n        $[39] = t29;\n    } else {\n        t29 = $[39];\n    }\n    let t30;\n    if ($[40] !== t28) {\n        t30 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-success\",\n            children: [\n                t27,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t28,\n                        t29\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 278,\n            columnNumber: 11\n        }, this);\n        $[40] = t28;\n        $[41] = t30;\n    } else {\n        t30 = $[41];\n    }\n    let t31;\n    if ($[42] !== t15 || $[43] !== t20 || $[44] !== t25 || $[45] !== t30) {\n        t31 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                t15,\n                t20,\n                t25,\n                t30\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 286,\n            columnNumber: 11\n        }, this);\n        $[42] = t15;\n        $[43] = t20;\n        $[44] = t25;\n        $[45] = t30;\n        $[46] = t31;\n    } else {\n        t31 = $[46];\n    }\n    let t32;\n    if ($[47] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t32 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-2xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 218\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 122\n                    }, this),\n                    \"نظرة عامة على الأقسام\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 297,\n            columnNumber: 11\n        }, this);\n        $[47] = t32;\n    } else {\n        t32 = $[47];\n    }\n    let t33;\n    if ($[48] !== departments) {\n        t33 = departments.slice(0, 6).map(_temp4);\n        $[48] = departments;\n        $[49] = t33;\n    } else {\n        t33 = $[49];\n    }\n    let t34;\n    if ($[50] !== t33) {\n        t34 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2\",\n            children: t33\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 312,\n            columnNumber: 11\n        }, this);\n        $[50] = t33;\n        $[51] = t34;\n    } else {\n        t34 = $[51];\n    }\n    let t35;\n    if ($[52] !== departments.length) {\n        t35 = departments.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                variant: \"outline\",\n                className: \"shadow-soft hover-lift border-2 border-border/50\",\n                children: [\n                    \"عرض جميع الأقسام (\",\n                    departments.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 71\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 37\n        }, this);\n        $[52] = departments.length;\n        $[53] = t35;\n    } else {\n        t35 = $[53];\n    }\n    let t36;\n    if ($[54] !== t34 || $[55] !== t35) {\n        t36 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t32,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            t34,\n                            t35\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 113\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 42\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 11\n        }, this);\n        $[54] = t34;\n        $[55] = t35;\n        $[56] = t36;\n    } else {\n        t36 = $[56];\n    }\n    let t37;\n    if ($[57] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t37 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 216\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 121\n                    }, this),\n                    \"النشاط الأخير\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 11\n        }, this);\n        $[57] = t37;\n    } else {\n        t37 = $[57];\n    }\n    let t38;\n    if ($[58] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t38 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t37,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-spaced\",\n                            children: recentActivity.map(_temp5)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 100\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 87\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 16\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 344,\n            columnNumber: 11\n        }, this);\n        $[58] = t38;\n    } else {\n        t38 = $[58];\n    }\n    let t39;\n    if ($[59] !== t36) {\n        t39 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced lg:grid-cols-3\",\n            children: [\n                t36,\n                t38\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 11\n        }, this);\n        $[59] = t36;\n        $[60] = t39;\n    } else {\n        t39 = $[60];\n    }\n    let t40;\n    if ($[61] !== t10 || $[62] !== t31 || $[63] !== t39) {\n        t40 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-spaced p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen\",\n            children: [\n                t10,\n                t31,\n                t39\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 11\n        }, this);\n        $[61] = t10;\n        $[62] = t31;\n        $[63] = t39;\n        $[64] = t40;\n    } else {\n        t40 = $[64];\n    }\n    return t40;\n}\n_s(DashboardPage, \"tQPfJsDLSP1wpGqWQro21Fafwx0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore,\n        _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData\n    ];\n});\n_c1 = DashboardPage;\nfunction _temp5(activity, index) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start rtl-space-x p-4 rounded-xl gradient-green-soft border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-white rounded-full\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 308\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 194\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 container-spaced mr-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-semibold text-foreground\",\n                        children: activity.action\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 409\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: activity.details\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 487\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground font-medium\",\n                        children: activity.time\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 558\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 363\n            }, this)\n        ]\n    }, index, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 370,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp4(department) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__.DepartmentCard, {\n        department: department,\n        onAddEmployee: ()=>{\n            console.log(\"Add employee to\", department.name);\n        },\n        onViewDetails: ()=>{\n            console.log(\"View details for\", department.name);\n        }\n    }, department.id, false, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 373,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp3(_, i) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-muted rounded w-20\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 bg-muted rounded\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 177\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 50\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-16 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 247\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-muted rounded w-24\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 297\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 234\n            }, this)\n        ]\n    }, i, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 380,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp2(sum, dept) {\n    var _dept_employees;\n    const employeeCount = ((_dept_employees = dept.employees) === null || _dept_employees === void 0 ? void 0 : _dept_employees.length) || 0;\n    return sum + employeeCount / dept.capacity * 100;\n}\nfunction _temp(resolve) {\n    return setTimeout(resolve, 500);\n}\nvar _c1;\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});
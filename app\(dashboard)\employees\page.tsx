"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { EmployeeTable } from "@/components/employees/employee-table"
import { EmployeeFilters } from "@/components/employees/employee-filters"
import { AddEmployeeDialog } from "@/components/employees/add-employee-dialog"
import { BulkActionsBar } from "@/components/employees/bulk-actions-bar"
import { useHRStore } from "@/lib/store/hr-store"
import { useApiData } from "@/lib/hooks/use-api-data"
import { 
  Users, 
  UserPlus, 
  Search,
  Filter,
  Download,
  Upload
} from "lucide-react"

interface EmployeesPageProps {
  params?: Promise<Record<string, string>>
  searchParams?: Promise<Record<string, string | string[]>>
}

export default function EmployeesPage({ params, searchParams }: EmployeesPageProps) {
  const { data: session } = useSession()
  const { 
    employees, 
    departments, 
    selectedEmployees, 
    searchQuery, 
    setSearchQuery,
    getFilteredEmployees,
    clearSelection
  } = useHRStore()
  
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    department: '',
    status: '',
    dateRange: { from: null, to: null }
  })

  // Load API data
  useApiData()

  const filteredEmployees = getFilteredEmployees()
  const hasSelection = selectedEmployees.length > 0

  const handleSearch = (value: string) => {
    setSearchQuery(value)
  }

  const handleClearFilters = () => {
    setFilters({
      department: '',
      status: '',
      dateRange: { from: null, to: null }
    })
    setSearchQuery('')
  }

  const handleExport = () => {
    // TODO: Implement CSV export
    console.log('Exporting employees...')
  }

  const handleImport = () => {
    // TODO: Implement CSV import
    console.log('Importing employees...')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Employees</h1>
          <p className="text-muted-foreground">
            Manage your organization's employees and their assignments.
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleImport}>
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button onClick={() => setShowAddDialog(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Employee
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.length}</div>
            <p className="text-xs text-muted-foreground">
              Active employees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter(emp => emp.status === 'ACTIVE').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transferred</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter(emp => emp.status === 'TRANSFERRED').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Recently transferred
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unassigned</CardTitle>
            <Users className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter(emp => emp.departmentId === null).length}
            </div>
            <p className="text-xs text-muted-foreground">
              In free bucket
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Employee Management</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-8 w-[300px]"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              {(searchQuery || filters.department || filters.status) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFilters}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {showFilters && (
            <div className="mb-4">
              <EmployeeFilters
                filters={filters}
                onFiltersChange={setFilters}
                departments={departments}
              />
            </div>
          )}

          {hasSelection && (
            <div className="mb-4">
              <BulkActionsBar
                selectedCount={selectedEmployees.length}
                onClearSelection={clearSelection}
              />
            </div>
          )}

          <EmployeeTable
            employees={filteredEmployees}
            departments={departments}
          />
        </CardContent>
      </Card>

      {/* Add Employee Dialog */}
      <AddEmployeeDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        departments={departments}
      />
    </div>
  )
}

"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useHRStore } from '@/lib/store/hr-store'
import { syncService, SyncStatus } from '@/lib/services/sync-service'
import { localStorageService } from '@/lib/services/local-storage-service'
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Database,
  Clock,
  AlertTriangle,
  CheckCircle,
  Download,
  Upload,
  HardDrive,
  Cloud
} from 'lucide-react'
import { toast } from 'sonner'

export function SyncStatusPanel() {
  const { loadFromLocalStorage, saveToLocalStorage, syncData, isLoading } = useHRStore()
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: false,
    lastSync: null,
    pendingChanges: 0,
    conflicts: 0,
    autoSyncEnabled: false
  })
  const [storageStats, setStorageStats] = useState({
    employees: 0,
    departments: 0,
    lastSync: null as Date | null,
    storageSize: 0
  })
  const [isSyncing, setIsSyncing] = useState(false)
  const [syncProgress, setSyncProgress] = useState(0)

  // Update sync status periodically
  useEffect(() => {
    const updateStatus = async () => {
      try {
        const [status, stats] = await Promise.all([
          syncService.getSyncStatus(),
          localStorageService.getStorageStats()
        ])
        setSyncStatus(status)
        setStorageStats(stats)
      } catch (error) {
        console.error('Failed to update sync status:', error)
      }
    }

    updateStatus()
    const interval = setInterval(updateStatus, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const handleManualSync = async () => {
    setIsSyncing(true)
    setSyncProgress(0)
    
    try {
      await syncData()
      toast.success('تمت المزامنة بنجاح')
    } catch (error) {
      toast.error('فشلت عملية المزامنة')
    } finally {
      setIsSyncing(false)
      setSyncProgress(0)
    }
  }

  const handleLoadFromLocal = async () => {
    try {
      await loadFromLocalStorage()
      toast.success('تم تحميل البيانات من التخزين المحلي')
    } catch (error) {
      toast.error('فشل في تحميل البيانات المحلية')
    }
  }

  const handleSaveToLocal = async () => {
    try {
      await saveToLocalStorage()
      toast.success('تم حفظ البيانات في التخزين المحلي')
    } catch (error) {
      toast.error('فشل في حفظ البيانات المحلية')
    }
  }

  const handleToggleAutoSync = (enabled: boolean) => {
    if (enabled) {
      syncService.startAutoSync(5) // 5 minutes interval
      toast.success('تم تفعيل المزامنة التلقائية')
    } else {
      syncService.stopAutoSync()
      toast.success('تم إيقاف المزامنة التلقائية')
    }
  }

  const handleClearLocalStorage = async () => {
    const confirmed = window.confirm(
      'هل أنت متأكد من مسح جميع البيانات المحلية؟ هذا الإجراء لا يمكن التراجع عنه.'
    )
    
    if (!confirmed) return

    try {
      await localStorageService.clearAll()
      setStorageStats({
        employees: 0,
        departments: 0,
        lastSync: null,
        storageSize: 0
      })
      toast.success('تم مسح جميع البيانات المحلية')
    } catch (error) {
      toast.error('فشل في مسح البيانات المحلية')
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatLastSync = (date: Date | null): string => {
    if (!date) return 'لم تتم المزامنة بعد'
    
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMinutes < 1) return 'الآن'
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`
    if (diffHours < 24) return `منذ ${diffHours} ساعة`
    return `منذ ${diffDays} يوم`
  }

  return (
    <div className="container-spaced">
      {/* Connection Status */}
      <Card className={`shadow-xl border-0 ${syncStatus.isOnline ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {syncStatus.isOnline ? (
              <Wifi className="h-6 w-6 text-green-600" />
            ) : (
              <WifiOff className="h-6 w-6 text-red-600" />
            )}
            حالة الاتصال
            <Badge variant={syncStatus.isOnline ? "default" : "destructive"}>
              {syncStatus.isOnline ? 'متصل' : 'غير متصل'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid-spaced md:grid-cols-2">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">آخر مزامنة</p>
                <p className="text-xs text-muted-foreground">
                  {formatLastSync(syncStatus.lastSync)}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Database className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">التغييرات المعلقة</p>
                <p className="text-xs text-muted-foreground">
                  {syncStatus.pendingChanges} تغيير
                </p>
              </div>
            </div>
          </div>

          {syncStatus.conflicts > 0 && (
            <div className="mt-4 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-yellow-900">
                    تعارضات في المزامنة
                  </p>
                  <p className="text-xs text-yellow-700">
                    يوجد {syncStatus.conflicts} تعارض يتطلب حل يدوي
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sync Controls */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <RefreshCw className="h-4 w-4 text-white" />
            </div>
            عمليات المزامنة
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          {/* Auto Sync Toggle */}
          <div className="flex items-center justify-between p-3 rounded-lg gradient-green-soft border border-primary/20">
            <div className="flex items-center gap-3">
              <RefreshCw className="h-5 w-5 text-primary" />
              <div>
                <Label htmlFor="auto-sync" className="text-sm font-medium">
                  المزامنة التلقائية
                </Label>
                <p className="text-xs text-muted-foreground">
                  مزامنة البيانات كل 5 دقائق
                </p>
              </div>
            </div>
            <Switch
              id="auto-sync"
              checked={syncStatus.autoSyncEnabled}
              onCheckedChange={handleToggleAutoSync}
            />
          </div>

          {/* Sync Progress */}
          {isSyncing && (
            <div className="container-spaced">
              <div className="flex justify-between text-sm">
                <span>جاري المزامنة...</span>
                <span>{syncProgress}%</span>
              </div>
              <Progress value={syncProgress} className="h-2" />
            </div>
          )}

          {/* Manual Sync Buttons */}
          <div className="grid-spaced md:grid-cols-2">
            <Button
              onClick={handleManualSync}
              disabled={isSyncing || isLoading || !syncStatus.isOnline}
              className="gradient-primary hover:opacity-90 shadow-medium hover-lift"
            >
              <Cloud className="h-4 w-4 ml-2" />
              {isSyncing ? 'جاري المزامنة...' : 'مزامنة يدوية'}
            </Button>

            <Button
              onClick={handleLoadFromLocal}
              disabled={isLoading}
              variant="outline"
              className="shadow-soft hover-lift border-2 border-border/50"
            >
              <Download className="h-4 w-4 ml-2" />
              تحميل من المحلي
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Storage Statistics */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-secondary rounded-lg flex items-center justify-center shadow-lg">
              <HardDrive className="h-4 w-4 text-white" />
            </div>
            إحصائيات التخزين المحلي
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          <div className="grid-spaced md:grid-cols-3">
            <div className="text-center p-3 rounded-lg gradient-green-soft border border-primary/20">
              <div className="text-2xl font-bold text-primary">{storageStats.employees}</div>
              <p className="text-sm text-muted-foreground">موظف محفوظ</p>
            </div>

            <div className="text-center p-3 rounded-lg gradient-green-soft border border-secondary/20">
              <div className="text-2xl font-bold text-secondary">{storageStats.departments}</div>
              <p className="text-sm text-muted-foreground">قسم محفوظ</p>
            </div>

            <div className="text-center p-3 rounded-lg gradient-green-soft border border-accent/20">
              <div className="text-lg font-bold text-accent">
                {formatFileSize(storageStats.storageSize)}
              </div>
              <p className="text-sm text-muted-foreground">حجم البيانات</p>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={handleSaveToLocal}
              disabled={isLoading}
              variant="outline"
              className="flex-1 shadow-soft hover-lift border-2 border-border/50"
            >
              <Upload className="h-4 w-4 ml-2" />
              حفظ في المحلي
            </Button>

            <Button
              onClick={handleClearLocalStorage}
              disabled={isLoading}
              variant="destructive"
              className="flex-1"
            >
              <AlertTriangle className="h-4 w-4 ml-2" />
              مسح البيانات المحلية
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Offline Mode Notice */}
      {!syncStatus.isOnline && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <WifiOff className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-900">
                  وضع عدم الاتصال
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  أنت تعمل حالياً في وضع عدم الاتصال. سيتم حفظ جميع التغييرات محلياً ومزامنتها عند عودة الاتصال.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

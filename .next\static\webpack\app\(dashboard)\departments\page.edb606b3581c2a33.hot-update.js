"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/departments/page",{

/***/ "(app-pages-browser)/./lib/store/hr-store.ts":
/*!*******************************!*\
  !*** ./lib/store/hr-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHRStore: () => (/* binding */ useHRStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/services/employee-service */ \"(app-pages-browser)/./lib/services/employee-service.ts\");\n/* harmony import */ var _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/distribution-service */ \"(app-pages-browser)/./lib/services/distribution-service.ts\");\n/* harmony import */ var _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/local-storage-service */ \"(app-pages-browser)/./lib/services/local-storage-service.ts\");\n/* harmony import */ var _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/sync-service */ \"(app-pages-browser)/./lib/services/sync-service.ts\");\n/* harmony import */ var _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/collaboration-service */ \"(app-pages-browser)/./lib/services/collaboration-service.ts\");\n/* harmony import */ var _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/id-generator */ \"(app-pages-browser)/./lib/utils/id-generator.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst useHRStore = (0,zustand__WEBPACK_IMPORTED_MODULE_7__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_8__.subscribeWithSelector)((set, get)=>({\n        // Initial state\n        employees: [],\n        departments: [],\n        freeBucket: [],\n        selectedEmployees: [],\n        isLoading: false,\n        searchQuery: '',\n        // Data actions\n        setEmployees: (employees)=>set(()=>({\n                    employees,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                })),\n        setDepartments: (departments)=>set({\n                departments\n            }),\n        // Load data from API\n        loadData: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const [employeesResponse, departmentsResponse] = await Promise.all([\n                    fetch('/api/employees'),\n                    fetch('/api/departments?includeEmployees=true')\n                ]);\n                if (employeesResponse.ok && departmentsResponse.ok) {\n                    const employees = await employeesResponse.json();\n                    const departments = await departmentsResponse.json();\n                    set({\n                        employees,\n                        departments,\n                        freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                    });\n                } else {\n                    console.error('Failed to load data from API');\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تحميل البيانات');\n                }\n            } catch (error) {\n                console.error('Error loading data:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في تحميل البيانات');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        addEmployee: (employee)=>set((state)=>({\n                    employees: [\n                        ...state.employees,\n                        employee\n                    ]\n                })),\n        updateEmployee: (id, updates)=>set((state)=>({\n                    employees: state.employees.map((emp)=>emp.id === id ? {\n                            ...emp,\n                            ...updates\n                        } : emp)\n                })),\n        removeEmployee: (id)=>set((state)=>({\n                    employees: state.employees.filter((emp)=>emp.id !== id),\n                    selectedEmployees: state.selectedEmployees.filter((empId)=>empId !== id)\n                })),\n        // Department actions\n        addDepartment: (department)=>set((state)=>({\n                    departments: [\n                        ...state.departments,\n                        department\n                    ]\n                })),\n        updateDepartment: (id, updates)=>set((state)=>({\n                    departments: state.departments.map((dept)=>dept.id === id ? {\n                            ...dept,\n                            ...updates\n                        } : dept)\n                })),\n        removeDepartment: (id)=>set((state)=>({\n                    departments: state.departments.filter((dept)=>dept.id !== id)\n                })),\n        // Enhanced Employee Operations\n        createEmployee: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await fetch('/api/employees', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (response.ok) {\n                    const newEmployee = await response.json();\n                    set((state)=>({\n                            employees: [\n                                ...state.employees,\n                                newEmployee\n                            ],\n                            freeBucket: newEmployee.departmentId === null ? [\n                                ...state.freeBucket,\n                                newEmployee\n                            ] : state.freeBucket\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم إنشاء الموظف بنجاح');\n                    return true;\n                } else {\n                    const error = await response.json();\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.error || 'فشل في إنشاء الموظف');\n                    return false;\n                }\n            } catch (error) {\n                console.error('Error creating employee:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إنشاء الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateEmployeeData: async (id, data)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.updateEmployee(id, data, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم تحديث الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في تحديث الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في تحديث الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        transferEmployee: async (id, data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.transferEmployee(id, data, employees, departments);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket.filter((emp)=>emp.id !== id),\n                                result.data\n                            ] : state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم نقل الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في نقل الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في نقل الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        archiveEmployee: async (id, userId)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.archiveEmployee(id, userId, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم أرشفة الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في أرشفة الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في أرشفة الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Selection actions\n        toggleEmployeeSelection: (id)=>set((state)=>({\n                    selectedEmployees: state.selectedEmployees.includes(id) ? state.selectedEmployees.filter((empId)=>empId !== id) : [\n                        ...state.selectedEmployees,\n                        id\n                    ]\n                })),\n        selectAllEmployees: (ids)=>set({\n                selectedEmployees: ids\n            }),\n        clearSelection: ()=>set({\n                selectedEmployees: []\n            }),\n        // Bulk operations\n        executeBulkOperation: async function(operation) {\n            let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'system';\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.bulkOperation(operation, employees, departments, userId);\n                if (result.success && result.data) {\n                    // Update employees with the results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        result.data.forEach((updatedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === updatedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = updatedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null),\n                            selectedEmployees: []\n                        };\n                    });\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في العملية الجماعية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Search & filter\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        getFilteredEmployees: ()=>{\n            const { employees, searchQuery } = get();\n            if (!searchQuery) return employees;\n            return employees.filter((emp)=>emp.name.toLowerCase().includes(searchQuery.toLowerCase()) || emp.email.toLowerCase().includes(searchQuery.toLowerCase()) || emp.id.toLowerCase().includes(searchQuery.toLowerCase()));\n        },\n        // Free bucket operations\n        moveToFreeBucket: (employeeIds)=>set((state)=>{\n                const movedEmployees = state.employees.filter((emp)=>employeeIds.includes(emp.id)).map((emp)=>({\n                        ...emp,\n                        departmentId: null\n                    }));\n                return {\n                    employees: state.employees.map((emp)=>employeeIds.includes(emp.id) ? {\n                            ...emp,\n                            departmentId: null\n                        } : emp),\n                    freeBucket: [\n                        ...state.freeBucket,\n                        ...movedEmployees\n                    ]\n                };\n            }),\n        removeFromFreeBucket: (employeeIds)=>set((state)=>({\n                    freeBucket: state.freeBucket.filter((emp)=>!employeeIds.includes(emp.id)),\n                    employees: state.employees.filter((emp)=>!employeeIds.includes(emp.id))\n                })),\n        // Distribution\n        distributeEmployees: async (config, userId)=>{\n            const { departments, freeBucket } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.distributeEmployees(freeBucket, departments, config, userId);\n                if (result.success) {\n                    // Update employees with distributed results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        // Update overflow employees\n                        result.overflow.forEach((overflowEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === overflowEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = overflowEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في عملية التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في عملية التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        rebalanceEmployees: async (config, userId)=>{\n            const { departments, employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.rebalanceEmployees(departments, employees, config, userId);\n                if (result.success) {\n                    // Update employees with rebalanced results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في إعادة التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إعادة التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Local Storage & Sync\n        loadFromLocalStorage: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const [employees, departments] = await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                ]);\n                set({\n                    employees,\n                    departments,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                });\n                // Initialize ID generator with loaded data\n                _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n                console.log('Data loaded from local storage');\n            } catch (error) {\n                console.error('Failed to load from local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تحميل البيانات المحلية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        saveToLocalStorage: async ()=>{\n            const { employees, departments } = get();\n            try {\n                await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveEmployees(employees),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveDepartments(departments)\n                ]);\n                console.log('Data saved to local storage');\n            } catch (error) {\n                console.error('Failed to save to local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في حفظ البيانات المحلية');\n            }\n        },\n        syncData: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__.syncService.syncData({\n                    forceSync: true,\n                    resolveConflicts: 'local'\n                });\n                if (result.success) {\n                    // Reload data after sync\n                    const [employees, departments] = await Promise.all([\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                    ]);\n                    set({\n                        employees,\n                        departments,\n                        freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                    });\n                }\n            } catch (error) {\n                console.error('Sync failed:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشلت عملية المزامنة');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Collaboration\n        initializeCollaboration: async (user)=>{\n            try {\n                await _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.initialize(user);\n                // Set up collaboration event listeners\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('employee_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...employee } = data.employee;\n                        set((state)=>({\n                                employees: state.employees.map((emp)=>emp.id === employee.id ? employee : emp).concat(state.employees.find((emp)=>emp.id === employee.id) ? [] : [\n                                    employee\n                                ]),\n                                freeBucket: state.employees.filter((emp)=>emp.departmentId === null)\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                employees: state.employees.filter((emp)=>emp.id !== data.employeeId),\n                                freeBucket: state.freeBucket.filter((emp)=>emp.id !== data.employeeId)\n                            }));\n                    }\n                });\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('department_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...department } = data.department;\n                        set((state)=>({\n                                departments: state.departments.map((dept)=>dept.id === department.id ? department : dept).concat(state.departments.find((dept)=>dept.id === department.id) ? [] : [\n                                    department\n                                ])\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                departments: state.departments.filter((dept)=>dept.id !== data.departmentId)\n                            }));\n                    }\n                });\n                console.log('Collaboration initialized');\n            } catch (error) {\n                console.error('Failed to initialize collaboration:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تهيئة التعاون الفوري');\n            }\n        },\n        syncWithCollaboration: ()=>{\n            const { employees, departments } = get();\n            // Sync current data with collaboration service\n            employees.forEach((employee)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addEmployee(employee);\n            });\n            departments.forEach((department)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addDepartment(department);\n            });\n        },\n        // Initialization\n        initializeIDGenerator: ()=>{\n            const { departments, employees } = get();\n            _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n        }\n    })));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/store/hr-store.ts\n"));

/***/ })

});
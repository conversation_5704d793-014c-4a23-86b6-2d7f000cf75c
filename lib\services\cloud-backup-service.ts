import AWS from 'aws-sdk'
import { Employee, Department } from '@/lib/types'
import { localStorageService } from '@/lib/services/local-storage-service'
import { toast } from 'sonner'

export interface BackupMetadata {
  id: string
  timestamp: Date
  userId: string
  deviceId: string
  version: string
  size: number
  employeeCount: number
  departmentCount: number
  description?: string
  isAutomatic: boolean
}

export interface BackupData {
  metadata: BackupMetadata
  employees: Employee[]
  departments: Department[]
  checksum: string
}

export interface BackupProgress {
  stage: 'preparing' | 'compressing' | 'uploading' | 'verifying' | 'completed' | 'error'
  progress: number
  message: string
  bytesTransferred?: number
  totalBytes?: number
}

export interface RestoreProgress {
  stage: 'downloading' | 'verifying' | 'decompressing' | 'restoring' | 'completed' | 'error'
  progress: number
  message: string
  bytesTransferred?: number
  totalBytes?: number
}

export interface CloudBackupConfig {
  accessKeyId: string
  secretAccessKey: string
  region: string
  bucketName: string
  encryptionKey?: string
}

export class CloudBackupService {
  private static instance: CloudBackupService
  private s3: AWS.S3 | null = null
  private config: CloudBackupConfig | null = null
  private autoBackupInterval: NodeJS.Timeout | null = null

  private constructor() {}

  static getInstance(): CloudBackupService {
    if (!CloudBackupService.instance) {
      CloudBackupService.instance = new CloudBackupService()
    }
    return CloudBackupService.instance
  }

  /**
   * Initialize cloud backup service with AWS credentials
   */
  async initialize(config: CloudBackupConfig): Promise<void> {
    try {
      this.config = config

      // Configure AWS SDK
      AWS.config.update({
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
        region: config.region
      })

      this.s3 = new AWS.S3()

      // Test connection
      await this.testConnection()

      console.log('Cloud backup service initialized')
      toast.success('تم تهيئة خدمة النسخ الاحتياطي السحابي')
    } catch (error) {
      console.error('Failed to initialize cloud backup service:', error)
      throw new Error('فشل في تهيئة خدمة النسخ الاحتياطي السحابي')
    }
  }

  /**
   * Create manual backup
   */
  async createBackup(
    description?: string,
    onProgress?: (progress: BackupProgress) => void
  ): Promise<BackupMetadata> {
    if (!this.s3 || !this.config) {
      throw new Error('خدمة النسخ الاحتياطي غير مهيأة')
    }

    try {
      onProgress?.({
        stage: 'preparing',
        progress: 0,
        message: 'تحضير البيانات للنسخ الاحتياطي...'
      })

      // Get data from local storage
      const exportData = await localStorageService.exportData()
      
      // Create backup metadata
      const metadata: BackupMetadata = {
        id: this.generateBackupId(),
        timestamp: new Date(),
        userId: exportData.metadata?.userId || 'unknown',
        deviceId: exportData.metadata?.deviceId || 'unknown',
        version: '1.0.0',
        size: 0,
        employeeCount: exportData.employees.length,
        departmentCount: exportData.departments.length,
        description,
        isAutomatic: false
      }

      onProgress?.({
        stage: 'compressing',
        progress: 25,
        message: 'ضغط البيانات...'
      })

      // Create backup data
      const backupData: BackupData = {
        metadata,
        employees: exportData.employees,
        departments: exportData.departments,
        checksum: this.calculateChecksum(exportData)
      }

      // Compress and encrypt data
      const compressedData = await this.compressAndEncrypt(backupData)
      metadata.size = compressedData.length

      onProgress?.({
        stage: 'uploading',
        progress: 50,
        message: 'رفع النسخة الاحتياطية...',
        totalBytes: compressedData.length
      })

      // Upload to S3
      const key = `backups/${metadata.userId}/${metadata.id}.backup`
      
      await this.uploadToS3(key, compressedData, (bytesTransferred) => {
        onProgress?.({
          stage: 'uploading',
          progress: 50 + (bytesTransferred / compressedData.length) * 40,
          message: 'رفع النسخة الاحتياطية...',
          bytesTransferred,
          totalBytes: compressedData.length
        })
      })

      onProgress?.({
        stage: 'verifying',
        progress: 95,
        message: 'التحقق من النسخة الاحتياطية...'
      })

      // Verify backup
      await this.verifyBackup(key, metadata.checksum)

      // Save metadata
      await this.saveBackupMetadata(metadata)

      onProgress?.({
        stage: 'completed',
        progress: 100,
        message: 'تم إنشاء النسخة الاحتياطية بنجاح'
      })

      toast.success('تم إنشاء النسخة الاحتياطية بنجاح')
      return metadata
    } catch (error) {
      onProgress?.({
        stage: 'error',
        progress: 0,
        message: `خطأ في إنشاء النسخة الاحتياطية: ${error}`
      })
      
      console.error('Backup failed:', error)
      toast.error('فشل في إنشاء النسخة الاحتياطية')
      throw error
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(
    backupId: string,
    onProgress?: (progress: RestoreProgress) => void
  ): Promise<void> {
    if (!this.s3 || !this.config) {
      throw new Error('خدمة النسخ الاحتياطي غير مهيأة')
    }

    try {
      onProgress?.({
        stage: 'downloading',
        progress: 0,
        message: 'تحميل النسخة الاحتياطية...'
      })

      // Get backup metadata
      const metadata = await this.getBackupMetadata(backupId)
      if (!metadata) {
        throw new Error('النسخة الاحتياطية غير موجودة')
      }

      // Download backup from S3
      const key = `backups/${metadata.userId}/${backupId}.backup`
      const compressedData = await this.downloadFromS3(key, (bytesTransferred, totalBytes) => {
        onProgress?.({
          stage: 'downloading',
          progress: (bytesTransferred / totalBytes) * 40,
          message: 'تحميل النسخة الاحتياطية...',
          bytesTransferred,
          totalBytes
        })
      })

      onProgress?.({
        stage: 'verifying',
        progress: 45,
        message: 'التحقق من سلامة البيانات...'
      })

      onProgress?.({
        stage: 'decompressing',
        progress: 60,
        message: 'إلغاء ضغط البيانات...'
      })

      // Decompress and decrypt data
      const backupData = await this.decompressAndDecrypt(compressedData)

      // Verify checksum
      const calculatedChecksum = this.calculateChecksum({
        employees: backupData.employees,
        departments: backupData.departments,
        metadata: null
      })

      if (calculatedChecksum !== backupData.checksum) {
        throw new Error('فشل في التحقق من سلامة البيانات')
      }

      onProgress?.({
        stage: 'restoring',
        progress: 80,
        message: 'استعادة البيانات...'
      })

      // Restore data to local storage
      await localStorageService.importData({
        employees: backupData.employees,
        departments: backupData.departments
      })

      onProgress?.({
        stage: 'completed',
        progress: 100,
        message: 'تمت استعادة البيانات بنجاح'
      })

      toast.success('تمت استعادة البيانات بنجاح')
    } catch (error) {
      onProgress?.({
        stage: 'error',
        progress: 0,
        message: `خطأ في استعادة البيانات: ${error}`
      })
      
      console.error('Restore failed:', error)
      toast.error('فشل في استعادة البيانات')
      throw error
    }
  }

  /**
   * List available backups
   */
  async listBackups(userId: string): Promise<BackupMetadata[]> {
    if (!this.s3 || !this.config) {
      throw new Error('خدمة النسخ الاحتياطي غير مهيأة')
    }

    try {
      const prefix = `metadata/${userId}/`
      const response = await this.s3.listObjectsV2({
        Bucket: this.config.bucketName,
        Prefix: prefix
      }).promise()

      const backups: BackupMetadata[] = []

      if (response.Contents) {
        for (const object of response.Contents) {
          if (object.Key && object.Key.endsWith('.metadata')) {
            try {
              const metadata = await this.getBackupMetadata(
                object.Key.replace(prefix, '').replace('.metadata', '')
              )
              if (metadata) {
                backups.push(metadata)
              }
            } catch (error) {
              console.warn('Failed to load backup metadata:', object.Key, error)
            }
          }
        }
      }

      // Sort by timestamp (newest first)
      backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      return backups
    } catch (error) {
      console.error('Failed to list backups:', error)
      throw new Error('فشل في جلب قائمة النسخ الاحتياطية')
    }
  }

  /**
   * Delete backup
   */
  async deleteBackup(backupId: string, userId: string): Promise<void> {
    if (!this.s3 || !this.config) {
      throw new Error('خدمة النسخ الاحتياطي غير مهيأة')
    }

    try {
      const backupKey = `backups/${userId}/${backupId}.backup`
      const metadataKey = `metadata/${userId}/${backupId}.metadata`

      await Promise.all([
        this.s3.deleteObject({
          Bucket: this.config.bucketName,
          Key: backupKey
        }).promise(),
        this.s3.deleteObject({
          Bucket: this.config.bucketName,
          Key: metadataKey
        }).promise()
      ])

      toast.success('تم حذف النسخة الاحتياطية')
    } catch (error) {
      console.error('Failed to delete backup:', error)
      toast.error('فشل في حذف النسخة الاحتياطية')
      throw error
    }
  }

  /**
   * Start automatic backup
   */
  startAutoBackup(intervalHours: number = 24): void {
    if (this.autoBackupInterval) {
      clearInterval(this.autoBackupInterval)
    }

    this.autoBackupInterval = setInterval(async () => {
      try {
        await this.createBackup('نسخة احتياطية تلقائية')
        console.log('Automatic backup completed')
      } catch (error) {
        console.error('Automatic backup failed:', error)
      }
    }, intervalHours * 60 * 60 * 1000)

    console.log(`Automatic backup started with ${intervalHours} hour interval`)
  }

  /**
   * Stop automatic backup
   */
  stopAutoBackup(): void {
    if (this.autoBackupInterval) {
      clearInterval(this.autoBackupInterval)
      this.autoBackupInterval = null
      console.log('Automatic backup stopped')
    }
  }

  /**
   * Test S3 connection
   */
  private async testConnection(): Promise<void> {
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    try {
      await this.s3.headBucket({ Bucket: this.config.bucketName }).promise()
    } catch (error) {
      throw new Error('فشل في الاتصال بخدمة التخزين السحابي')
    }
  }

  /**
   * Generate unique backup ID
   */
  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Calculate data checksum
   */
  private calculateChecksum(data: any): string {
    // Simple checksum calculation (in production, use a proper hash function)
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(16)
  }

  /**
   * Compress and encrypt data (simplified implementation)
   */
  private async compressAndEncrypt(data: BackupData): Promise<Buffer> {
    // In a real implementation, use proper compression and encryption
    const jsonString = JSON.stringify(data)
    return Buffer.from(jsonString, 'utf8')
  }

  /**
   * Decompress and decrypt data (simplified implementation)
   */
  private async decompressAndDecrypt(data: Buffer): Promise<BackupData> {
    // In a real implementation, use proper decompression and decryption
    const jsonString = data.toString('utf8')
    return JSON.parse(jsonString)
  }

  /**
   * Upload data to S3
   */
  private async uploadToS3(
    key: string,
    data: Buffer,
    onProgress?: (bytesTransferred: number) => void
  ): Promise<void> {
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    const upload = this.s3.upload({
      Bucket: this.config.bucketName,
      Key: key,
      Body: data,
      ServerSideEncryption: 'AES256'
    })

    upload.on('httpUploadProgress', (progress) => {
      onProgress?.(progress.loaded)
    })

    await upload.promise()
  }

  /**
   * Download data from S3
   */
  private async downloadFromS3(
    key: string,
    onProgress?: (bytesTransferred: number, totalBytes: number) => void
  ): Promise<Buffer> {
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    const response = await this.s3.getObject({
      Bucket: this.config.bucketName,
      Key: key
    }).promise()

    if (!response.Body) {
      throw new Error('No data received from S3')
    }

    return response.Body as Buffer
  }

  /**
   * Verify backup integrity
   */
  private async verifyBackup(key: string, expectedChecksum: string): Promise<void> {
    // In a real implementation, download and verify the backup
    // For now, just check if the object exists
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    await this.s3.headObject({
      Bucket: this.config.bucketName,
      Key: key
    }).promise()
  }

  /**
   * Save backup metadata
   */
  private async saveBackupMetadata(metadata: BackupMetadata): Promise<void> {
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    const key = `metadata/${metadata.userId}/${metadata.id}.metadata`
    const data = Buffer.from(JSON.stringify(metadata), 'utf8')

    await this.s3.upload({
      Bucket: this.config.bucketName,
      Key: key,
      Body: data,
      ContentType: 'application/json'
    }).promise()
  }

  /**
   * Get backup metadata
   */
  private async getBackupMetadata(backupId: string, userId?: string): Promise<BackupMetadata | null> {
    if (!this.s3 || !this.config) {
      throw new Error('S3 not configured')
    }

    try {
      // Use provided userId or try to get from local storage metadata
      let targetUserId = userId
      if (!targetUserId) {
        const metadata = await localStorageService.getMetadata()
        targetUserId = metadata?.userId || 'current-user'
      }

      const key = `metadata/${targetUserId}/${backupId}.metadata`

      const response = await this.s3.getObject({
        Bucket: this.config.bucketName,
        Key: key
      }).promise()

      if (!response.Body) {
        return null
      }

      const jsonString = response.Body.toString('utf8')
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('Failed to get backup metadata:', error)
      return null
    }
  }
}

// Export singleton instance
export const cloudBackupService = CloudBackupService.getInstance()

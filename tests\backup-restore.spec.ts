import { test, expect } from '@playwright/test'

test.describe('Cloud Backup and Restore', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('should display backup management panel', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    await expect(page.locator('text=حالة النسخ الاحتياطي السحابي')).toBeVisible()
    await expect(page.locator('[data-testid="backup-status"]')).toBeVisible()
  })

  test('should show configuration status', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Check configuration status
    const configStatus = page.locator('[data-testid="config-status"]')
    await expect(configStatus).toBeVisible()
    
    const statusText = await configStatus.textContent()
    expect(statusText).toMatch(/(مكون|غير مكون)/)
    
    // Should show configuration button
    await expect(page.locator('text=إعدادات النسخ الاحتياطي')).toBeVisible()
  })

  test('should open backup configuration dialog', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Click configuration button
    await page.click('text=إعدادات النسخ الاحتياطي')
    
    // Verify dialog opened
    await expect(page.locator('text=إعدادات النسخ الاحتياطي السحابي')).toBeVisible()
    await expect(page.locator('[data-testid="access-key-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="secret-key-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="region-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="bucket-input"]')).toBeVisible()
  })

  test('should validate backup configuration form', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Open configuration dialog
    await page.click('text=إعدادات النسخ الاحتياطي')
    
    // Try to save empty form
    await page.click('text=حفظ الإعدادات')
    
    // Should show validation errors
    await expect(page.locator('text=Access Key مطلوب')).toBeVisible()
    await expect(page.locator('text=Secret Key مطلوب')).toBeVisible()
    await expect(page.locator('text=اسم الحاوية مطلوب')).toBeVisible()
  })

  test('should save backup configuration', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Open configuration dialog
    await page.click('text=إعدادات النسخ الاحتياطي')
    
    // Fill configuration form
    await page.fill('[data-testid="access-key-input"]', 'AKIA_TEST_KEY')
    await page.fill('[data-testid="secret-key-input"]', 'test_secret_key')
    await page.fill('[data-testid="region-input"]', 'us-east-1')
    await page.fill('[data-testid="bucket-input"]', 'hr-synergy-test')
    
    // Save configuration
    await page.click('text=حفظ الإعدادات')
    
    // Should show success message
    await expect(page.locator('text=تم حفظ إعدادات النسخ الاحتياطي')).toBeVisible()
  })

  test('should create manual backup', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Skip if not configured
    const configStatus = await page.locator('[data-testid="config-status"]').textContent()
    if (configStatus?.includes('غير مكون')) {
      test.skip('Backup service not configured')
    }
    
    // Add backup description
    await page.fill('[data-testid="backup-description"]', 'نسخة احتياطية اختبار')
    
    // Create backup
    await page.click('text=إنشاء نسخة احتياطية')
    
    // Should show progress
    await expect(page.locator('[data-testid="backup-progress"]')).toBeVisible()
    await expect(page.locator('text=جاري إنشاء النسخة الاحتياطية')).toBeVisible()
    
    // Wait for completion (with timeout)
    await expect(page.locator('text=تم إنشاء النسخة الاحتياطية بنجاح')).toBeVisible({ timeout: 30000 })
  })

  test('should toggle auto-backup', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Skip if not configured
    const configStatus = await page.locator('[data-testid="config-status"]').textContent()
    if (configStatus?.includes('غير مكون')) {
      test.skip('Backup service not configured')
    }
    
    // Toggle auto-backup
    await page.click('[data-testid="auto-backup-toggle"]')
    
    // Should show confirmation
    await expect(page.locator('text=تم تفعيل النسخ الاحتياطي التلقائي')).toBeVisible()
    
    // Toggle off
    await page.click('[data-testid="auto-backup-toggle"]')
    await expect(page.locator('text=تم إيقاف النسخ الاحتياطي التلقائي')).toBeVisible()
  })

  test('should display backup list', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Check backup list section
    await expect(page.locator('text=النسخ الاحتياطية')).toBeVisible()
    
    const backupList = page.locator('[data-testid="backup-list"]')
    if (await backupList.isVisible()) {
      // Should show backup entries
      await expect(page.locator('[data-testid="backup-item"]')).toBeVisible()
      
      // Check backup details
      await expect(page.locator('[data-testid="backup-date"]')).toBeVisible()
      await expect(page.locator('[data-testid="backup-size"]')).toBeVisible()
      await expect(page.locator('[data-testid="backup-type"]')).toBeVisible()
    } else {
      // If no backups, should show empty state
      await expect(page.locator('text=لا توجد نسخ احتياطية')).toBeVisible()
    }
  })

  test('should restore from backup', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Check if backups exist
    const backupItem = page.locator('[data-testid="backup-item"]').first()
    if (await backupItem.isVisible()) {
      // Click restore button
      await page.click('[data-testid="restore-backup"]:first-child')
      
      // Should show confirmation dialog
      await expect(page.locator('text=هل أنت متأكد من استعادة هذه النسخة الاحتياطية')).toBeVisible()
      
      // Confirm restore
      await page.click('text=موافق')
      
      // Should show progress
      await expect(page.locator('[data-testid="restore-progress"]')).toBeVisible()
      await expect(page.locator('text=جاري استعادة النسخة الاحتياطية')).toBeVisible()
      
      // Wait for completion
      await expect(page.locator('text=تمت استعادة البيانات بنجاح')).toBeVisible({ timeout: 30000 })
    } else {
      test.skip('No backups available for restore test')
    }
  })

  test('should delete backup', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Check if backups exist
    const backupItem = page.locator('[data-testid="backup-item"]').first()
    if (await backupItem.isVisible()) {
      // Get initial backup count
      const initialCount = await page.locator('[data-testid="backup-item"]').count()
      
      // Click delete button
      await page.click('[data-testid="delete-backup"]:first-child')
      
      // Should show confirmation dialog
      await expect(page.locator('text=هل أنت متأكد من حذف هذه النسخة الاحتياطية')).toBeVisible()
      
      // Confirm deletion
      await page.click('text=موافق')
      
      // Should show success message
      await expect(page.locator('text=تم حذف النسخة الاحتياطية')).toBeVisible()
      
      // Backup count should decrease
      await page.waitForTimeout(1000)
      const newCount = await page.locator('[data-testid="backup-item"]').count()
      expect(newCount).toBe(initialCount - 1)
    } else {
      test.skip('No backups available for delete test')
    }
  })

  test('should show backup progress with details', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Skip if not configured
    const configStatus = await page.locator('[data-testid="config-status"]').textContent()
    if (configStatus?.includes('غير مكون')) {
      test.skip('Backup service not configured')
    }
    
    // Start backup
    await page.click('text=إنشاء نسخة احتياطية')
    
    // Check progress elements
    const progressBar = page.locator('[data-testid="backup-progress-bar"]')
    if (await progressBar.isVisible()) {
      await expect(progressBar).toBeVisible()
      
      // Check progress percentage
      await expect(page.locator('[data-testid="progress-percentage"]')).toBeVisible()
      
      // Check progress message
      await expect(page.locator('[data-testid="progress-message"]')).toBeVisible()
      
      // Check bytes transferred (if available)
      const bytesInfo = page.locator('[data-testid="bytes-transferred"]')
      if (await bytesInfo.isVisible()) {
        await expect(bytesInfo).toBeVisible()
      }
    }
  })

  test('should handle backup errors gracefully', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Configure with invalid credentials
    await page.click('text=إعدادات النسخ الاحتياطي')
    await page.fill('[data-testid="access-key-input"]', 'INVALID_KEY')
    await page.fill('[data-testid="secret-key-input"]', 'invalid_secret')
    await page.fill('[data-testid="region-input"]', 'invalid-region')
    await page.fill('[data-testid="bucket-input"]', 'invalid-bucket')
    await page.click('text=حفظ الإعدادات')
    
    // Try to create backup
    await page.click('text=إنشاء نسخة احتياطية')
    
    // Should show error message
    await expect(page.locator('text=فشل في إنشاء النسخة الاحتياطية')).toBeVisible({ timeout: 10000 })
  })
})

test.describe('Backup Performance and Reliability', () => {
  test('should handle large backup files efficiently', async ({ page }) => {
    await page.goto('/')
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Skip if not configured
    const configStatus = await page.locator('[data-testid="config-status"]').textContent()
    if (configStatus?.includes('غير مكون')) {
      test.skip('Backup service not configured')
    }
    
    // Measure backup time
    const startTime = Date.now()
    await page.click('text=إنشاء نسخة احتياطية')
    
    // Wait for completion or timeout
    try {
      await expect(page.locator('text=تم إنشاء النسخة الاحتياطية بنجاح')).toBeVisible({ timeout: 60000 })
      const endTime = Date.now()
      const backupTime = endTime - startTime
      
      // Should complete within reasonable time (1 minute)
      expect(backupTime).toBeLessThan(60000)
    } catch (error) {
      // If backup fails due to configuration, that's expected in test environment
      console.log('Backup test skipped due to configuration')
    }
  })

  test('should maintain UI responsiveness during backup', async ({ page }) => {
    await page.goto('/')
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Skip if not configured
    const configStatus = await page.locator('[data-testid="config-status"]').textContent()
    if (configStatus?.includes('غير مكون')) {
      test.skip('Backup service not configured')
    }
    
    // Start backup
    await page.click('text=إنشاء نسخة احتياطية')
    
    // UI should remain responsive
    await page.click('text=الموظفون')
    await expect(page.locator('[data-testid="employee-list"]')).toBeVisible()
    
    // Can navigate back to backup page
    await page.click('text=النسخ الاحتياطي')
    await expect(page.locator('[data-testid="backup-status"]')).toBeVisible()
  })

  test('should show accurate backup file sizes', async ({ page }) => {
    await page.click('text=النسخ الاحتياطي')
    await page.waitForLoadState('networkidle')
    
    // Check backup list
    const backupItem = page.locator('[data-testid="backup-item"]').first()
    if (await backupItem.isVisible()) {
      const sizeElement = page.locator('[data-testid="backup-size"]').first()
      const sizeText = await sizeElement.textContent()
      
      // Should show size in proper format
      expect(sizeText).toMatch(/(بايت|كيلوبايت|ميجابايت|جيجابايت)/)
      
      // Should be a reasonable size (not 0 or negative)
      const sizeNumber = parseFloat(sizeText?.split(' ')[0] || '0')
      expect(sizeNumber).toBeGreaterThan(0)
    }
  })
})

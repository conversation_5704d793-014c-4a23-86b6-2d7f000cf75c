"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/employees/page",{

/***/ "(app-pages-browser)/./lib/store/hr-store.ts":
/*!*******************************!*\
  !*** ./lib/store/hr-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHRStore: () => (/* binding */ useHRStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/services/employee-service */ \"(app-pages-browser)/./lib/services/employee-service.ts\");\n/* harmony import */ var _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/distribution-service */ \"(app-pages-browser)/./lib/services/distribution-service.ts\");\n/* harmony import */ var _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/local-storage-service */ \"(app-pages-browser)/./lib/services/local-storage-service.ts\");\n/* harmony import */ var _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/sync-service */ \"(app-pages-browser)/./lib/services/sync-service.ts\");\n/* harmony import */ var _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/collaboration-service */ \"(app-pages-browser)/./lib/services/collaboration-service.ts\");\n/* harmony import */ var _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/id-generator */ \"(app-pages-browser)/./lib/utils/id-generator.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst useHRStore = (0,zustand__WEBPACK_IMPORTED_MODULE_7__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_8__.subscribeWithSelector)((set, get)=>({\n        // Initial state\n        employees: [],\n        departments: [],\n        freeBucket: [],\n        selectedEmployees: [],\n        isLoading: false,\n        searchQuery: '',\n        // Data actions\n        setEmployees: (employees)=>set(()=>({\n                    employees,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                })),\n        setDepartments: (departments)=>set({\n                departments\n            }),\n        // Load data from API\n        loadData: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const [employeesResponse, departmentsResponse] = await Promise.all([\n                    fetch('/api/employees'),\n                    fetch('/api/departments?includeEmployees=true')\n                ]);\n                if (employeesResponse.ok && departmentsResponse.ok) {\n                    const employees = await employeesResponse.json();\n                    const departments = await departmentsResponse.json();\n                    set({\n                        employees,\n                        departments,\n                        freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                    });\n                } else {\n                    console.error('Failed to load data from API');\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تحميل البيانات');\n                }\n            } catch (error) {\n                console.error('Error loading data:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في تحميل البيانات');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        addEmployee: (employee)=>set((state)=>({\n                    employees: [\n                        ...state.employees,\n                        employee\n                    ]\n                })),\n        updateEmployee: (id, updates)=>set((state)=>({\n                    employees: state.employees.map((emp)=>emp.id === id ? {\n                            ...emp,\n                            ...updates\n                        } : emp)\n                })),\n        removeEmployee: (id)=>set((state)=>({\n                    employees: state.employees.filter((emp)=>emp.id !== id),\n                    selectedEmployees: state.selectedEmployees.filter((empId)=>empId !== id)\n                })),\n        // Department actions\n        addDepartment: (department)=>set((state)=>({\n                    departments: [\n                        ...state.departments,\n                        department\n                    ]\n                })),\n        updateDepartment: (id, updates)=>set((state)=>({\n                    departments: state.departments.map((dept)=>dept.id === id ? {\n                            ...dept,\n                            ...updates\n                        } : dept)\n                })),\n        removeDepartment: (id)=>set((state)=>({\n                    departments: state.departments.filter((dept)=>dept.id !== id)\n                })),\n        // Enhanced Employee Operations\n        createEmployee: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await fetch('/api/employees', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (response.ok) {\n                    const newEmployee = await response.json();\n                    set((state)=>({\n                            employees: [\n                                ...state.employees,\n                                newEmployee\n                            ],\n                            freeBucket: newEmployee.departmentId === null ? [\n                                ...state.freeBucket,\n                                newEmployee\n                            ] : state.freeBucket\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم إنشاء الموظف بنجاح');\n                    return true;\n                } else {\n                    const error = await response.json();\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.error || 'فشل في إنشاء الموظف');\n                    return false;\n                }\n            } catch (error) {\n                console.error('Error creating employee:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إنشاء الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateEmployeeData: async (id, data)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.updateEmployee(id, data, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم تحديث الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في تحديث الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في تحديث الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        transferEmployee: async (id, data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.transferEmployee(id, data, employees, departments);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket.filter((emp)=>emp.id !== id),\n                                result.data\n                            ] : state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم نقل الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في نقل الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في نقل الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        archiveEmployee: async (id, userId)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.archiveEmployee(id, userId, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم أرشفة الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في أرشفة الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في أرشفة الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Selection actions\n        toggleEmployeeSelection: (id)=>set((state)=>({\n                    selectedEmployees: state.selectedEmployees.includes(id) ? state.selectedEmployees.filter((empId)=>empId !== id) : [\n                        ...state.selectedEmployees,\n                        id\n                    ]\n                })),\n        selectAllEmployees: (ids)=>set({\n                selectedEmployees: ids\n            }),\n        clearSelection: ()=>set({\n                selectedEmployees: []\n            }),\n        // Bulk operations\n        executeBulkOperation: async function(operation) {\n            let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'system';\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.bulkOperation(operation, employees, departments, userId);\n                if (result.success && result.data) {\n                    // Update employees with the results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        result.data.forEach((updatedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === updatedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = updatedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null),\n                            selectedEmployees: []\n                        };\n                    });\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في العملية الجماعية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Search & filter\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        getFilteredEmployees: ()=>{\n            const { employees, searchQuery } = get();\n            if (!searchQuery) return employees;\n            return employees.filter((emp)=>emp.name.toLowerCase().includes(searchQuery.toLowerCase()) || emp.email.toLowerCase().includes(searchQuery.toLowerCase()) || emp.id.toLowerCase().includes(searchQuery.toLowerCase()));\n        },\n        // Free bucket operations\n        moveToFreeBucket: (employeeIds)=>set((state)=>{\n                const movedEmployees = state.employees.filter((emp)=>employeeIds.includes(emp.id)).map((emp)=>({\n                        ...emp,\n                        departmentId: null\n                    }));\n                return {\n                    employees: state.employees.map((emp)=>employeeIds.includes(emp.id) ? {\n                            ...emp,\n                            departmentId: null\n                        } : emp),\n                    freeBucket: [\n                        ...state.freeBucket,\n                        ...movedEmployees\n                    ]\n                };\n            }),\n        removeFromFreeBucket: (employeeIds)=>set((state)=>({\n                    freeBucket: state.freeBucket.filter((emp)=>!employeeIds.includes(emp.id)),\n                    employees: state.employees.filter((emp)=>!employeeIds.includes(emp.id))\n                })),\n        // Distribution\n        distributeEmployees: async (config, userId)=>{\n            const { departments, freeBucket } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.distributeEmployees(freeBucket, departments, config, userId);\n                if (result.success) {\n                    // Update employees with distributed results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        // Update overflow employees\n                        result.overflow.forEach((overflowEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === overflowEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = overflowEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في عملية التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في عملية التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        rebalanceEmployees: async (config, userId)=>{\n            const { departments, employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.rebalanceEmployees(departments, employees, config, userId);\n                if (result.success) {\n                    // Update employees with rebalanced results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في إعادة التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إعادة التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Local Storage & Sync\n        loadFromLocalStorage: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const [employees, departments] = await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                ]);\n                set({\n                    employees,\n                    departments,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                });\n                // Initialize ID generator with loaded data\n                _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n                console.log('Data loaded from local storage');\n            } catch (error) {\n                console.error('Failed to load from local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تحميل البيانات المحلية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        saveToLocalStorage: async ()=>{\n            const { employees, departments } = get();\n            try {\n                await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveEmployees(employees),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveDepartments(departments)\n                ]);\n                console.log('Data saved to local storage');\n            } catch (error) {\n                console.error('Failed to save to local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في حفظ البيانات المحلية');\n            }\n        },\n        syncData: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__.syncService.syncData({\n                    forceSync: true,\n                    resolveConflicts: 'local'\n                });\n                if (result.success) {\n                    // Reload data after sync\n                    const [employees, departments] = await Promise.all([\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                    ]);\n                    set({\n                        employees,\n                        departments,\n                        freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                    });\n                }\n            } catch (error) {\n                console.error('Sync failed:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشلت عملية المزامنة');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Collaboration\n        initializeCollaboration: async (user)=>{\n            try {\n                await _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.initialize(user);\n                // Set up collaboration event listeners\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('employee_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...employee } = data.employee;\n                        set((state)=>({\n                                employees: state.employees.map((emp)=>emp.id === employee.id ? employee : emp).concat(state.employees.find((emp)=>emp.id === employee.id) ? [] : [\n                                    employee\n                                ]),\n                                freeBucket: state.employees.filter((emp)=>emp.departmentId === null)\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                employees: state.employees.filter((emp)=>emp.id !== data.employeeId),\n                                freeBucket: state.freeBucket.filter((emp)=>emp.id !== data.employeeId)\n                            }));\n                    }\n                });\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('department_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...department } = data.department;\n                        set((state)=>({\n                                departments: state.departments.map((dept)=>dept.id === department.id ? department : dept).concat(state.departments.find((dept)=>dept.id === department.id) ? [] : [\n                                    department\n                                ])\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                departments: state.departments.filter((dept)=>dept.id !== data.departmentId)\n                            }));\n                    }\n                });\n                console.log('Collaboration initialized');\n            } catch (error) {\n                console.error('Failed to initialize collaboration:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تهيئة التعاون الفوري');\n            }\n        },\n        syncWithCollaboration: ()=>{\n            const { employees, departments } = get();\n            // Sync current data with collaboration service\n            employees.forEach((employee)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addEmployee(employee);\n            });\n            departments.forEach((department)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addDepartment(department);\n            });\n        },\n        // Initialization\n        initializeIDGenerator: ()=>{\n            const { departments, employees } = get();\n            _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n        }\n    })));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/store/hr-store.ts\n"));

/***/ })

});
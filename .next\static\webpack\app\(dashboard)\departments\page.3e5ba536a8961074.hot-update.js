"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/departments/page",{

/***/ "(app-pages-browser)/./lib/store/hr-store.ts":
/*!*******************************!*\
  !*** ./lib/store/hr-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHRStore: () => (/* binding */ useHRStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/services/employee-service */ \"(app-pages-browser)/./lib/services/employee-service.ts\");\n/* harmony import */ var _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/distribution-service */ \"(app-pages-browser)/./lib/services/distribution-service.ts\");\n/* harmony import */ var _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/local-storage-service */ \"(app-pages-browser)/./lib/services/local-storage-service.ts\");\n/* harmony import */ var _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/sync-service */ \"(app-pages-browser)/./lib/services/sync-service.ts\");\n/* harmony import */ var _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/collaboration-service */ \"(app-pages-browser)/./lib/services/collaboration-service.ts\");\n/* harmony import */ var _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/id-generator */ \"(app-pages-browser)/./lib/utils/id-generator.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst useHRStore = (0,zustand__WEBPACK_IMPORTED_MODULE_7__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_8__.subscribeWithSelector)((set, get)=>({\n        // Initial state\n        employees: [],\n        departments: [],\n        freeBucket: [],\n        selectedEmployees: [],\n        isLoading: false,\n        searchQuery: '',\n        // Data actions\n        setEmployees: (employees)=>set((state)=>({\n                    employees,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                })),\n        setDepartments: (departments)=>set({\n                departments\n            }),\n        addEmployee: (employee)=>set((state)=>({\n                    employees: [\n                        ...state.employees,\n                        employee\n                    ]\n                })),\n        updateEmployee: (id, updates)=>set((state)=>({\n                    employees: state.employees.map((emp)=>emp.id === id ? {\n                            ...emp,\n                            ...updates\n                        } : emp)\n                })),\n        removeEmployee: (id)=>set((state)=>({\n                    employees: state.employees.filter((emp)=>emp.id !== id),\n                    selectedEmployees: state.selectedEmployees.filter((empId)=>empId !== id)\n                })),\n        // Department actions\n        addDepartment: (department)=>set((state)=>({\n                    departments: [\n                        ...state.departments,\n                        department\n                    ]\n                })),\n        updateDepartment: (id, updates)=>set((state)=>({\n                    departments: state.departments.map((dept)=>dept.id === id ? {\n                            ...dept,\n                            ...updates\n                        } : dept)\n                })),\n        removeDepartment: (id)=>set((state)=>({\n                    departments: state.departments.filter((dept)=>dept.id !== id)\n                })),\n        // Enhanced Employee Operations\n        createEmployee: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await fetch('/api/employees', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (response.ok) {\n                    const newEmployee = await response.json();\n                    set((state)=>({\n                            employees: [\n                                ...state.employees,\n                                newEmployee\n                            ],\n                            freeBucket: newEmployee.departmentId === null ? [\n                                ...state.freeBucket,\n                                newEmployee\n                            ] : state.freeBucket\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم إنشاء الموظف بنجاح');\n                    return true;\n                } else {\n                    const error = await response.json();\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.error || 'فشل في إنشاء الموظف');\n                    return false;\n                }\n            } catch (error) {\n                console.error('Error creating employee:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إنشاء الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateEmployeeData: async (id, data)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.updateEmployee(id, data, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم تحديث الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في تحديث الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في تحديث الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        transferEmployee: async (id, data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.transferEmployee(id, data, employees, departments);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket.filter((emp)=>emp.id !== id),\n                                result.data\n                            ] : state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم نقل الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في نقل الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في نقل الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        archiveEmployee: async (id, userId)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.archiveEmployee(id, userId, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('تم أرشفة الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || 'فشل في أرشفة الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في أرشفة الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Selection actions\n        toggleEmployeeSelection: (id)=>set((state)=>({\n                    selectedEmployees: state.selectedEmployees.includes(id) ? state.selectedEmployees.filter((empId)=>empId !== id) : [\n                        ...state.selectedEmployees,\n                        id\n                    ]\n                })),\n        selectAllEmployees: (ids)=>set({\n                selectedEmployees: ids\n            }),\n        clearSelection: ()=>set({\n                selectedEmployees: []\n            }),\n        // Bulk operations\n        executeBulkOperation: async function(operation) {\n            let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'system';\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.bulkOperation(operation, employees, departments, userId);\n                if (result.success && result.data) {\n                    // Update employees with the results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        result.data.forEach((updatedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === updatedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = updatedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null),\n                            selectedEmployees: []\n                        };\n                    });\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في العملية الجماعية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Search & filter\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        getFilteredEmployees: ()=>{\n            const { employees, searchQuery } = get();\n            if (!searchQuery) return employees;\n            return employees.filter((emp)=>emp.name.toLowerCase().includes(searchQuery.toLowerCase()) || emp.email.toLowerCase().includes(searchQuery.toLowerCase()) || emp.id.toLowerCase().includes(searchQuery.toLowerCase()));\n        },\n        // Free bucket operations\n        moveToFreeBucket: (employeeIds)=>set((state)=>{\n                const movedEmployees = state.employees.filter((emp)=>employeeIds.includes(emp.id)).map((emp)=>({\n                        ...emp,\n                        departmentId: null\n                    }));\n                return {\n                    employees: state.employees.map((emp)=>employeeIds.includes(emp.id) ? {\n                            ...emp,\n                            departmentId: null\n                        } : emp),\n                    freeBucket: [\n                        ...state.freeBucket,\n                        ...movedEmployees\n                    ]\n                };\n            }),\n        removeFromFreeBucket: (employeeIds)=>set((state)=>({\n                    freeBucket: state.freeBucket.filter((emp)=>!employeeIds.includes(emp.id)),\n                    employees: state.employees.filter((emp)=>!employeeIds.includes(emp.id))\n                })),\n        // Distribution\n        distributeEmployees: async (config, userId)=>{\n            const { departments, freeBucket } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.distributeEmployees(freeBucket, departments, config, userId);\n                if (result.success) {\n                    // Update employees with distributed results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        // Update overflow employees\n                        result.overflow.forEach((overflowEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === overflowEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = overflowEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في عملية التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في عملية التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        rebalanceEmployees: async (config, userId)=>{\n            const { departments, employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.rebalanceEmployees(departments, employees, config, userId);\n                if (result.success) {\n                    // Update employees with rebalanced results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في إعادة التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('خطأ في إعادة التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Local Storage & Sync\n        loadFromLocalStorage: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const [employees, departments] = await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                ]);\n                set({\n                    employees,\n                    departments,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                });\n                // Initialize ID generator with loaded data\n                _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n                console.log('Data loaded from local storage');\n            } catch (error) {\n                console.error('Failed to load from local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تحميل البيانات المحلية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        saveToLocalStorage: async ()=>{\n            const { employees, departments } = get();\n            try {\n                await Promise.all([\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveEmployees(employees),\n                    _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.saveDepartments(departments)\n                ]);\n                console.log('Data saved to local storage');\n            } catch (error) {\n                console.error('Failed to save to local storage:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في حفظ البيانات المحلية');\n            }\n        },\n        syncData: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_sync_service__WEBPACK_IMPORTED_MODULE_3__.syncService.syncData({\n                    forceSync: true,\n                    resolveConflicts: 'local'\n                });\n                if (result.success) {\n                    // Reload data after sync\n                    const [employees, departments] = await Promise.all([\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadEmployees(),\n                        _lib_services_local_storage_service__WEBPACK_IMPORTED_MODULE_2__.localStorageService.loadDepartments()\n                    ]);\n                    set({\n                        employees,\n                        departments,\n                        freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                    });\n                }\n            } catch (error) {\n                console.error('Sync failed:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشلت عملية المزامنة');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Collaboration\n        initializeCollaboration: async (user)=>{\n            try {\n                await _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.initialize(user);\n                // Set up collaboration event listeners\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('employee_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...employee } = data.employee;\n                        set((state)=>({\n                                employees: state.employees.map((emp)=>emp.id === employee.id ? employee : emp).concat(state.employees.find((emp)=>emp.id === employee.id) ? [] : [\n                                    employee\n                                ]),\n                                freeBucket: state.employees.filter((emp)=>emp.departmentId === null)\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                employees: state.employees.filter((emp)=>emp.id !== data.employeeId),\n                                freeBucket: state.freeBucket.filter((emp)=>emp.id !== data.employeeId)\n                            }));\n                    }\n                });\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.on('department_changed', (data)=>{\n                    if (data.action === 'add' || data.action === 'update') {\n                        const { _lastModified, _modifiedBy, _deviceId, ...department } = data.department;\n                        set((state)=>({\n                                departments: state.departments.map((dept)=>dept.id === department.id ? department : dept).concat(state.departments.find((dept)=>dept.id === department.id) ? [] : [\n                                    department\n                                ])\n                            }));\n                    } else if (data.action === 'delete') {\n                        set((state)=>({\n                                departments: state.departments.filter((dept)=>dept.id !== data.departmentId)\n                            }));\n                    }\n                });\n                console.log('Collaboration initialized');\n            } catch (error) {\n                console.error('Failed to initialize collaboration:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('فشل في تهيئة التعاون الفوري');\n            }\n        },\n        syncWithCollaboration: ()=>{\n            const { employees, departments } = get();\n            // Sync current data with collaboration service\n            employees.forEach((employee)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addEmployee(employee);\n            });\n            departments.forEach((department)=>{\n                _lib_services_collaboration_service__WEBPACK_IMPORTED_MODULE_4__.collaborationService.addDepartment(department);\n            });\n        },\n        // Initialization\n        initializeIDGenerator: ()=>{\n            const { departments, employees } = get();\n            _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_5__.idGenerator.initializeCounters(departments, employees);\n        }\n    })));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/store/hr-store.ts\n"));

/***/ })

});
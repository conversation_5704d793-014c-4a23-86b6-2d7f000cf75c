"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { collaborationService, ActiveUser, CollaborationStatus } from '@/lib/services/collaboration-service'
import { 
  Users, 
  Wifi, 
  WifiOff, 
  Eye,
  Clock,
  Activity,
  UserCheck,
  Globe,
  Zap,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { toast } from 'sonner'

export function CollaborationPanel() {
  const [status, setStatus] = useState<CollaborationStatus>({
    isConnected: false,
    activeUsers: [],
    documentState: 'offline',
    lastSync: null,
    pendingChanges: 0
  })
  const [recentActivity, setRecentActivity] = useState<Array<{
    id: string
    type: string
    user: string
    description: string
    timestamp: Date
  }>>([])

  useEffect(() => {
    // Update status periodically
    const updateStatus = () => {
      const currentStatus = collaborationService.getStatus()
      setStatus(currentStatus)
    }

    updateStatus()
    const statusInterval = setInterval(updateStatus, 5000) // Update every 5 seconds

    // Listen for collaboration events
    const handleEmployeeChanged = (data: any) => {
      const activity = {
        id: Date.now().toString(),
        type: 'employee',
        user: data.employee?._modifiedBy || 'مستخدم غير معروف',
        description: data.action === 'add' ? 'أضاف موظف جديد' : 
                    data.action === 'update' ? 'حدث بيانات موظف' : 'حذف موظف',
        timestamp: new Date()
      }
      
      setRecentActivity(prev => [activity, ...prev.slice(0, 9)]) // Keep last 10 activities
      
      if (data.action === 'add') {
        toast.success(`${activity.user} أضاف موظف جديد`)
      } else if (data.action === 'update') {
        toast.info(`${activity.user} حدث بيانات موظف`)
      }
    }

    const handleDepartmentChanged = (data: any) => {
      const activity = {
        id: Date.now().toString(),
        type: 'department',
        user: data.department?._modifiedBy || 'مستخدم غير معروف',
        description: data.action === 'add' ? 'أضاف قسم جديد' : 
                    data.action === 'update' ? 'حدث بيانات قسم' : 'حذف قسم',
        timestamp: new Date()
      }
      
      setRecentActivity(prev => [activity, ...prev.slice(0, 9)])
      
      if (data.action === 'add') {
        toast.success(`${activity.user} أضاف قسم جديد`)
      } else if (data.action === 'update') {
        toast.info(`${activity.user} حدث بيانات قسم`)
      }
    }

    const handleUsersChanged = (users: ActiveUser[]) => {
      setStatus(prev => ({ ...prev, activeUsers: users }))
    }

    const handleConnectionStatus = (data: { connected: boolean; status: string }) => {
      setStatus(prev => ({ 
        ...prev, 
        isConnected: data.connected,
        documentState: data.connected ? 'synced' : 'offline'
      }))
    }

    // Register event listeners
    collaborationService.on('employee_changed', handleEmployeeChanged)
    collaborationService.on('department_changed', handleDepartmentChanged)
    collaborationService.on('users_changed', handleUsersChanged)
    collaborationService.on('connection_status', handleConnectionStatus)

    return () => {
      clearInterval(statusInterval)
      collaborationService.off('employee_changed', handleEmployeeChanged)
      collaborationService.off('department_changed', handleDepartmentChanged)
      collaborationService.off('users_changed', handleUsersChanged)
      collaborationService.off('connection_status', handleConnectionStatus)
    }
  }, [])

  const getStatusIcon = () => {
    switch (status.documentState) {
      case 'synced':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'syncing':
        return <Activity className="h-5 w-5 text-blue-600 animate-pulse" />
      case 'conflict':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'offline':
        return <WifiOff className="h-5 w-5 text-red-600" />
      default:
        return <WifiOff className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusText = () => {
    switch (status.documentState) {
      case 'synced':
        return 'متزامن'
      case 'syncing':
        return 'جاري المزامنة'
      case 'conflict':
        return 'تعارض'
      case 'offline':
        return 'غير متصل'
      default:
        return 'غير معروف'
    }
  }

  const getStatusColor = () => {
    switch (status.documentState) {
      case 'synced':
        return 'bg-green-50 border-green-200'
      case 'syncing':
        return 'bg-blue-50 border-blue-200'
      case 'conflict':
        return 'bg-yellow-50 border-yellow-200'
      case 'offline':
        return 'bg-red-50 border-red-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const formatTimeAgo = (date: Date): string => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)

    if (diffMinutes < 1) return 'الآن'
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`
    if (diffHours < 24) return `منذ ${diffHours} ساعة`
    return date.toLocaleDateString('ar-SA')
  }

  return (
    <div className="container-spaced">
      {/* Connection Status */}
      <Card className={`shadow-xl border-0 ${getStatusColor()}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {getStatusIcon()}
            حالة التعاون الفوري
            <Badge variant={status.isConnected ? "default" : "destructive"}>
              {getStatusText()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid-spaced md:grid-cols-3">
            <div className="flex items-center gap-3">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">حالة الاتصال</p>
                <p className="text-xs text-muted-foreground">
                  {status.isConnected ? 'متصل بالخادم' : 'غير متصل'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">آخر مزامنة</p>
                <p className="text-xs text-muted-foreground">
                  {status.lastSync ? formatTimeAgo(status.lastSync) : 'لم تتم بعد'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">التغييرات المعلقة</p>
                <p className="text-xs text-muted-foreground">
                  {status.pendingChanges} تغيير
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Users */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
            المستخدمون النشطون ({status.activeUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {status.activeUsers.length > 0 ? (
            <div className="container-spaced">
              {status.activeUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center gap-4 p-3 rounded-xl gradient-green-soft border border-border/50"
                >
                  <Avatar className="h-10 w-10 border-2 border-white shadow-lg">
                    <AvatarImage src="" alt={user.name} />
                    <AvatarFallback className="gradient-primary text-white font-bold">
                      {user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium text-foreground">{user.name}</p>
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {user.role}
                      </Badge>
                      {user.currentPage && (
                        <Badge variant="secondary" className="text-xs">
                          <Eye className="h-3 w-3 ml-1" />
                          {user.currentPage}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(user.lastSeen)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-muted-foreground">
                لا يوجد مستخدمون نشطون
              </p>
              <p className="text-sm text-muted-foreground">
                {status.isConnected ? 'أنت المستخدم الوحيد المتصل حالياً' : 'غير متصل بالخادم'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg">
              <Activity className="h-4 w-4 text-white" />
            </div>
            النشاط الأخير
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentActivity.length > 0 ? (
            <div className="container-spaced">
              {recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start gap-4 p-3 rounded-xl gradient-green-soft border border-border/50"
                >
                  <div className="w-8 h-8 gradient-secondary rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                    {activity.type === 'employee' ? (
                      <UserCheck className="h-4 w-4 text-white" />
                    ) : (
                      <Users className="h-4 w-4 text-white" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-sm font-medium text-foreground">
                      {activity.user} {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                  </div>
                  
                  <Badge variant="outline" className="text-xs">
                    {activity.type === 'employee' ? 'موظف' : 'قسم'}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-muted-foreground">
                لا يوجد نشاط حديث
              </p>
              <p className="text-sm text-muted-foreground">
                سيظهر النشاط الفوري للمستخدمين الآخرين هنا
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Offline Notice */}
      {!status.isConnected && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <WifiOff className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-900">
                  وضع عدم الاتصال
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  التعاون الفوري غير متاح حالياً. سيتم حفظ التغييرات محلياً ومزامنتها عند عودة الاتصال.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

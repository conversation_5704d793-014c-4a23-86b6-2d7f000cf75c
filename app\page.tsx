"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Users, 
  Building2, 
  BarChart3, 
  Shield,
  ArrowLeft,
  CheckCircle
} from "lucide-react"

export default function LandingPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (status === "authenticated") {
      router.push("/dashboard")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    )
  }

  if (status === "authenticated") {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">HR Synergy</h1>
                <p className="text-sm text-gray-600">نظام إدارة الموظفين المؤسسي</p>
              </div>
            </div>
            <Button 
              onClick={() => router.push("/auth/signin")}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <ArrowLeft className="ml-2 h-4 w-4" />
              تسجيل الدخول
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            نظام إدارة الموظفين الذكي
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            منصة شاملة لإدارة الموظفين والأقسام بكفاءة عالية مع أدوات التوزيع الذكي والتحليلات المتقدمة
          </p>
          <Button 
            size="lg"
            onClick={() => router.push("/auth/signin")}
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg"
          >
            ابدأ الآن
            <ArrowLeft className="ml-2 h-5 w-5" />
          </Button>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <Card className="border-green-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-right">إدارة الموظفين</CardTitle>
              <CardDescription className="text-right">
                إدارة شاملة لبيانات الموظفين مع إمكانيات البحث والتصفية المتقدمة
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-green-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-right">إدارة الأقسام</CardTitle>
              <CardDescription className="text-right">
                تنظيم الأقسام وإدارة السعات مع التوزيع الذكي للموظفين
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-green-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-right">التحليلات والتقارير</CardTitle>
              <CardDescription className="text-right">
                تقارير مفصلة وإحصائيات في الوقت الفعلي لاتخاذ قرارات مدروسة
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Features List */}
        <div className="bg-white rounded-lg shadow-sm border border-green-200 p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            المميزات الرئيسية
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">إدارة الموظفين والأقسام</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">التوزيع الذكي للموظفين</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">استيراد وتصدير البيانات</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">البحث والتصفية المتقدمة</span>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">تقارير وإحصائيات مفصلة</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">واجهة عربية بالكامل</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">أمان وحماية البيانات</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">سهولة الاستخدام</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-green-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 space-x-reverse mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-semibold text-gray-900">HR Synergy</span>
            </div>
            <p className="text-gray-600">
              © 2024 HR Synergy. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

import { Employee, <PERSON> } from '@/lib/types'

export interface VirtualizationConfig {
  itemHeight: number
  containerHeight: number
  overscan: number
  threshold: number
}

export interface LazyLoadConfig {
  pageSize: number
  preloadPages: number
  cacheSize: number
}

export interface OptimisticUpdate<T> {
  id: string
  type: 'create' | 'update' | 'delete'
  data: T
  timestamp: Date
  rollback: () => void
}

export interface PerformanceMetrics {
  renderTime: number
  listSize: number
  visibleItems: number
  memoryUsage: number
  cacheHitRate: number
  optimisticUpdates: number
}

export class PerformanceService {
  private static instance: PerformanceService
  private cache: Map<string, any> = new Map()
  private optimisticUpdates: Map<string, OptimisticUpdate<any>> = new Map()
  private metrics: PerformanceMetrics = {
    renderTime: 0,
    listSize: 0,
    visibleItems: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    optimisticUpdates: 0
  }

  private constructor() {}

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService()
    }
    return PerformanceService.instance
  }

  /**
   * Get default virtualization config
   */
  getVirtualizationConfig(): VirtualizationConfig {
    return {
      itemHeight: 80, // Height of each list item in pixels
      containerHeight: 600, // Height of the container
      overscan: 5, // Number of items to render outside visible area
      threshold: 100 // Minimum items to enable virtualization
    }
  }

  /**
   * Get default lazy loading config
   */
  getLazyLoadConfig(): LazyLoadConfig {
    return {
      pageSize: 50, // Items per page
      preloadPages: 2, // Pages to preload ahead
      cacheSize: 500 // Maximum items in cache
    }
  }

  /**
   * Check if virtualization should be enabled
   */
  shouldVirtualize(itemCount: number): boolean {
    const config = this.getVirtualizationConfig()
    return itemCount >= config.threshold
  }

  /**
   * Paginate data for lazy loading
   */
  paginateData<T>(data: T[], page: number, pageSize: number): T[] {
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    return data.slice(startIndex, endIndex)
  }

  /**
   * Create optimistic update
   */
  createOptimisticUpdate<T>(
    id: string,
    type: 'create' | 'update' | 'delete',
    data: T,
    rollback: () => void
  ): void {
    const update: OptimisticUpdate<T> = {
      id,
      type,
      data,
      timestamp: new Date(),
      rollback
    }

    this.optimisticUpdates.set(id, update)
    this.metrics.optimisticUpdates = this.optimisticUpdates.size
  }

  /**
   * Confirm optimistic update
   */
  confirmOptimisticUpdate(id: string): void {
    this.optimisticUpdates.delete(id)
    this.metrics.optimisticUpdates = this.optimisticUpdates.size
  }

  /**
   * Rollback optimistic update
   */
  rollbackOptimisticUpdate(id: string): void {
    const update = this.optimisticUpdates.get(id)
    if (update) {
      update.rollback()
      this.optimisticUpdates.delete(id)
      this.metrics.optimisticUpdates = this.optimisticUpdates.size
    }
  }

  /**
   * Rollback all optimistic updates
   */
  rollbackAllOptimisticUpdates(): void {
    this.optimisticUpdates.forEach(update => update.rollback())
    this.optimisticUpdates.clear()
    this.metrics.optimisticUpdates = 0
  }

  /**
   * Cache data with expiration
   */
  cacheData(key: string, data: any, ttl: number = 300000): void { // 5 minutes default TTL
    const cacheEntry = {
      data,
      timestamp: Date.now(),
      ttl
    }
    
    this.cache.set(key, cacheEntry)
    
    // Clean up expired entries
    this.cleanupCache()
  }

  /**
   * Get cached data
   */
  getCachedData(key: string): any | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now()
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * Debounce function calls
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  /**
   * Throttle function calls
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0
    
    return (...args: Parameters<T>) => {
      const now = Date.now()
      
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }

  /**
   * Measure render performance
   */
  measureRenderTime<T>(renderFunction: () => T): T {
    const startTime = performance.now()
    const result = renderFunction()
    const endTime = performance.now()
    
    this.metrics.renderTime = endTime - startTime
    return result
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics {
    // Update memory usage (rough estimate)
    this.metrics.memoryUsage = this.cache.size * 1024 // Rough estimate in bytes
    
    // Calculate cache hit rate
    const totalRequests = this.cache.size + 100 // Rough estimate
    this.metrics.cacheHitRate = totalRequests > 0 ? (this.cache.size / totalRequests) * 100 : 0
    
    return { ...this.metrics }
  }

  /**
   * Update list metrics
   */
  updateListMetrics(listSize: number, visibleItems: number): void {
    this.metrics.listSize = listSize
    this.metrics.visibleItems = visibleItems
  }

  /**
   * Optimize employee search
   */
  optimizeEmployeeSearch(
    employees: Employee[],
    searchQuery: string,
    filters: Record<string, any> = {}
  ): Employee[] {
    const cacheKey = `search_${searchQuery}_${JSON.stringify(filters)}`
    
    // Check cache first
    const cached = this.getCachedData(cacheKey)
    if (cached) {
      return cached
    }
    
    // Perform search
    const results = this.measureRenderTime(() => {
      let filtered = employees
      
      // Apply search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filtered = filtered.filter(emp =>
          emp.name.toLowerCase().includes(query) ||
          emp.email.toLowerCase().includes(query) ||
          emp.id.toLowerCase().includes(query)
        )
      }
      
      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          filtered = filtered.filter(emp => {
            const empValue = (emp as any)[key]
            return empValue === value
          })
        }
      })
      
      return filtered
    })
    
    // Cache results
    this.cacheData(cacheKey, results, 60000) // 1 minute TTL for search results
    
    return results
  }

  /**
   * Optimize department statistics calculation
   */
  optimizeDepartmentStats(
    departments: Department[],
    employees: Employee[]
  ): Array<Department & { employeeCount: number; utilization: number }> {
    const cacheKey = `dept_stats_${departments.length}_${employees.length}`
    
    // Check cache first
    const cached = this.getCachedData(cacheKey)
    if (cached) {
      return cached
    }
    
    // Calculate stats
    const results = this.measureRenderTime(() => {
      return departments.map(dept => {
        const employeeCount = employees.filter(emp => 
          emp.departmentId === dept.id && emp.status === 'ACTIVE'
        ).length
        
        const utilization = dept.capacity > 0 ? (employeeCount / dept.capacity) * 100 : 0
        
        return {
          ...dept,
          employeeCount,
          utilization
        }
      })
    })
    
    // Cache results
    this.cacheData(cacheKey, results, 120000) // 2 minutes TTL
    
    return results
  }

  /**
   * Preload data for better UX
   */
  async preloadData(
    dataLoader: () => Promise<any>,
    cacheKey: string,
    priority: 'high' | 'low' = 'low'
  ): Promise<void> {
    // Check if already cached
    if (this.getCachedData(cacheKey)) {
      return
    }
    
    // Use requestIdleCallback for low priority preloading
    const loadData = async () => {
      try {
        const data = await dataLoader()
        this.cacheData(cacheKey, data)
      } catch (error) {
        console.warn('Preload failed:', error)
      }
    }
    
    if (priority === 'high') {
      await loadData()
    } else {
      // Use requestIdleCallback if available, otherwise setTimeout
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(loadData)
      } else {
        setTimeout(loadData, 100)
      }
    }
  }

  /**
   * Batch DOM updates
   */
  batchDOMUpdates(updates: (() => void)[]): void {
    // Use requestAnimationFrame to batch DOM updates
    requestAnimationFrame(() => {
      updates.forEach(update => update())
    })
  }

  /**
   * Memory cleanup
   */
  cleanup(): void {
    this.clearCache()
    this.rollbackAllOptimisticUpdates()
    
    // Reset metrics
    this.metrics = {
      renderTime: 0,
      listSize: 0,
      visibleItems: 0,
      memoryUsage: 0,
      cacheHitRate: 0,
      optimisticUpdates: 0
    }
  }
}

// Export singleton instance
export const performanceService = PerformanceService.getInstance()

// Export utility functions
export const debounce = <T extends (...args: any[]) => any>(func: T, delay: number) =>
  performanceService.debounce(func, delay)

export const throttle = <T extends (...args: any[]) => any>(func: T, delay: number) =>
  performanceService.throttle(func, delay)

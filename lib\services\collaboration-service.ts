import * as Y from 'yjs'
import { WebsocketProvider } from 'y-websocket'
import { IndexeddbPersistence } from 'y-indexeddb'
import { Employee, Department, User } from '@/lib/types'
import { toast } from 'sonner'

export interface CollaborationEvent {
  type: 'employee_added' | 'employee_updated' | 'employee_deleted' | 'department_added' | 'department_updated' | 'department_deleted'
  data: any
  userId: string
  timestamp: Date
  deviceId: string
}

export interface ActiveUser {
  id: string
  name: string
  email: string
  role: string
  isOnline: boolean
  lastSeen: Date
  currentPage?: string
  deviceId: string
}

export interface CollaborationStatus {
  isConnected: boolean
  activeUsers: ActiveUser[]
  documentState: 'synced' | 'syncing' | 'conflict' | 'offline'
  lastSync: Date | null
  pendingChanges: number
}

export class CollaborationService {
  private static instance: CollaborationService
  private ydoc: Y.Doc
  private wsProvider: WebsocketProvider | null = null
  private indexeddbProvider: IndexeddbPersistence | null = null
  private employeesMap: Y.Map<any>
  private departmentsMap: Y.Map<any>
  private usersMap: Y.Map<any>
  private eventsArray: Y.Array<any>
  private currentUser: User | null = null
  private deviceId: string
  private eventListeners: Map<string, Function[]> = new Map()

  private constructor() {
    this.ydoc = new Y.Doc()
    this.employeesMap = this.ydoc.getMap('employees')
    this.departmentsMap = this.ydoc.getMap('departments')
    this.usersMap = this.ydoc.getMap('users')
    this.eventsArray = this.ydoc.getArray('events')
    this.deviceId = this.generateDeviceId()
    
    this.setupEventListeners()
  }

  static getInstance(): CollaborationService {
    if (!CollaborationService.instance) {
      CollaborationService.instance = new CollaborationService()
    }
    return CollaborationService.instance
  }

  /**
   * Initialize collaboration with user and WebSocket connection
   */
  async initialize(user: User, wsUrl: string = 'ws://localhost:1234'): Promise<void> {
    try {
      this.currentUser = user

      // Set up IndexedDB persistence for offline support
      this.indexeddbProvider = new IndexeddbPersistence('hr-synergy-collab', this.ydoc)
      
      // Set up WebSocket provider for real-time sync
      this.wsProvider = new WebsocketProvider(wsUrl, 'hr-synergy-room', this.ydoc)
      
      // Add current user to active users
      this.usersMap.set(user.id, {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        isOnline: true,
        lastSeen: new Date(),
        deviceId: this.deviceId
      })

      // Set up connection event listeners
      this.wsProvider.on('status', (event: any) => {
        console.log('WebSocket status:', event.status)
        this.emit('connection_status', { 
          connected: event.status === 'connected',
          status: event.status 
        })
        
        if (event.status === 'connected') {
          toast.success('متصل - المزامنة الفورية نشطة')
        } else if (event.status === 'disconnected') {
          toast.warning('انقطع الاتصال - سيتم العمل في وضع عدم الاتصال')
        }
      })

      // Set up awareness for user presence
      this.wsProvider.awareness.setLocalStateField('user', {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        deviceId: this.deviceId
      })

      this.wsProvider.awareness.on('change', () => {
        this.updateActiveUsers()
      })

      console.log('Collaboration service initialized for user:', user.id)
    } catch (error) {
      console.error('Failed to initialize collaboration service:', error)
      throw new Error('فشل في تهيئة خدمة التعاون')
    }
  }

  /**
   * Add or update employee in shared document
   */
  async addEmployee(employee: Employee): Promise<void> {
    try {
      this.employeesMap.set(employee.id, {
        ...employee,
        _lastModified: new Date(),
        _modifiedBy: this.currentUser?.id,
        _deviceId: this.deviceId
      })

      this.addEvent({
        type: 'employee_added',
        data: employee,
        userId: this.currentUser?.id || '',
        timestamp: new Date(),
        deviceId: this.deviceId
      })

      console.log('Employee added to shared document:', employee.id)
    } catch (error) {
      console.error('Failed to add employee to shared document:', error)
      throw new Error('فشل في إضافة الموظف للمستند المشترك')
    }
  }

  /**
   * Update employee in shared document
   */
  async updateEmployee(employee: Employee): Promise<void> {
    try {
      const existing = this.employeesMap.get(employee.id)
      if (!existing) {
        throw new Error('الموظف غير موجود في المستند المشترك')
      }

      this.employeesMap.set(employee.id, {
        ...employee,
        _lastModified: new Date(),
        _modifiedBy: this.currentUser?.id,
        _deviceId: this.deviceId
      })

      this.addEvent({
        type: 'employee_updated',
        data: employee,
        userId: this.currentUser?.id || '',
        timestamp: new Date(),
        deviceId: this.deviceId
      })

      console.log('Employee updated in shared document:', employee.id)
    } catch (error) {
      console.error('Failed to update employee in shared document:', error)
      throw new Error('فشل في تحديث الموظف في المستند المشترك')
    }
  }

  /**
   * Remove employee from shared document
   */
  async removeEmployee(employeeId: string): Promise<void> {
    try {
      const employee = this.employeesMap.get(employeeId)
      if (!employee) {
        throw new Error('الموظف غير موجود في المستند المشترك')
      }

      this.employeesMap.delete(employeeId)

      this.addEvent({
        type: 'employee_deleted',
        data: { id: employeeId },
        userId: this.currentUser?.id || '',
        timestamp: new Date(),
        deviceId: this.deviceId
      })

      console.log('Employee removed from shared document:', employeeId)
    } catch (error) {
      console.error('Failed to remove employee from shared document:', error)
      throw new Error('فشل في حذف الموظف من المستند المشترك')
    }
  }

  /**
   * Add or update department in shared document
   */
  async addDepartment(department: Department): Promise<void> {
    try {
      this.departmentsMap.set(department.id, {
        ...department,
        _lastModified: new Date(),
        _modifiedBy: this.currentUser?.id,
        _deviceId: this.deviceId
      })

      this.addEvent({
        type: 'department_added',
        data: department,
        userId: this.currentUser?.id || '',
        timestamp: new Date(),
        deviceId: this.deviceId
      })

      console.log('Department added to shared document:', department.id)
    } catch (error) {
      console.error('Failed to add department to shared document:', error)
      throw new Error('فشل في إضافة القسم للمستند المشترك')
    }
  }

  /**
   * Update department in shared document
   */
  async updateDepartment(department: Department): Promise<void> {
    try {
      const existing = this.departmentsMap.get(department.id)
      if (!existing) {
        throw new Error('القسم غير موجود في المستند المشترك')
      }

      this.departmentsMap.set(department.id, {
        ...department,
        _lastModified: new Date(),
        _modifiedBy: this.currentUser?.id,
        _deviceId: this.deviceId
      })

      this.addEvent({
        type: 'department_updated',
        data: department,
        userId: this.currentUser?.id || '',
        timestamp: new Date(),
        deviceId: this.deviceId
      })

      console.log('Department updated in shared document:', department.id)
    } catch (error) {
      console.error('Failed to update department in shared document:', error)
      throw new Error('فشل في تحديث القسم في المستند المشترك')
    }
  }

  /**
   * Get all employees from shared document
   */
  getEmployees(): Employee[] {
    const employees: Employee[] = []
    
    this.employeesMap.forEach((value, key) => {
      if (value && typeof value === 'object') {
        const { _lastModified, _modifiedBy, _deviceId, ...employee } = value
        employees.push(employee as Employee)
      }
    })
    
    return employees
  }

  /**
   * Get all departments from shared document
   */
  getDepartments(): Department[] {
    const departments: Department[] = []
    
    this.departmentsMap.forEach((value, key) => {
      if (value && typeof value === 'object') {
        const { _lastModified, _modifiedBy, _deviceId, ...department } = value
        departments.push(department as Department)
      }
    })
    
    return departments
  }

  /**
   * Get collaboration status
   */
  getStatus(): CollaborationStatus {
    const activeUsers = this.getActiveUsers()
    const isConnected = this.wsProvider?.wsconnected || false
    
    return {
      isConnected,
      activeUsers,
      documentState: isConnected ? 'synced' : 'offline',
      lastSync: new Date(), // In a real implementation, track actual sync time
      pendingChanges: 0 // In a real implementation, track pending changes
    }
  }

  /**
   * Get active users
   */
  getActiveUsers(): ActiveUser[] {
    const users: ActiveUser[] = []
    
    if (this.wsProvider?.awareness) {
      this.wsProvider.awareness.getStates().forEach((state, clientId) => {
        if (state.user) {
          users.push({
            ...state.user,
            isOnline: true,
            lastSeen: new Date()
          })
        }
      })
    }
    
    return users
  }

  /**
   * Set current page for user presence
   */
  setCurrentPage(page: string): void {
    if (this.wsProvider?.awareness) {
      this.wsProvider.awareness.setLocalStateField('currentPage', page)
    }
  }

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  /**
   * Setup internal event listeners
   */
  private setupEventListeners(): void {
    // Listen for employee changes
    this.employeesMap.observe((event) => {
      event.changes.keys.forEach((change, key) => {
        if (change.action === 'add' || change.action === 'update') {
          const employee = this.employeesMap.get(key)
          if (employee && employee._deviceId !== this.deviceId) {
            this.emit('employee_changed', { action: change.action, employee })
          }
        } else if (change.action === 'delete') {
          this.emit('employee_changed', { action: 'delete', employeeId: key })
        }
      })
    })

    // Listen for department changes
    this.departmentsMap.observe((event) => {
      event.changes.keys.forEach((change, key) => {
        if (change.action === 'add' || change.action === 'update') {
          const department = this.departmentsMap.get(key)
          if (department && department._deviceId !== this.deviceId) {
            this.emit('department_changed', { action: change.action, department })
          }
        } else if (change.action === 'delete') {
          this.emit('department_changed', { action: 'delete', departmentId: key })
        }
      })
    })
  }

  /**
   * Add event to events array
   */
  private addEvent(event: CollaborationEvent): void {
    this.eventsArray.push([event])
  }

  /**
   * Update active users list
   */
  private updateActiveUsers(): void {
    this.emit('users_changed', this.getActiveUsers())
  }

  /**
   * Generate unique device ID
   */
  private generateDeviceId(): string {
    const stored = localStorage.getItem('hr_synergy_collab_device_id')
    if (stored) return stored
    
    const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem('hr_synergy_collab_device_id', deviceId)
    return deviceId
  }

  /**
   * Disconnect from collaboration
   */
  disconnect(): void {
    if (this.wsProvider) {
      this.wsProvider.disconnect()
      this.wsProvider = null
    }
    
    if (this.indexeddbProvider) {
      this.indexeddbProvider.destroy()
      this.indexeddbProvider = null
    }
    
    console.log('Disconnected from collaboration service')
  }
}

// Export singleton instance
export const collaborationService = CollaborationService.getInstance()

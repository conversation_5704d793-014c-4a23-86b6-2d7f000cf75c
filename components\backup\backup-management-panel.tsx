"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  cloudBackupService, 
  BackupMetadata, 
  BackupProgress, 
  RestoreProgress,
  CloudBackupConfig 
} from '@/lib/services/cloud-backup-service'
import { 
  Cloud, 
  Download, 
  Upload,
  Settings,
  Trash2,
  <PERSON>fresh<PERSON><PERSON>,
  Shield,
  Clock,
  HardDrive,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Database
} from 'lucide-react'
import { toast } from 'sonner'

export function BackupManagementPanel() {
  const { data: session } = useSession()
  const [backups, setBackups] = useState<BackupMetadata[]>([])
  const [isConfigured, setIsConfigured] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [backupProgress, setBackupProgress] = useState<BackupProgress | null>(null)
  const [restoreProgress, setRestoreProgress] = useState<RestoreProgress | null>(null)
  const [autoBackupEnabled, setAutoBackupEnabled] = useState(false)
  const [showConfigDialog, setShowConfigDialog] = useState(false)
  const [backupDescription, setBackupDescription] = useState('')

  const [config, setConfig] = useState<CloudBackupConfig>({
    accessKeyId: '',
    secretAccessKey: '',
    region: 'us-east-1',
    bucketName: '',
    encryptionKey: ''
  })

  useEffect(() => {
    // Check if backup service is configured
    const savedConfig = localStorage.getItem('hr_synergy_backup_config')
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig)
        setConfig(parsedConfig)
        initializeBackupService(parsedConfig)
      } catch (error) {
        console.error('Failed to load backup config:', error)
      }
    }
  }, [])

  const initializeBackupService = async (backupConfig: CloudBackupConfig) => {
    try {
      await cloudBackupService.initialize(backupConfig)
      setIsConfigured(true)
      loadBackups()
    } catch (error) {
      console.error('Failed to initialize backup service:', error)
      toast.error('فشل في تهيئة خدمة النسخ الاحتياطي')
    }
  }

  const loadBackups = async () => {
    if (!session?.user?.id || !isConfigured) return

    setIsLoading(true)
    try {
      const backupList = await cloudBackupService.listBackups(session.user.id)
      setBackups(backupList)
    } catch (error) {
      console.error('Failed to load backups:', error)
      toast.error('فشل في تحميل قائمة النسخ الاحتياطية')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveConfig = async () => {
    try {
      await initializeBackupService(config)
      localStorage.setItem('hr_synergy_backup_config', JSON.stringify(config))
      setShowConfigDialog(false)
      toast.success('تم حفظ إعدادات النسخ الاحتياطي')
    } catch (error) {
      toast.error('فشل في حفظ الإعدادات')
    }
  }

  const handleCreateBackup = async () => {
    if (!isConfigured) {
      toast.error('يرجى تكوين إعدادات النسخ الاحتياطي أولاً')
      return
    }

    try {
      setBackupProgress({
        stage: 'preparing',
        progress: 0,
        message: 'بدء إنشاء النسخة الاحتياطية...'
      })

      const metadata = await cloudBackupService.createBackup(
        backupDescription || undefined,
        setBackupProgress
      )

      setBackups(prev => [metadata, ...prev])
      setBackupDescription('')
      setBackupProgress(null)
    } catch (error) {
      setBackupProgress(null)
      console.error('Backup failed:', error)
    }
  }

  const handleRestoreBackup = async (backupId: string) => {
    if (!isConfigured) {
      toast.error('خدمة النسخ الاحتياطي غير مكونة')
      return
    }

    const confirmed = window.confirm(
      'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.'
    )

    if (!confirmed) return

    try {
      setRestoreProgress({
        stage: 'downloading',
        progress: 0,
        message: 'بدء استعادة النسخة الاحتياطية...'
      })

      await cloudBackupService.restoreFromBackup(backupId, setRestoreProgress)
      setRestoreProgress(null)
    } catch (error) {
      setRestoreProgress(null)
      console.error('Restore failed:', error)
    }
  }

  const handleDeleteBackup = async (backupId: string) => {
    if (!session?.user?.id) return

    const confirmed = window.confirm(
      'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ هذا الإجراء لا يمكن التراجع عنه.'
    )

    if (!confirmed) return

    try {
      await cloudBackupService.deleteBackup(backupId, session.user.id)
      setBackups(prev => prev.filter(backup => backup.id !== backupId))
    } catch (error) {
      console.error('Failed to delete backup:', error)
    }
  }

  const handleToggleAutoBackup = (enabled: boolean) => {
    if (enabled) {
      cloudBackupService.startAutoBackup(24) // 24 hours
      toast.success('تم تفعيل النسخ الاحتياطي التلقائي')
    } else {
      cloudBackupService.stopAutoBackup()
      toast.success('تم إيقاف النسخ الاحتياطي التلقائي')
    }
    setAutoBackupEnabled(enabled)
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleString('ar-SA')
  }

  return (
    <div className="container-spaced">
      {/* Configuration Status */}
      <Card className={`shadow-xl border-0 ${isConfigured ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Shield className={`h-6 w-6 ${isConfigured ? 'text-green-600' : 'text-yellow-600'}`} />
            حالة النسخ الاحتياطي السحابي
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? 'مكون' : 'غير مكون'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!isConfigured && (
            <div className="flex items-center gap-3 p-3 rounded-lg bg-yellow-100 border border-yellow-300">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-yellow-900">
                  يرجى تكوين إعدادات النسخ الاحتياطي السحابي
                </p>
                <p className="text-xs text-yellow-700">
                  تحتاج إلى إعداد AWS S3 لتفعيل النسخ الاحتياطي السحابي
                </p>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Dialog open={showConfigDialog} onOpenChange={setShowConfigDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" className="shadow-soft hover-lift border-2 border-border/50">
                  <Settings className="h-4 w-4 ml-2" />
                  إعدادات النسخ الاحتياطي
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>إعدادات النسخ الاحتياطي السحابي</DialogTitle>
                  <DialogDescription>
                    قم بتكوين AWS S3 للنسخ الاحتياطي السحابي
                  </DialogDescription>
                </DialogHeader>
                <div className="container-spaced">
                  <div className="container-spaced">
                    <Label htmlFor="accessKeyId">Access Key ID</Label>
                    <Input
                      id="accessKeyId"
                      value={config.accessKeyId}
                      onChange={(e) => setConfig({ ...config, accessKeyId: e.target.value })}
                      placeholder="AKIA..."
                    />
                  </div>

                  <div className="container-spaced">
                    <Label htmlFor="secretAccessKey">Secret Access Key</Label>
                    <Input
                      id="secretAccessKey"
                      type="password"
                      value={config.secretAccessKey}
                      onChange={(e) => setConfig({ ...config, secretAccessKey: e.target.value })}
                      placeholder="..."
                    />
                  </div>

                  <div className="container-spaced">
                    <Label htmlFor="region">المنطقة</Label>
                    <Input
                      id="region"
                      value={config.region}
                      onChange={(e) => setConfig({ ...config, region: e.target.value })}
                      placeholder="us-east-1"
                    />
                  </div>

                  <div className="container-spaced">
                    <Label htmlFor="bucketName">اسم الحاوية</Label>
                    <Input
                      id="bucketName"
                      value={config.bucketName}
                      onChange={(e) => setConfig({ ...config, bucketName: e.target.value })}
                      placeholder="hr-synergy-backups"
                    />
                  </div>

                  <Button onClick={handleSaveConfig} className="w-full gradient-primary">
                    حفظ الإعدادات
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {isConfigured && (
              <Button onClick={loadBackups} variant="outline" disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 ml-2 ${isLoading ? 'animate-spin' : ''}`} />
                تحديث القائمة
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Backup Controls */}
      {isConfigured && (
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
                <Cloud className="h-4 w-4 text-white" />
              </div>
              عمليات النسخ الاحتياطي
            </CardTitle>
          </CardHeader>
          <CardContent className="container-spaced">
            {/* Auto Backup Toggle */}
            <div className="flex items-center justify-between p-3 rounded-lg gradient-green-soft border border-primary/20">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-primary" />
                <div>
                  <Label htmlFor="auto-backup" className="text-sm font-medium">
                    النسخ الاحتياطي التلقائي
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    نسخة احتياطية كل 24 ساعة
                  </p>
                </div>
              </div>
              <Switch
                id="auto-backup"
                checked={autoBackupEnabled}
                onCheckedChange={handleToggleAutoBackup}
              />
            </div>

            {/* Manual Backup */}
            <div className="container-spaced">
              <Label htmlFor="backup-description">وصف النسخة الاحتياطية (اختياري)</Label>
              <Input
                id="backup-description"
                value={backupDescription}
                onChange={(e) => setBackupDescription(e.target.value)}
                placeholder="مثل: نسخة احتياطية قبل التحديث"
              />
            </div>

            {/* Backup Progress */}
            {backupProgress && (
              <div className="container-spaced">
                <div className="flex justify-between text-sm">
                  <span>{backupProgress.message}</span>
                  <span>{Math.round(backupProgress.progress)}%</span>
                </div>
                <Progress value={backupProgress.progress} className="h-2" />
                {backupProgress.bytesTransferred && backupProgress.totalBytes && (
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(backupProgress.bytesTransferred)} / {formatFileSize(backupProgress.totalBytes)}
                  </p>
                )}
              </div>
            )}

            {/* Restore Progress */}
            {restoreProgress && (
              <div className="container-spaced">
                <div className="flex justify-between text-sm">
                  <span>{restoreProgress.message}</span>
                  <span>{Math.round(restoreProgress.progress)}%</span>
                </div>
                <Progress value={restoreProgress.progress} className="h-2" />
              </div>
            )}

            <Button
              onClick={handleCreateBackup}
              disabled={!!backupProgress || !!restoreProgress}
              className="w-full gradient-primary hover:opacity-90 shadow-medium hover-lift"
            >
              <Upload className="h-4 w-4 ml-2" />
              {backupProgress ? 'جاري إنشاء النسخة الاحتياطية...' : 'إنشاء نسخة احتياطية'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Backup List */}
      {isConfigured && (
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 gradient-secondary rounded-lg flex items-center justify-center shadow-lg">
                <Database className="h-4 w-4 text-white" />
              </div>
              النسخ الاحتياطية ({backups.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {backups.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>الوصف</TableHead>
                    <TableHead>الحجم</TableHead>
                    <TableHead>البيانات</TableHead>
                    <TableHead>النوع</TableHead>
                    <TableHead className="w-32">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {backups.map((backup) => (
                    <TableRow key={backup.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{formatDate(backup.timestamp)}</p>
                          <p className="text-xs text-muted-foreground">{backup.id}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {backup.description || 'بدون وصف'}
                      </TableCell>
                      <TableCell>
                        {formatFileSize(backup.size)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>{backup.employeeCount} موظف</p>
                          <p>{backup.departmentCount} قسم</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={backup.isAutomatic ? "secondary" : "default"}>
                          {backup.isAutomatic ? 'تلقائي' : 'يدوي'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRestoreBackup(backup.id)}
                            disabled={!!restoreProgress}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteBackup(backup.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-12">
                <HardDrive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium text-muted-foreground">
                  لا توجد نسخ احتياطية
                </p>
                <p className="text-sm text-muted-foreground">
                  قم بإنشاء أول نسخة احتياطية لحماية بياناتك
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

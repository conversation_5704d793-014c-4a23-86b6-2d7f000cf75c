"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/employees/page",{

/***/ "(app-pages-browser)/./components/employees/add-employee-dialog.tsx":
/*!******************************************************!*\
  !*** ./components/employees/add-employee-dialog.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddEmployeeDialog: () => (/* binding */ AddEmployeeDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store/hr-store */ \"(app-pages-browser)/./lib/store/hr-store.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ AddEmployeeDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AddEmployeeDialog(param) {\n    let { open, onOpenChange, departments } = param;\n    _s();\n    const { createEmployee } = (0,_lib_store_hr_store__WEBPACK_IMPORTED_MODULE_2__.useHRStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        departmentId: ''\n    });\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const success = await createEmployee({\n                name: formData.name,\n                email: formData.email,\n                departmentId: formData.departmentId || null,\n                hireDate: new Date(),\n                userId: 'current-user' // This should come from session\n            });\n            if (success) {\n                // Reset form\n                setFormData({\n                    name: '',\n                    email: '',\n                    departmentId: ''\n                });\n                onOpenChange(false);\n            }\n        } catch (error) {\n            console.error('Failed to add employee:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n            className: \"sm:max-w-[425px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                            children: \"Add New Employee\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                            children: \"Create a new employee record. They will be assigned to the selected department.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"name\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"name\",\n                                            value: formData.name,\n                                            onChange: (e_0)=>handleInputChange('name', e_0.target.value),\n                                            placeholder: \"Enter employee name\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"email\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"email\",\n                                            type: \"email\",\n                                            value: formData.email,\n                                            onChange: (e_1)=>handleInputChange('email', e_1.target.value),\n                                            placeholder: \"Enter email address\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"department\",\n                                            children: \"Department\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: formData.departmentId,\n                                            onValueChange: (value_0)=>handleInputChange('departmentId', value_0),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                        placeholder: \"Select department\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"unassigned\",\n                                                            children: \"Unassigned (Free Bucket)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: dept.id,\n                                                                children: [\n                                                                    dept.name,\n                                                                    \" (\",\n                                                                    dept.code,\n                                                                    \")\"\n                                                                ]\n                                                            }, dept.id, true, {\n                                                                fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                                                lineNumber: 85,\n                                                                columnNumber: 44\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    disabled: isLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    children: isLoading ? 'Adding...' : 'Add Employee'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Augment code\\\\components\\\\employees\\\\add-employee-dialog.tsx\",\n        lineNumber: 56,\n        columnNumber: 10\n    }, this);\n}\n_s(AddEmployeeDialog, \"xcBTxrpDmQYyefbow4zULyoN+cg=\", false, function() {\n    return [\n        _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_2__.useHRStore\n    ];\n});\n_c = AddEmployeeDialog;\nvar _c;\n$RefreshReg$(_c, \"AddEmployeeDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/employees/add-employee-dialog.tsx\n"));

/***/ })

});
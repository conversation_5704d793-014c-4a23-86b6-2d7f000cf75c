{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "7f8b8985c98be8010cf1c8123c629304", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6af188c52a6ed86eea302301697bbe6bf8ab53a2c51ee62a9ac3b56a825f013d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "45fd80922940561f37b4c3c7d0bac0116fc8597c01cc279f82dcefa39ede5906"}}}, "functions": {}, "sortedMiddleware": ["/"]}
{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "52338f981032443678dc4397b825ffd7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7cd5b89005052699914edea7e8d91ef613222a428c0ef3fbe102f7e64d9e7f5e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8de5dea4434825cbaf2e8f4b049554d42e9658256eb4a678f2336e6e6eef94af"}}}, "functions": {}, "sortedMiddleware": ["/"]}
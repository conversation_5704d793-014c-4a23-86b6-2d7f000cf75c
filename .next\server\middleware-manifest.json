{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "c7245f6a8a6ea1bf6e1c45b60088e6ed", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5da7e7b69d83deb459e3baf54865626c283ecb38f83339474a7bf2c118b7906f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "abeac254d00fcb4fe82f18945792a3f663205befbd6ff7b438fc1c41c3978e78"}}}, "functions": {}, "sortedMiddleware": ["/"]}
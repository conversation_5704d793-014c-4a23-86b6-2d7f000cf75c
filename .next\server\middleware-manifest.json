{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8zQZR8ktc+UATJedC4TPrhgcHIGS8WYYb9iIANHxQZY=", "__NEXT_PREVIEW_MODE_ID": "010affc2c96ac75f3397353aa699e3dc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fd538e6e88ed2c774ee8e2e146de38e084454b162cd3e3604a1f40d989476873", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2c413b4deb461a4e2d14bf54a2f236ab466b537587a36be2d5688591209dc51e"}}}, "functions": {}, "sortedMiddleware": ["/"]}
{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "51a836cfed080006812f8e7fc4644cc0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71ef65a5e6f6e43022ca447706010ef3ec8c5d48fa1a12e7bfccff3f77aa0a0a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "343184498059e2badbc1e3cd618ee31482ba178274219702a09e6b3db8c697a0"}}}, "functions": {}, "sortedMiddleware": ["/"]}
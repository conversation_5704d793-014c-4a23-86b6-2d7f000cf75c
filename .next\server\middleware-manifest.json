{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "81c61a0a380b8e6ad65c42d7d257aa39", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c713ea3bfba015c9d0eb657691d5dfe9cd85be744706e8ab6f38650ec0c78c8a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2bf1ff40fa4a3f934f655cc89fdb1cb90d45a77f8edcf09841a827a5fc67c489"}}}, "functions": {}, "sortedMiddleware": ["/"]}
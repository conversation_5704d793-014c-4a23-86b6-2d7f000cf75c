{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "ae83b9b074e64593d6c9f56f721a5e3c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a4c5e6dd0a378f1ed413b46074e158ee45db0c2e9689ae4362a67060442f0697", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8d3e2c6dd44827f4bc7fed4b845c76a7279e8f77f50276e05eddf56af543667b"}}}, "functions": {}, "sortedMiddleware": ["/"]}
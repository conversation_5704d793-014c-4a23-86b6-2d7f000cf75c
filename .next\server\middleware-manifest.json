{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0hiwvJTK5RsP49jvdEmEltGP7bzsSS9/zwYpSWCxJPU=", "__NEXT_PREVIEW_MODE_ID": "51827020c31c912fc204b8ea798b0e39", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5eb615499afef076c5b41284ce4adbe13955c58850104c9b4634e73d99a7ed72", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f97a1412e50384d00fc6519562fb8f2e048dbce94f8bc038e3b21e0126db23a3"}}}, "functions": {}, "sortedMiddleware": ["/"]}
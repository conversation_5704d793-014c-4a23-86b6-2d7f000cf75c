{"authors": "y<PERSON><PERSON> david <PERSON> <https://github.com/3x14159265>", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": 0, "libraryMinorVersion": 0, "libraryName": "Mozilla's localForage", "typingsPackageName": "localforage", "projectName": "https://github.com/mozilla/localforage", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "Mixed", "globals": [], "declaredModules": ["localforage"], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "bba7a83e59e81283119744a07bd67d2e3de1118bd97c92e1f9e49b10a378de7d"}
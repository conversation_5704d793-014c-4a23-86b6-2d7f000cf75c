export function testPerf(tc: t.TestCase): Promise<void>;
export function testIdbUpdateAndMerge(tc: t.TestCase): Promise<void>;
export function testIdbConcurrentMerge(tc: t.TestCase): Promise<void>;
export function testMetaStorage(tc: t.TestCase): Promise<void>;
export function test<PERSON>ar<PERSON><PERSON><PERSON><PERSON>(tc: t.TestCase): Promise<void>;
import * as t from 'lib0/testing.js';
//# sourceMappingURL=y-indexeddb.tests.d.ts.map
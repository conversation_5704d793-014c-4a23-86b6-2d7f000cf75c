"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./lib/store/hr-store.ts":
/*!*******************************!*\
  !*** ./lib/store/hr-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHRStore: () => (/* binding */ useHRStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/services/employee-service */ \"(app-pages-browser)/./lib/services/employee-service.ts\");\n/* harmony import */ var _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/distribution-service */ \"(app-pages-browser)/./lib/services/distribution-service.ts\");\n/* harmony import */ var _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/id-generator */ \"(app-pages-browser)/./lib/utils/id-generator.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\nconst useHRStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        // Initial state\n        employees: [],\n        departments: [],\n        freeBucket: [],\n        selectedEmployees: [],\n        isLoading: false,\n        searchQuery: '',\n        // Data actions\n        setEmployees: (employees)=>set((state)=>({\n                    employees,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                })),\n        setDepartments: (departments)=>set({\n                departments\n            }),\n        addEmployee: (employee)=>set((state)=>({\n                    employees: [\n                        ...state.employees,\n                        employee\n                    ]\n                })),\n        updateEmployee: (id, updates)=>set((state)=>({\n                    employees: state.employees.map((emp)=>emp.id === id ? {\n                            ...emp,\n                            ...updates\n                        } : emp)\n                })),\n        removeEmployee: (id)=>set((state)=>({\n                    employees: state.employees.filter((emp)=>emp.id !== id),\n                    selectedEmployees: state.selectedEmployees.filter((empId)=>empId !== id)\n                })),\n        // Enhanced Employee Operations\n        createEmployee: async (data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.createEmployee(data, departments, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: [\n                                ...state.employees,\n                                result.data\n                            ],\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket,\n                                result.data\n                            ] : state.freeBucket\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم إنشاء الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في إنشاء الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في إنشاء الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateEmployeeData: async (id, data)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.updateEmployee(id, data, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم تحديث الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في تحديث الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في تحديث الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        transferEmployee: async (id, data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.transferEmployee(id, data, employees, departments);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket.filter((emp)=>emp.id !== id),\n                                result.data\n                            ] : state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم نقل الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في نقل الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في نقل الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        archiveEmployee: async (id, userId)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.archiveEmployee(id, userId, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم أرشفة الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في أرشفة الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في أرشفة الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Selection actions\n        toggleEmployeeSelection: (id)=>set((state)=>({\n                    selectedEmployees: state.selectedEmployees.includes(id) ? state.selectedEmployees.filter((empId)=>empId !== id) : [\n                        ...state.selectedEmployees,\n                        id\n                    ]\n                })),\n        selectAllEmployees: (ids)=>set({\n                selectedEmployees: ids\n            }),\n        clearSelection: ()=>set({\n                selectedEmployees: []\n            }),\n        // Bulk operations\n        executeBulkOperation: async (operation, userId)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.bulkOperation(operation, employees, departments, userId);\n                if (result.success && result.data) {\n                    // Update employees with the results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        result.data.forEach((updatedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === updatedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = updatedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null),\n                            selectedEmployees: []\n                        };\n                    });\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في العملية الجماعية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Search & filter\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        getFilteredEmployees: ()=>{\n            const { employees, searchQuery } = get();\n            if (!searchQuery) return employees;\n            return employees.filter((emp)=>emp.name.toLowerCase().includes(searchQuery.toLowerCase()) || emp.email.toLowerCase().includes(searchQuery.toLowerCase()) || emp.id.toLowerCase().includes(searchQuery.toLowerCase()));\n        },\n        // Free bucket operations\n        moveToFreeBucket: (employeeIds)=>set((state)=>{\n                const movedEmployees = state.employees.filter((emp)=>employeeIds.includes(emp.id)).map((emp)=>({\n                        ...emp,\n                        departmentId: null\n                    }));\n                return {\n                    employees: state.employees.map((emp)=>employeeIds.includes(emp.id) ? {\n                            ...emp,\n                            departmentId: null\n                        } : emp),\n                    freeBucket: [\n                        ...state.freeBucket,\n                        ...movedEmployees\n                    ]\n                };\n            }),\n        removeFromFreeBucket: (employeeIds)=>set((state)=>({\n                    freeBucket: state.freeBucket.filter((emp)=>!employeeIds.includes(emp.id)),\n                    employees: state.employees.filter((emp)=>!employeeIds.includes(emp.id))\n                })),\n        // Distribution\n        distributeEmployees: async (config, userId)=>{\n            const { departments, freeBucket } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.distributeEmployees(freeBucket, departments, config, userId);\n                if (result.success) {\n                    // Update employees with distributed results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        // Update overflow employees\n                        result.overflow.forEach((overflowEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === overflowEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = overflowEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('فشل في عملية التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في عملية التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        rebalanceEmployees: async (config, userId)=>{\n            const { departments, employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.rebalanceEmployees(departments, employees, config, userId);\n                if (result.success) {\n                    // Update employees with rebalanced results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('فشل في إعادة التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في إعادة التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Initialization\n        initializeIDGenerator: ()=>{\n            const { departments, employees } = get();\n            _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_2__.idGenerator.initializeCounters(departments, employees);\n        }\n    })));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/store/hr-store.ts\n"));

/***/ })

});
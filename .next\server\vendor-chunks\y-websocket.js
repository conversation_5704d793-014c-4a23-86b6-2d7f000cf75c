"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-websocket";
exports.ids = ["vendor-chunks/y-websocket"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-websocket/src/y-websocket.js":
/*!*****************************************************!*\
  !*** ./node_modules/y-websocket/src/y-websocket.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsocketProvider: () => (/* binding */ WebsocketProvider),\n/* harmony export */   messageAuth: () => (/* binding */ messageAuth),\n/* harmony export */   messageAwareness: () => (/* binding */ messageAwareness),\n/* harmony export */   messageQueryAwareness: () => (/* binding */ messageQueryAwareness),\n/* harmony export */   messageSync: () => (/* binding */ messageSync)\n/* harmony export */ });\n/* harmony import */ var lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lib0/broadcastchannel */ \"(ssr)/./node_modules/lib0/broadcastchannel.js\");\n/* harmony import */ var lib0_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/time */ \"(ssr)/./node_modules/lib0/time.js\");\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/./node_modules/lib0/decoding.js\");\n/* harmony import */ var y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! y-protocols/sync */ \"(ssr)/./node_modules/y-protocols/sync.js\");\n/* harmony import */ var y_protocols_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! y-protocols/auth */ \"(ssr)/./node_modules/y-protocols/auth.js\");\n/* harmony import */ var y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! y-protocols/awareness */ \"(ssr)/./node_modules/y-protocols/awareness.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lib0/math */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var lib0_url__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lib0/url */ \"(ssr)/./node_modules/lib0/url.js\");\n/* harmony import */ var lib0_environment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lib0/environment */ \"(ssr)/./node_modules/lib0/environment.js\");\n/**\n * @module provider/websocket\n */ /* eslint-env browser */  // eslint-disable-line\n\n\n\n\n\n\n\n\n\n\n\nconst messageSync = 0;\nconst messageQueryAwareness = 3;\nconst messageAwareness = 1;\nconst messageAuth = 2;\n/**\n *                       encoder,          decoder,          provider,          emitSynced, messageType\n * @type {Array<function(encoding.Encoder, decoding.Decoder, WebsocketProvider, boolean,    number):void>}\n */ const messageHandlers = [];\nmessageHandlers[messageSync] = (encoder, decoder, provider, emitSynced, _messageType)=>{\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageSync);\n    const syncMessageType = y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.readSyncMessage(decoder, encoder, provider.doc, provider);\n    if (emitSynced && syncMessageType === y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.messageYjsSyncStep2 && !provider.synced) {\n        provider.synced = true;\n    }\n};\nmessageHandlers[messageQueryAwareness] = (encoder, _decoder, provider, _emitSynced, _messageType)=>{\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageAwareness);\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint8Array(encoder, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.encodeAwarenessUpdate(provider.awareness, Array.from(provider.awareness.getStates().keys())));\n};\nmessageHandlers[messageAwareness] = (_encoder, decoder, provider, _emitSynced, _messageType)=>{\n    y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.applyAwarenessUpdate(provider.awareness, lib0_decoding__WEBPACK_IMPORTED_MODULE_4__.readVarUint8Array(decoder), provider);\n};\nmessageHandlers[messageAuth] = (_encoder, decoder, provider, _emitSynced, _messageType)=>{\n    y_protocols_auth__WEBPACK_IMPORTED_MODULE_1__.readAuthMessage(decoder, provider.doc, (_ydoc, reason)=>permissionDeniedHandler(provider, reason));\n};\n// @todo - this should depend on awareness.outdatedTime\nconst messageReconnectTimeout = 30000;\n/**\n * @param {WebsocketProvider} provider\n * @param {string} reason\n */ const permissionDeniedHandler = (provider, reason)=>console.warn(`Permission denied to access ${provider.url}.\\n${reason}`);\n/**\n * @param {WebsocketProvider} provider\n * @param {Uint8Array} buf\n * @param {boolean} emitSynced\n * @return {encoding.Encoder}\n */ const readMessage = (provider, buf, emitSynced)=>{\n    const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_4__.createDecoder(buf);\n    const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n    const messageType = lib0_decoding__WEBPACK_IMPORTED_MODULE_4__.readVarUint(decoder);\n    const messageHandler = provider.messageHandlers[messageType];\n    if (/** @type {any} */ messageHandler) {\n        messageHandler(encoder, decoder, provider, emitSynced, messageType);\n    } else {\n        console.error('Unable to compute message');\n    }\n    return encoder;\n};\n/**\n * @param {WebsocketProvider} provider\n */ const setupWS = (provider)=>{\n    if (provider.shouldConnect && provider.ws === null) {\n        const websocket = new provider._WS(provider.url);\n        websocket.binaryType = 'arraybuffer';\n        provider.ws = websocket;\n        provider.wsconnecting = true;\n        provider.wsconnected = false;\n        provider.synced = false;\n        websocket.onmessage = (event)=>{\n            provider.wsLastMessageReceived = lib0_time__WEBPACK_IMPORTED_MODULE_5__.getUnixTime();\n            const encoder = readMessage(provider, new Uint8Array(event.data), true);\n            if (lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.length(encoder) > 1) {\n                websocket.send(lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n            }\n        };\n        websocket.onerror = (event)=>{\n            provider.emit('connection-error', [\n                event,\n                provider\n            ]);\n        };\n        websocket.onclose = (event)=>{\n            provider.emit('connection-close', [\n                event,\n                provider\n            ]);\n            provider.ws = null;\n            provider.wsconnecting = false;\n            if (provider.wsconnected) {\n                provider.wsconnected = false;\n                provider.synced = false;\n                // update awareness (all users except local left)\n                y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.removeAwarenessStates(provider.awareness, Array.from(provider.awareness.getStates().keys()).filter((client)=>client !== provider.doc.clientID), provider);\n                provider.emit('status', [\n                    {\n                        status: 'disconnected'\n                    }\n                ]);\n            } else {\n                provider.wsUnsuccessfulReconnects++;\n            }\n            // Start with no reconnect timeout and increase timeout by\n            // using exponential backoff starting with 100ms\n            setTimeout(setupWS, lib0_math__WEBPACK_IMPORTED_MODULE_6__.min(lib0_math__WEBPACK_IMPORTED_MODULE_6__.pow(2, provider.wsUnsuccessfulReconnects) * 100, provider.maxBackoffTime), provider);\n        };\n        websocket.onopen = ()=>{\n            provider.wsLastMessageReceived = lib0_time__WEBPACK_IMPORTED_MODULE_5__.getUnixTime();\n            provider.wsconnecting = false;\n            provider.wsconnected = true;\n            provider.wsUnsuccessfulReconnects = 0;\n            provider.emit('status', [\n                {\n                    status: 'connected'\n                }\n            ]);\n            // always send sync step 1 when connected\n            const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n            lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageSync);\n            y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.writeSyncStep1(encoder, provider.doc);\n            websocket.send(lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n            // broadcast local awareness state\n            if (provider.awareness.getLocalState() !== null) {\n                const encoderAwarenessState = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n                lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoderAwarenessState, messageAwareness);\n                lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint8Array(encoderAwarenessState, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.encodeAwarenessUpdate(provider.awareness, [\n                    provider.doc.clientID\n                ]));\n                websocket.send(lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoderAwarenessState));\n            }\n        };\n        provider.emit('status', [\n            {\n                status: 'connecting'\n            }\n        ]);\n    }\n};\n/**\n * @param {WebsocketProvider} provider\n * @param {ArrayBuffer} buf\n */ const broadcastMessage = (provider, buf)=>{\n    const ws = provider.ws;\n    if (provider.wsconnected && ws && ws.readyState === ws.OPEN) {\n        ws.send(buf);\n    }\n    if (provider.bcconnected) {\n        lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(provider.bcChannel, buf, provider);\n    }\n};\n/**\n * Websocket Provider for Yjs. Creates a websocket connection to sync the shared document.\n * The document name is attached to the provided url. I.e. the following example\n * creates a websocket connection to http://localhost:1234/my-document-name\n *\n * @example\n *   import * as Y from 'yjs'\n *   import { WebsocketProvider } from 'y-websocket'\n *   const doc = new Y.Doc()\n *   const provider = new WebsocketProvider('http://localhost:1234', 'my-document-name', doc)\n *\n * @extends {Observable<string>}\n */ class WebsocketProvider extends lib0_observable__WEBPACK_IMPORTED_MODULE_8__.Observable {\n    /**\n   * @param {string} serverUrl\n   * @param {string} roomname\n   * @param {Y.Doc} doc\n   * @param {object} opts\n   * @param {boolean} [opts.connect]\n   * @param {awarenessProtocol.Awareness} [opts.awareness]\n   * @param {Object<string,string>} [opts.params]\n   * @param {typeof WebSocket} [opts.WebSocketPolyfill] Optionall provide a WebSocket polyfill\n   * @param {number} [opts.resyncInterval] Request server state every `resyncInterval` milliseconds\n   * @param {number} [opts.maxBackoffTime] Maximum amount of time to wait before trying to reconnect (we try to reconnect using exponential backoff)\n   * @param {boolean} [opts.disableBc] Disable cross-tab BroadcastChannel communication\n   */ constructor(serverUrl, roomname, doc, { connect = true, awareness = new y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.Awareness(doc), params = {}, WebSocketPolyfill = WebSocket, resyncInterval = -1, maxBackoffTime = 2500, disableBc = false } = {}){\n        super();\n        // ensure that url is always ends with /\n        while(serverUrl[serverUrl.length - 1] === '/'){\n            serverUrl = serverUrl.slice(0, serverUrl.length - 1);\n        }\n        const encodedParams = lib0_url__WEBPACK_IMPORTED_MODULE_9__.encodeQueryParams(params);\n        this.maxBackoffTime = maxBackoffTime;\n        this.bcChannel = serverUrl + '/' + roomname;\n        this.url = serverUrl + '/' + roomname + (encodedParams.length === 0 ? '' : '?' + encodedParams);\n        this.roomname = roomname;\n        this.doc = doc;\n        this._WS = WebSocketPolyfill;\n        this.awareness = awareness;\n        this.wsconnected = false;\n        this.wsconnecting = false;\n        this.bcconnected = false;\n        this.disableBc = disableBc;\n        this.wsUnsuccessfulReconnects = 0;\n        this.messageHandlers = messageHandlers.slice();\n        /**\n     * @type {boolean}\n     */ this._synced = false;\n        /**\n     * @type {WebSocket?}\n     */ this.ws = null;\n        this.wsLastMessageReceived = 0;\n        /**\n     * Whether to connect to other peers or not\n     * @type {boolean}\n     */ this.shouldConnect = connect;\n        /**\n     * @type {number}\n     */ this._resyncInterval = 0;\n        if (resyncInterval > 0) {\n            this._resyncInterval = /** @type {any} */ setInterval(()=>{\n                if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n                    // resend sync step 1\n                    const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n                    lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageSync);\n                    y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.writeSyncStep1(encoder, doc);\n                    this.ws.send(lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n                }\n            }, resyncInterval);\n        }\n        /**\n     * @param {ArrayBuffer} data\n     * @param {any} origin\n     */ this._bcSubscriber = (data, origin)=>{\n            if (origin !== this) {\n                const encoder = readMessage(this, new Uint8Array(data), false);\n                if (lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.length(encoder) > 1) {\n                    lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(this.bcChannel, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder), this);\n                }\n            }\n        };\n        /**\n     * Listens to Yjs updates and sends them to remote peers (ws and broadcastchannel)\n     * @param {Uint8Array} update\n     * @param {any} origin\n     */ this._updateHandler = (update, origin)=>{\n            if (origin !== this) {\n                const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n                lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageSync);\n                y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.writeUpdate(encoder, update);\n                broadcastMessage(this, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n            }\n        };\n        this.doc.on('update', this._updateHandler);\n        /**\n     * @param {any} changed\n     * @param {any} _origin\n     */ this._awarenessUpdateHandler = ({ added, updated, removed }, _origin)=>{\n            const changedClients = added.concat(updated).concat(removed);\n            const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n            lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageAwareness);\n            lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint8Array(encoder, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.encodeAwarenessUpdate(awareness, changedClients));\n            broadcastMessage(this, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n        };\n        this._exitHandler = ()=>{\n            y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.removeAwarenessStates(this.awareness, [\n                doc.clientID\n            ], 'app closed');\n        };\n        if (lib0_environment__WEBPACK_IMPORTED_MODULE_10__.isNode && typeof process !== 'undefined') {\n            process.on('exit', this._exitHandler);\n        }\n        awareness.on('update', this._awarenessUpdateHandler);\n        this._checkInterval = /** @type {any} */ setInterval(()=>{\n            if (this.wsconnected && messageReconnectTimeout < lib0_time__WEBPACK_IMPORTED_MODULE_5__.getUnixTime() - this.wsLastMessageReceived) {\n                // no message received in a long time - not even your own awareness\n                // updates (which are updated every 15 seconds)\n                /** @type {WebSocket} */ this.ws.close();\n            }\n        }, messageReconnectTimeout / 10);\n        if (connect) {\n            this.connect();\n        }\n    }\n    /**\n   * @type {boolean}\n   */ get synced() {\n        return this._synced;\n    }\n    set synced(state) {\n        if (this._synced !== state) {\n            this._synced = state;\n            this.emit('synced', [\n                state\n            ]);\n            this.emit('sync', [\n                state\n            ]);\n        }\n    }\n    destroy() {\n        if (this._resyncInterval !== 0) {\n            clearInterval(this._resyncInterval);\n        }\n        clearInterval(this._checkInterval);\n        this.disconnect();\n        if (lib0_environment__WEBPACK_IMPORTED_MODULE_10__.isNode && typeof process !== 'undefined') {\n            process.off('exit', this._exitHandler);\n        }\n        this.awareness.off('update', this._awarenessUpdateHandler);\n        this.doc.off('update', this._updateHandler);\n        super.destroy();\n    }\n    connectBc() {\n        if (this.disableBc) {\n            return;\n        }\n        if (!this.bcconnected) {\n            lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.subscribe(this.bcChannel, this._bcSubscriber);\n            this.bcconnected = true;\n        }\n        // send sync step1 to bc\n        // write sync step 1\n        const encoderSync = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoderSync, messageSync);\n        y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.writeSyncStep1(encoderSync, this.doc);\n        lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(this.bcChannel, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoderSync), this);\n        // broadcast local state\n        const encoderState = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoderState, messageSync);\n        y_protocols_sync__WEBPACK_IMPORTED_MODULE_0__.writeSyncStep2(encoderState, this.doc);\n        lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(this.bcChannel, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoderState), this);\n        // write queryAwareness\n        const encoderAwarenessQuery = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoderAwarenessQuery, messageQueryAwareness);\n        lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(this.bcChannel, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoderAwarenessQuery), this);\n        // broadcast local awareness state\n        const encoderAwarenessState = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoderAwarenessState, messageAwareness);\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint8Array(encoderAwarenessState, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.encodeAwarenessUpdate(this.awareness, [\n            this.doc.clientID\n        ]));\n        lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.publish(this.bcChannel, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoderAwarenessState), this);\n    }\n    disconnectBc() {\n        // broadcast message with local awareness state set to null (indicating disconnect)\n        const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.createEncoder();\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint(encoder, messageAwareness);\n        lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.writeVarUint8Array(encoder, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_2__.encodeAwarenessUpdate(this.awareness, [\n            this.doc.clientID\n        ], new Map()));\n        broadcastMessage(this, lib0_encoding__WEBPACK_IMPORTED_MODULE_3__.toUint8Array(encoder));\n        if (this.bcconnected) {\n            lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_7__.unsubscribe(this.bcChannel, this._bcSubscriber);\n            this.bcconnected = false;\n        }\n    }\n    disconnect() {\n        this.shouldConnect = false;\n        this.disconnectBc();\n        if (this.ws !== null) {\n            this.ws.close();\n        }\n    }\n    connect() {\n        this.shouldConnect = true;\n        if (!this.wsconnected && this.ws === null) {\n            setupWS(this);\n            this.connectBc();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-websocket/src/y-websocket.js\n");

/***/ })

};
;
"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { performanceService, PerformanceMetrics } from '@/lib/services/performance-service'
import { 
  Zap, 
  TrendingUp, 
  Database,
  Clock,
  Eye,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Activity,
  HardDrive,
  Cpu
} from 'lucide-react'

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>(performanceService.getMetrics())
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [history, setHistory] = useState<PerformanceMetrics[]>([])

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isMonitoring) {
      interval = setInterval(() => {
        const currentMetrics = performanceService.getMetrics()
        setMetrics(currentMetrics)
        
        // Keep last 20 measurements for history
        setHistory(prev => [currentMetrics, ...prev.slice(0, 19)])
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [isMonitoring])

  const handleToggleMonitoring = () => {
    setIsMonitoring(!isMonitoring)
    if (!isMonitoring) {
      setHistory([])
    }
  }

  const handleClearCache = () => {
    performanceService.clearCache()
    setMetrics(performanceService.getMetrics())
  }

  const handleCleanup = () => {
    performanceService.cleanup()
    setMetrics(performanceService.getMetrics())
    setHistory([])
  }

  const getPerformanceStatus = (renderTime: number) => {
    if (renderTime < 16) return { status: 'excellent', color: 'text-green-600', label: 'ممتاز' }
    if (renderTime < 33) return { status: 'good', color: 'text-blue-600', label: 'جيد' }
    if (renderTime < 50) return { status: 'fair', color: 'text-yellow-600', label: 'مقبول' }
    return { status: 'poor', color: 'text-red-600', label: 'ضعيف' }
  }

  const performanceStatus = getPerformanceStatus(metrics.renderTime)

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const averageRenderTime = history.length > 0 
    ? history.reduce((sum, m) => sum + m.renderTime, 0) / history.length
    : 0

  return (
    <div className="container-spaced">
      {/* Performance Overview */}
      <div className="grid-spaced md:grid-cols-4">
        <Card className="gradient-green-soft border-r-4 border-r-primary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">زمن العرض</CardTitle>
            <Clock className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${performanceStatus.color}`}>
              {metrics.renderTime.toFixed(1)}ms
            </div>
            <p className="text-xs text-muted-foreground">
              {performanceStatus.label} • الهدف: &lt;16ms
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-secondary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العناصر المرئية</CardTitle>
            <Eye className="h-4 w-4 text-secondary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-secondary">{metrics.visibleItems}</div>
            <p className="text-xs text-muted-foreground">
              من {metrics.listSize} إجمالي
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-accent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">استخدام الذاكرة</CardTitle>
            <HardDrive className="h-4 w-4 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-accent">
              {formatBytes(metrics.memoryUsage)}
            </div>
            <p className="text-xs text-muted-foreground">
              ذاكرة التخزين المؤقت
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-success">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل الإصابة</CardTitle>
            <Database className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">
              {metrics.cacheHitRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              كفاءة التخزين المؤقت
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Controls */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <Activity className="h-4 w-4 text-white" />
            </div>
            مراقبة الأداء
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          <div className="flex items-center justify-between p-3 rounded-lg gradient-green-soft border border-primary/20">
            <div className="flex items-center gap-3">
              <Activity className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm font-medium">المراقبة المباشرة</p>
                <p className="text-xs text-muted-foreground">
                  {isMonitoring ? 'جاري المراقبة...' : 'متوقف'}
                </p>
              </div>
            </div>
            <Button
              onClick={handleToggleMonitoring}
              variant={isMonitoring ? "destructive" : "default"}
              size="sm"
            >
              {isMonitoring ? 'إيقاف' : 'بدء'} المراقبة
            </Button>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={handleClearCache}
              variant="outline"
              className="flex-1 shadow-soft hover-lift border-2 border-border/50"
            >
              <Database className="h-4 w-4 ml-2" />
              مسح التخزين المؤقت
            </Button>

            <Button
              onClick={handleCleanup}
              variant="outline"
              className="flex-1 shadow-soft hover-lift border-2 border-border/50"
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تنظيف شامل
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Performance History */}
      {history.length > 0 && (
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              تاريخ الأداء
            </CardTitle>
          </CardHeader>
          <CardContent className="container-spaced">
            <div className="grid-spaced md:grid-cols-3">
              <div className="text-center p-3 rounded-lg gradient-green-soft border border-primary/20">
                <div className="text-lg font-bold text-primary">
                  {averageRenderTime.toFixed(1)}ms
                </div>
                <p className="text-sm text-muted-foreground">متوسط زمن العرض</p>
              </div>

              <div className="text-center p-3 rounded-lg gradient-green-soft border border-secondary/20">
                <div className="text-lg font-bold text-secondary">
                  {Math.max(...history.map(h => h.renderTime)).toFixed(1)}ms
                </div>
                <p className="text-sm text-muted-foreground">أقصى زمن عرض</p>
              </div>

              <div className="text-center p-3 rounded-lg gradient-green-soft border border-accent/20">
                <div className="text-lg font-bold text-accent">
                  {Math.min(...history.map(h => h.renderTime)).toFixed(1)}ms
                </div>
                <p className="text-sm text-muted-foreground">أقل زمن عرض</p>
              </div>
            </div>

            {/* Simple performance chart */}
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">زمن العرض (آخر {history.length} قياس)</h4>
              <div className="flex items-end gap-1 h-20">
                {history.slice(0, 20).reverse().map((metric, index) => {
                  const height = Math.max(5, (metric.renderTime / 100) * 80) // Scale to 80px max
                  const color = metric.renderTime < 16 ? 'bg-green-500' : 
                               metric.renderTime < 33 ? 'bg-blue-500' : 
                               metric.renderTime < 50 ? 'bg-yellow-500' : 'bg-red-500'
                  
                  return (
                    <div
                      key={index}
                      className={`${color} rounded-t transition-all duration-300`}
                      style={{ height: `${height}px`, width: '8px' }}
                      title={`${metric.renderTime.toFixed(1)}ms`}
                    />
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Optimistic Updates */}
      {metrics.optimisticUpdates > 0 && (
        <Card className="shadow-xl border-0 bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-yellow-900">
              <AlertTriangle className="h-5 w-5" />
              التحديثات المتفائلة النشطة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <p className="text-sm text-yellow-800">
                يوجد {metrics.optimisticUpdates} تحديث متفائل في انتظار التأكيد
              </p>
              <Badge variant="secondary">
                {metrics.optimisticUpdates}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Tips */}
      <Card className="shadow-xl border-0 bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-blue-900">
            <TrendingUp className="h-5 w-5" />
            نصائح لتحسين الأداء
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          <div className="grid-spaced md:grid-cols-2">
            <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-100 border border-blue-300">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-900">التقسيم الافتراضي</p>
                <p className="text-xs text-blue-700">
                  يتم تفعيله تلقائياً للقوائم الكبيرة (&gt;100 عنصر)
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-100 border border-blue-300">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-900">التخزين المؤقت الذكي</p>
                <p className="text-xs text-blue-700">
                  يحفظ نتائج البحث والحسابات المعقدة
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-100 border border-blue-300">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-900">التحديثات المتفائلة</p>
                <p className="text-xs text-blue-700">
                  تحديث فوري للواجهة مع إمكانية التراجع
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-100 border border-blue-300">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-900">التحميل الكسول</p>
                <p className="text-xs text-blue-700">
                  تحميل البيانات عند الحاجة فقط
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

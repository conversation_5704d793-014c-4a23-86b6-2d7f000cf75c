{"name": "@types/localforage", "version": "0.0.33", "description": "TypeScript definitions for Mozilla's localForage", "license": "MIT", "author": "y<PERSON><PERSON> david <PERSON> <https://github.com/3x14159265>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "bba7a83e59e81283119744a07bd67d2e3de1118bd97c92e1f9e49b10a378de7d"}
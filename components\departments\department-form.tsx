"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useHRStore } from '@/lib/store/hr-store'
import { Department } from '@/lib/types'
import { departmentService } from '@/lib/services/department-service'
import { 
  Building2, 
  Hash, 
  Users,
  Save,
  X,
  Lightbulb
} from 'lucide-react'
import { toast } from 'sonner'

const departmentSchema = z.object({
  name: z.string().min(2, 'اسم القسم يجب أن يكون على الأقل حرفين'),
  code: z.string().regex(/^[A-Z]{2,5}$/, 'رمز القسم يجب أن يكون من 2-5 أحرف كبيرة'),
  capacity: z.number().min(1, 'السعة يجب أن تكون على الأقل 1').max(1000, 'السعة لا يمكن أن تتجاوز 1000'),
})

type DepartmentFormData = z.infer<typeof departmentSchema>

interface DepartmentFormProps {
  department?: Department
  onSuccess?: () => void
  onCancel?: () => void
  mode?: 'create' | 'edit'
}

export function DepartmentForm({ 
  department, 
  onSuccess, 
  onCancel, 
  mode = 'create' 
}: DepartmentFormProps) {
  const { data: session } = useSession()
  const { departments, employees, addDepartment, updateDepartment, isLoading } = useHRStore()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [suggestedCode, setSuggestedCode] = useState<string>('')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: department?.name || '',
      code: department?.code || '',
      capacity: department?.capacity || 10
    }
  })

  const watchedName = watch('name')
  const watchedCode = watch('code')

  // Generate suggested code when name changes (only in create mode)
  useEffect(() => {
    if (mode === 'create' && watchedName && watchedName.length >= 2) {
      const suggested = departmentService.suggestDepartmentCode(watchedName, departments)
      setSuggestedCode(suggested)
    } else {
      setSuggestedCode('')
    }
  }, [watchedName, departments, mode])

  const applySuggestedCode = () => {
    if (suggestedCode) {
      setValue('code', suggestedCode)
      setSuggestedCode('')
    }
  }

  const onSubmit = async (data: DepartmentFormData) => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    setIsSubmitting(true)
    try {
      let result

      if (mode === 'create') {
        result = await departmentService.createDepartment({
          name: data.name,
          code: data.code,
          capacity: data.capacity,
          userId: session.user.id
        }, departments)
      } else if (department) {
        result = await departmentService.updateDepartment(
          department.id,
          {
            name: data.name,
            code: data.code,
            capacity: data.capacity,
            userId: session.user.id
          },
          departments,
          employees
        )
      }

      if (result?.success && result.data) {
        if (mode === 'create') {
          addDepartment(result.data)
        } else {
          updateDepartment(department!.id, result.data)
        }
        
        toast.success(mode === 'create' ? 'تم إنشاء القسم بنجاح' : 'تم تحديث القسم بنجاح')
        onSuccess?.()
      } else {
        // Handle validation errors
        if (result?.validationErrors) {
          Object.entries(result.validationErrors).forEach(([field, message]) => {
            toast.error(`${field}: ${message}`)
          })
        } else {
          toast.error(result?.error || 'حدث خطأ أثناء حفظ البيانات')
        }
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get current employee count for this department (in edit mode)
  const currentEmployeeCount = department 
    ? employees.filter(emp => emp.departmentId === department.id && emp.status === 'ACTIVE').length
    : 0

  return (
    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
            <Building2 className="h-4 w-4 text-white" />
          </div>
          {mode === 'create' ? 'إضافة قسم جديد' : 'تعديل بيانات القسم'}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="container-spaced">
          {/* Current Employee Count (for edit mode) */}
          {mode === 'edit' && department && (
            <Card className="gradient-green-soft border-primary/20">
              <CardContent className="pt-4">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-primary">
                      الموظفون الحاليون
                    </p>
                    <p className="text-lg font-bold text-primary">
                      {currentEmployeeCount} من {department.capacity}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Name Field */}
          <div className="container-spaced">
            <Label htmlFor="name" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              اسم القسم
            </Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="أدخل اسم القسم"
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          {/* Code Field */}
          <div className="container-spaced">
            <Label htmlFor="code" className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              رمز القسم
            </Label>
            <div className="flex gap-2">
              <Input
                id="code"
                {...register('code')}
                placeholder="مثل: ENG, HR, IT"
                className={`${errors.code ? 'border-destructive' : ''} uppercase`}
                onChange={(e) => {
                  const value = e.target.value.toUpperCase()
                  setValue('code', value)
                }}
              />
              {suggestedCode && suggestedCode !== watchedCode && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={applySuggestedCode}
                  className="flex items-center gap-1 whitespace-nowrap"
                >
                  <Lightbulb className="h-4 w-4" />
                  {suggestedCode}
                </Button>
              )}
            </div>
            {errors.code && (
              <p className="text-sm text-destructive">{errors.code.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              رمز القسم يُستخدم في توليد معرفات الموظفين (مثل: ENG-001)
            </p>
          </div>

          {/* Capacity Field */}
          <div className="container-spaced">
            <Label htmlFor="capacity" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              سعة القسم
            </Label>
            <Input
              id="capacity"
              type="number"
              min="1"
              max="1000"
              {...register('capacity', { valueAsNumber: true })}
              placeholder="عدد الموظفين الأقصى"
              className={errors.capacity ? 'border-destructive' : ''}
            />
            {errors.capacity && (
              <p className="text-sm text-destructive">{errors.capacity.message}</p>
            )}
            {mode === 'edit' && currentEmployeeCount > 0 && (
              <p className="text-xs text-muted-foreground">
                لا يمكن تقليل السعة إلى أقل من {currentEmployeeCount} (عدد الموظفين الحاليين)
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="gradient-primary hover:opacity-90 shadow-medium hover-lift"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
            </Button>

            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="shadow-soft hover-lift border-2 border-border/50"
              >
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

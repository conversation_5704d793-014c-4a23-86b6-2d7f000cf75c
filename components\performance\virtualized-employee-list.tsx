"use client"

import React, { use<PERSON>emo, use<PERSON>allback, useState, useEffect } from 'react'
import { FixedSizeList as List } from 'react-window'
import InfiniteLoader from 'react-window-infinite-loader'
import { useInView } from 'react-intersection-observer'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useHRStore } from '@/lib/store/hr-store'
import { performanceService, debounce } from '@/lib/services/performance-service'
import { Employee } from '@/lib/types'
import { 
  Users, 
  Edit, 
  Eye,
  Building2,
  Mail,
  Calendar,
  Zap,
  TrendingUp
} from 'lucide-react'

interface VirtualizedEmployeeListProps {
  onEditEmployee?: (employee: Employee) => void
  onViewEmployee?: (employee: Employee) => void
  searchQuery?: string
  filters?: Record<string, any>
  showBulkActions?: boolean
}

interface EmployeeItemProps {
  index: number
  style: React.CSSProperties
  data: {
    employees: Employee[]
    selectedEmployees: string[]
    onToggleSelection: (id: string) => void
    onEdit?: (employee: Employee) => void
    onView?: (employee: Employee) => void
    departments: any[]
  }
}

const EmployeeItem: React.FC<EmployeeItemProps> = ({ index, style, data }) => {
  const { employees, selectedEmployees, onToggleSelection, onEdit, onView, departments } = data
  const employee = employees[index]

  if (!employee) {
    return (
      <div style={style} className="p-4">
        <div className="animate-pulse">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const department = departments.find(d => d.id === employee.departmentId)
  const isSelected = selectedEmployees.includes(employee.id)

  const getStatusBadge = (status: Employee['status']) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', variant: 'default' as const },
      TRANSFERRED: { label: 'منقول', variant: 'secondary' as const },
      PENDING_REMOVAL: { label: 'في انتظار الإزالة', variant: 'destructive' as const },
      ARCHIVED: { label: 'مؤرشف', variant: 'outline' as const }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <div style={style} className="p-2">
      <Card className={`transition-all duration-200 hover:shadow-md ${isSelected ? 'ring-2 ring-primary' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => onToggleSelection(employee.id)}
            />
            
            <Avatar className="h-12 w-12 border-2 border-white shadow-lg">
              <AvatarImage src="" alt={employee.name} />
              <AvatarFallback className="gradient-primary text-white font-bold">
                {employee.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-foreground truncate">{employee.name}</h3>
                {getStatusBadge(employee.status)}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  <span className="truncate">{employee.email}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Building2 className="h-3 w-3" />
                  <span>{department?.name || 'غير مخصص'}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{new Date(employee.hireDate).toLocaleDateString('ar-SA')}</span>
                </div>
              </div>
              
              <div className="mt-2">
                <span className="text-xs font-mono text-muted-foreground">{employee.id}</span>
              </div>
            </div>
            
            <div className="flex gap-1">
              {onView && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onView(employee)}
                  className="h-8 w-8 p-0"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              
              {onEdit && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onEdit(employee)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function VirtualizedEmployeeList({
  onEditEmployee,
  onViewEmployee,
  searchQuery = '',
  filters = {},
  showBulkActions = true
}: VirtualizedEmployeeListProps) {
  const { 
    employees, 
    departments, 
    selectedEmployees, 
    toggleEmployeeSelection,
    selectAllEmployees,
    clearSelection
  } = useHRStore()

  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 })
  const [metrics, setMetrics] = useState(performanceService.getMetrics())

  // Optimized search and filtering
  const filteredEmployees = useMemo(() => {
    return performanceService.optimizeEmployeeSearch(employees, searchQuery, filters)
  }, [employees, searchQuery, filters])

  // Virtualization config
  const config = performanceService.getVirtualizationConfig()
  const shouldVirtualize = performanceService.shouldVirtualize(filteredEmployees.length)

  // Debounced selection handlers
  const debouncedToggleSelection = useCallback(
    debounce((id: string) => toggleEmployeeSelection(id), 100),
    [toggleEmployeeSelection]
  )

  const debouncedSelectAll = useCallback(
    debounce(() => {
      if (selectedEmployees.length === filteredEmployees.length) {
        clearSelection()
      } else {
        selectAllEmployees(filteredEmployees.map(emp => emp.id))
      }
    }, 200),
    [selectedEmployees, filteredEmployees, clearSelection, selectAllEmployees]
  )

  // Update metrics when visible range changes
  useEffect(() => {
    performanceService.updateListMetrics(
      filteredEmployees.length,
      visibleRange.end - visibleRange.start
    )
    setMetrics(performanceService.getMetrics())
  }, [filteredEmployees.length, visibleRange])

  // Performance monitoring component
  const PerformanceMonitor = () => {
    const { ref, inView } = useInView({ threshold: 0 })
    
    if (!inView) return <div ref={ref} />
    
    return (
      <div ref={ref} className="fixed bottom-4 right-4 z-50">
        <Card className="bg-black/80 text-white text-xs p-2">
          <div className="flex items-center gap-2">
            <Zap className="h-3 w-3" />
            <div>
              <div>عرض: {metrics.visibleItems}/{metrics.listSize}</div>
              <div>الأداء: {metrics.renderTime.toFixed(1)}ms</div>
              <div>الذاكرة: {(metrics.memoryUsage / 1024).toFixed(1)}KB</div>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  // Item data for virtualized list
  const itemData = useMemo(() => ({
    employees: filteredEmployees,
    selectedEmployees,
    onToggleSelection: debouncedToggleSelection,
    onEdit: onEditEmployee,
    onView: onViewEmployee,
    departments
  }), [
    filteredEmployees,
    selectedEmployees,
    debouncedToggleSelection,
    onEditEmployee,
    onViewEmployee,
    departments
  ])

  // Handle visible items change
  const handleItemsRendered = useCallback(({ visibleStartIndex, visibleStopIndex }: any) => {
    setVisibleRange({ start: visibleStartIndex, end: visibleStopIndex })
  }, [])

  if (filteredEmployees.length === 0) {
    return (
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-lg font-medium text-muted-foreground">
            لا توجد موظفين
          </p>
          <p className="text-sm text-muted-foreground">
            {searchQuery || Object.keys(filters).length > 0
              ? 'لا توجد نتائج تطابق المرشحات المحددة'
              : 'لم يتم إضافة أي موظفين بعد'}
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container-spaced">
      {/* Performance indicator */}
      {shouldVirtualize && (
        <Card className="bg-blue-50 border-blue-200 mb-4">
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  تم تفعيل التحسين التلقائي
                </p>
                <p className="text-xs text-blue-700">
                  يتم عرض {filteredEmployees.length} موظف باستخدام التقسيم الافتراضي لتحسين الأداء
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk actions */}
      {showBulkActions && selectedEmployees.length > 0 && (
        <Card className="bg-orange-50 border-orange-200 mb-4">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-orange-900">
                تم اختيار {selectedEmployees.length} موظف
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={clearSelection}
                  className="text-orange-700 border-orange-300 hover:bg-orange-100"
                >
                  إلغاء التحديد
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Header with select all */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm mb-4">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}
                onCheckedChange={debouncedSelectAll}
              />
              <h3 className="font-semibold">
                قائمة الموظفين ({filteredEmployees.length})
              </h3>
            </div>
            
            {shouldVirtualize && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                محسن
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Employee list */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-0">
          {shouldVirtualize ? (
            <List
              height={config.containerHeight}
              itemCount={filteredEmployees.length}
              itemSize={config.itemHeight}
              itemData={itemData}
              overscanCount={config.overscan}
              onItemsRendered={handleItemsRendered}
            >
              {EmployeeItem}
            </List>
          ) : (
            <div className="max-h-[600px] overflow-y-auto">
              {filteredEmployees.map((employee, index) => (
                <EmployeeItem
                  key={employee.id}
                  index={index}
                  style={{ height: config.itemHeight }}
                  data={itemData}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance monitor (only in development) */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </div>
  )
}

"use client"

import React, { useState, useMemo } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useHRStore } from '@/lib/store/hr-store'
import { freeBucketService } from '@/lib/services/free-bucket-service'
import { Employee } from '@/lib/types'
import { 
  Archive, 
  Users, 
  Shuffle,
  AlertTriangle,
  Clock,
  TrendingUp,
  Building2,
  CheckCircle,
  Eye,
  Calendar,
  BarChart3,
  Lightbulb
} from 'lucide-react'
import { toast } from 'sonner'

export function FreeBucketPanel() {
  const { data: session } = useSession()
  const { 
    employees, 
    departments, 
    freeBucket,
    setEmployees,
    isLoading 
  } = useHRStore()
  
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedDepartment, setSelectedDepartment] = useState<string>('')
  const [distributionStrategy, setDistributionStrategy] = useState<'round-robin' | 'least-loaded' | 'capacity-weighted'>('least-loaded')

  // Get Free Bucket statistics
  const stats = freeBucketService.getFreeBucketStats(employees)
  const recommendations = freeBucketService.getFreeBucketRecommendations(employees, departments)

  // Sort free bucket employees by stay duration (longest first)
  const sortedFreeBucket = useMemo(() => {
    return [...freeBucket].sort((a, b) => {
      const now = new Date()
      
      const getStayDuration = (emp: Employee) => {
        const lastTransfer = emp.transferHistory
          .filter(t => t.toDepartmentId === null)
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
        
        const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(emp.createdAt)
        return now.getTime() - transferDate.getTime()
      }

      return getStayDuration(b) - getStayDuration(a)
    })
  }, [freeBucket])

  const handleSelectAll = () => {
    if (selectedEmployees.length === freeBucket.length) {
      setSelectedEmployees([])
    } else {
      setSelectedEmployees(freeBucket.map(emp => emp.id))
    }
  }

  const handleSelectEmployee = (employeeId: string) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    )
  }

  const handleAssignToDepartment = async () => {
    if (!session?.user?.id || !selectedDepartment || selectedEmployees.length === 0) {
      toast.error('يرجى اختيار قسم وموظفين للتخصيص')
      return
    }

    setIsProcessing(true)
    try {
      const result = await freeBucketService.assignFromFreeBucket(
        selectedEmployees,
        selectedDepartment,
        session.user.id,
        employees,
        departments
      )

      if (result.success && result.data) {
        // Update employees in store
        const updatedEmployees = employees.map(emp => {
          const updated = result.data!.find(updatedEmp => updatedEmp.id === emp.id)
          return updated || emp
        })
        setEmployees(updatedEmployees)
        setSelectedEmployees([])
        setSelectedDepartment('')
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء التخصيص')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSmartDistribute = async () => {
    if (!session?.user?.id || selectedEmployees.length === 0) {
      toast.error('يرجى اختيار موظفين للتوزيع')
      return
    }

    setIsProcessing(true)
    try {
      const result = await freeBucketService.smartDistributeFromFreeBucket(
        selectedEmployees,
        distributionStrategy,
        session.user.id,
        employees,
        departments
      )

      if (result.success && result.data) {
        // Update employees in store
        const updatedEmployees = employees.map(emp => {
          const updated = result.data!.find(updatedEmp => updatedEmp.id === emp.id)
          return updated || emp
        })
        setEmployees(updatedEmployees)
        setSelectedEmployees([])
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء التوزيع الذكي')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleArchiveSelected = async () => {
    if (!session?.user?.id || selectedEmployees.length === 0) {
      toast.error('يرجى اختيار موظفين للأرشفة')
      return
    }

    const confirmed = window.confirm(
      `هل أنت متأكد من أرشفة ${selectedEmployees.length} موظف؟ هذا الإجراء لا يمكن التراجع عنه.`
    )
    
    if (!confirmed) return

    setIsProcessing(true)
    try {
      const result = await freeBucketService.archiveFromFreeBucket(
        selectedEmployees,
        session.user.id,
        employees
      )

      if (result.success && result.data) {
        // Update employees in store
        const updatedEmployees = employees.map(emp => {
          const updated = result.data!.find(updatedEmp => updatedEmp.id === emp.id)
          return updated || emp
        })
        setEmployees(updatedEmployees)
        setSelectedEmployees([])
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء الأرشفة')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCleanup = async () => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    setIsProcessing(true)
    try {
      const result = await freeBucketService.cleanupFreeBucket(
        30, // 30 days threshold
        session.user.id,
        employees
      )

      if (result.success && result.data) {
        // Update employees in store
        const updatedEmployees = employees.map(emp => {
          const updated = result.data!.find(updatedEmp => updatedEmp.id === emp.id)
          return updated || emp
        })
        setEmployees(updatedEmployees)
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء التنظيف')
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusBadge = (status: Employee['status']) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', variant: 'default' as const },
      TRANSFERRED: { label: 'منقول', variant: 'secondary' as const },
      PENDING_REMOVAL: { label: 'في انتظار الإزالة', variant: 'destructive' as const },
      ARCHIVED: { label: 'مؤرشف', variant: 'outline' as const }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <div className="container-spaced">
      {/* Statistics Overview */}
      <div className="grid-spaced md:grid-cols-4">
        <Card className="gradient-green-soft border-r-4 border-r-primary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الموظفين</CardTitle>
            <Users className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              في السلة المؤقتة
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-secondary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط البقاء</CardTitle>
            <Clock className="h-4 w-4 text-secondary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-secondary">{stats.averageStayDuration}</div>
            <p className="text-xs text-muted-foreground">
              يوم
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-accent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مضاف حديثاً</CardTitle>
            <TrendingUp className="h-4 w-4 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-accent">{stats.recentlyAdded.length}</div>
            <p className="text-xs text-muted-foreground">
              آخر 7 أيام
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-success">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في انتظار الإزالة</CardTitle>
            <AlertTriangle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.byStatus['PENDING_REMOVAL'] || 0}</div>
            <p className="text-xs text-muted-foreground">
              موظف
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {recommendations.recommendedActions.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-blue-900">
              <Lightbulb className="h-5 w-5" />
              توصيات
            </CardTitle>
          </CardHeader>
          <CardContent className="container-spaced">
            {recommendations.recommendedActions.map((action, index) => (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-blue-100 border border-blue-300">
                <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-blue-900">{action}</p>
              </div>
            ))}
            
            <div className="flex gap-2">
              {recommendations.shouldDistribute && (
                <Button
                  onClick={() => handleSmartDistribute()}
                  disabled={isProcessing || freeBucket.length === 0}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Shuffle className="h-4 w-4 ml-1" />
                  توزيع ذكي
                </Button>
              )}
              
              {recommendations.shouldCleanup && (
                <Button
                  onClick={handleCleanup}
                  disabled={isProcessing}
                  size="sm"
                  variant="outline"
                  className="text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  <Archive className="h-4 w-4 ml-1" />
                  تنظيف
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Operations */}
      {selectedEmployees.length > 0 && (
        <Card className="shadow-xl border-0 bg-orange-50 border-orange-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-orange-900">
              <Users className="h-5 w-5" />
              العمليات الجماعية
              <Badge variant="secondary">{selectedEmployees.length} محدد</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="container-spaced">
            <div className="grid-spaced md:grid-cols-2">
              {/* Assignment Operations */}
              <div className="container-spaced">
                <h4 className="font-semibold text-orange-900 mb-3">تخصيص لقسم</h4>
                <div className="flex gap-2">
                  <Select
                    value={selectedDepartment}
                    onValueChange={setSelectedDepartment}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="اختر القسم" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name} ({dept.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleAssignToDepartment}
                    disabled={!selectedDepartment || isProcessing}
                    size="sm"
                    className="bg-orange-600 hover:bg-orange-700 text-white"
                  >
                    <Building2 className="h-4 w-4 ml-1" />
                    تخصيص
                  </Button>
                </div>
              </div>

              {/* Smart Distribution */}
              <div className="container-spaced">
                <h4 className="font-semibold text-orange-900 mb-3">توزيع ذكي</h4>
                <div className="flex gap-2">
                  <Select
                    value={distributionStrategy}
                    onValueChange={(value: typeof distributionStrategy) => setDistributionStrategy(value)}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="round-robin">التوزيع الدائري</SelectItem>
                      <SelectItem value="least-loaded">الأقل تحميلاً</SelectItem>
                      <SelectItem value="capacity-weighted">مرجح بالسعة</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleSmartDistribute}
                    disabled={isProcessing}
                    size="sm"
                    className="bg-orange-600 hover:bg-orange-700 text-white"
                  >
                    <Shuffle className="h-4 w-4 ml-1" />
                    توزيع
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Button
                onClick={handleArchiveSelected}
                disabled={isProcessing}
                variant="destructive"
                size="sm"
              >
                <Archive className="h-4 w-4 ml-1" />
                أرشفة المحدد
              </Button>
              
              <Button
                onClick={() => setSelectedEmployees([])}
                variant="outline"
                size="sm"
                className="text-orange-700 border-orange-300 hover:bg-orange-100"
              >
                إلغاء التحديد
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employee List */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <Archive className="h-4 w-4 text-white" />
            </div>
            موظفو السلة المؤقتة ({freeBucket.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {freeBucket.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedEmployees.length === freeBucket.length && freeBucket.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>معرف الموظف</TableHead>
                  <TableHead>الاسم</TableHead>
                  <TableHead>البريد الإلكتروني</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>مدة البقاء</TableHead>
                  <TableHead>تاريخ الإضافة</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedFreeBucket.map((employee) => (
                  <TableRow key={employee.id} className="hover:bg-muted/50">
                    <TableCell>
                      <Checkbox
                        checked={selectedEmployees.includes(employee.id)}
                        onCheckedChange={() => handleSelectEmployee(employee.id)}
                      />
                    </TableCell>
                    <TableCell className="font-mono text-sm">{employee.id}</TableCell>
                    <TableCell className="font-medium">{employee.name}</TableCell>
                    <TableCell className="text-muted-foreground">{employee.email}</TableCell>
                    <TableCell>{getStatusBadge(employee.status)}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {freeBucketService.formatStayDuration(employee)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(employee.updatedAt).toLocaleDateString('ar-SA')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-muted-foreground">
                السلة المؤقتة فارغة
              </p>
              <p className="text-sm text-muted-foreground">
                لا توجد موظفين غير مخصصين حالياً
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

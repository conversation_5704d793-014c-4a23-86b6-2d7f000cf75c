import { test, expect } from '@playwright/test'

test.describe('Department Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('should display departments page', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')
    
    await expect(page.locator('text=إدارة الأقسام')).toBeVisible()
    await expect(page.locator('[data-testid="department-grid"]')).toBeVisible()
  })

  test('should create a new department', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Click add department button
    await page.click('text=إضافة قسم')
    
    // Fill department form
    await page.fill('[data-testid="department-name"]', 'قسم التسويق')
    await page.fill('[data-testid="department-code"]', 'MKT')
    await page.fill('[data-testid="department-capacity"]', '25')
    await page.fill('[data-testid="department-description"]', 'قسم التسويق والمبيعات')
    
    // Submit form
    await page.click('text=حفظ')
    
    // Verify department was created
    await expect(page.locator('text=تم إضافة القسم بنجاح')).toBeVisible()
    await expect(page.locator('text=قسم التسويق')).toBeVisible()
  })

  test('should edit department information', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Click edit button for first department
    await page.click('[data-testid="edit-department"]:first-child')
    
    // Update department capacity
    await page.fill('[data-testid="department-capacity"]', '30')
    
    // Submit form
    await page.click('text=حفظ التغييرات')
    
    // Verify department was updated
    await expect(page.locator('text=تم تحديث القسم')).toBeVisible()
  })

  test('should display department capacity utilization', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Check capacity indicators
    await expect(page.locator('[data-testid="capacity-indicator"]')).toBeVisible()
    await expect(page.locator('[data-testid="utilization-percentage"]')).toBeVisible()
    
    // Check capacity colors (green, yellow, red based on utilization)
    const capacityBar = page.locator('[data-testid="capacity-bar"]').first()
    const backgroundColor = await capacityBar.evaluate(el => 
      getComputedStyle(el).backgroundColor
    )
    expect(backgroundColor).toBeTruthy()
  })

  test('should prevent department deletion with assigned employees', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Try to delete department with employees
    await page.click('[data-testid="delete-department"]:first-child')
    
    // Should show warning message
    await expect(page.locator('text=لا يمكن حذف قسم يحتوي على موظفين')).toBeVisible()
  })

  test('should show department statistics', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Check department statistics
    await expect(page.locator('[data-testid="total-departments"]')).toBeVisible()
    await expect(page.locator('[data-testid="total-capacity"]')).toBeVisible()
    await expect(page.locator('[data-testid="occupied-positions"]')).toBeVisible()
    await expect(page.locator('[data-testid="available-positions"]')).toBeVisible()
  })

  test('should validate department form fields', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Click add department button
    await page.click('text=إضافة قسم')
    
    // Try to submit empty form
    await page.click('text=حفظ')
    
    // Verify validation errors
    await expect(page.locator('text=اسم القسم مطلوب')).toBeVisible()
    await expect(page.locator('text=رمز القسم مطلوب')).toBeVisible()
    await expect(page.locator('text=السعة مطلوبة')).toBeVisible()
  })

  test('should prevent duplicate department codes', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Click add department button
    await page.click('text=إضافة قسم')
    
    // Fill form with existing department code
    await page.fill('[data-testid="department-name"]', 'قسم جديد')
    await page.fill('[data-testid="department-code"]', 'IT') // Assuming IT already exists
    await page.fill('[data-testid="department-capacity"]', '20')
    
    // Submit form
    await page.click('text=حفظ')
    
    // Verify error message
    await expect(page.locator('text=رمز القسم موجود بالفعل')).toBeVisible()
  })

  test('should display department employee list', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Click view employees for a department
    await page.click('[data-testid="view-department-employees"]:first-child')
    
    // Verify employee list is displayed
    await expect(page.locator('[data-testid="department-employees-list"]')).toBeVisible()
    await expect(page.locator('text=موظفو القسم')).toBeVisible()
  })

  test('should handle department capacity warnings', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Check for over-capacity warnings
    const overCapacityWarning = page.locator('[data-testid="over-capacity-warning"]')
    if (await overCapacityWarning.isVisible()) {
      await expect(overCapacityWarning).toContainText('تجاوز السعة')
    }
    
    // Check for near-capacity warnings
    const nearCapacityWarning = page.locator('[data-testid="near-capacity-warning"]')
    if (await nearCapacityWarning.isVisible()) {
      await expect(nearCapacityWarning).toContainText('قريب من السعة القصوى')
    }
  })
})

test.describe('Department Management - Smart Distribution', () => {
  test('should distribute employees using round-robin algorithm', async ({ page }) => {
    await page.click('text=التوزيع الذكي')
    await page.waitForLoadState('networkidle')

    // Select round-robin strategy
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=التوزيع الدائري')
    
    // Select employees to distribute
    await page.check('[data-testid="select-all-unassigned"]')
    
    // Start distribution
    await page.click('text=بدء التوزيع')
    
    // Verify distribution completed
    await expect(page.locator('text=تم توزيع الموظفين بنجاح')).toBeVisible()
  })

  test('should distribute employees using least-loaded algorithm', async ({ page }) => {
    await page.click('text=التوزيع الذكي')
    await page.waitForLoadState('networkidle')

    // Select least-loaded strategy
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=الأقل تحميلاً')
    
    // Select specific employees
    await page.check('[data-testid="employee-checkbox"]:nth-child(1)')
    await page.check('[data-testid="employee-checkbox"]:nth-child(2)')
    
    // Start distribution
    await page.click('text=بدء التوزيع')
    
    // Verify distribution completed
    await expect(page.locator('text=تم توزيع الموظفين بنجاح')).toBeVisible()
  })

  test('should handle distribution with capacity constraints', async ({ page }) => {
    await page.click('text=التوزيع الذكي')
    await page.waitForLoadState('networkidle')

    // Enable capacity respect
    await page.check('[data-testid="respect-capacity"]')
    
    // Select capacity-weighted strategy
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=مرجح بالسعة')
    
    // Select more employees than total capacity
    await page.check('[data-testid="select-all-unassigned"]')
    
    // Start distribution
    await page.click('text=بدء التوزيع')
    
    // Verify overflow handling
    await expect(page.locator('text=بعض الموظفين لم يتم توزيعهم بسبب السعة')).toBeVisible()
  })

  test('should show distribution preview', async ({ page }) => {
    await page.click('text=التوزيع الذكي')
    await page.waitForLoadState('networkidle')

    // Select distribution strategy
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=التوزيع الدائري')
    
    // Select employees
    await page.check('[data-testid="employee-checkbox"]:nth-child(1)')
    await page.check('[data-testid="employee-checkbox"]:nth-child(2)')
    
    // Click preview
    await page.click('text=معاينة التوزيع')
    
    // Verify preview is displayed
    await expect(page.locator('[data-testid="distribution-preview"]')).toBeVisible()
    await expect(page.locator('text=معاينة التوزيع')).toBeVisible()
  })
})

test.describe('Department Management - Performance', () => {
  test('should handle large number of departments efficiently', async ({ page }) => {
    await page.goto('/departments')
    
    // Measure page load time
    const startTime = Date.now()
    await page.waitForLoadState('networkidle')
    const loadTime = Date.now() - startTime
    
    // Should load within reasonable time (5 seconds)
    expect(loadTime).toBeLessThan(5000)
    
    // Check if virtualization is enabled for large lists
    const departmentGrid = page.locator('[data-testid="department-grid"]')
    await expect(departmentGrid).toBeVisible()
  })

  test('should update department statistics in real-time', async ({ page }) => {
    await page.click('text=الأقسام')
    await page.waitForLoadState('networkidle')

    // Get initial statistics
    const initialCount = await page.locator('[data-testid="total-departments"]').textContent()
    
    // Add new department
    await page.click('text=إضافة قسم')
    await page.fill('[data-testid="department-name"]', 'قسم مؤقت')
    await page.fill('[data-testid="department-code"]', 'TEMP')
    await page.fill('[data-testid="department-capacity"]', '10')
    await page.click('text=حفظ')
    
    // Verify statistics updated
    await page.waitForTimeout(1000) // Wait for update
    const newCount = await page.locator('[data-testid="total-departments"]').textContent()
    expect(newCount).not.toBe(initialCount)
  })
})

import { test, expect } from '@playwright/test'

test.describe('Free Bucket Operations', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('should display free bucket panel', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')
    
    await expect(page.locator('text=موظفو السلة المؤقتة')).toBeVisible()
    await expect(page.locator('[data-testid="free-bucket-stats"]')).toBeVisible()
  })

  test('should move employees to free bucket', async ({ page }) => {
    // Go to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Select employees
    await page.check('[data-testid="employee-checkbox"]:first-child')
    
    // Click bulk actions
    await page.click('text=العمليات الجماعية')
    
    // Move to free bucket
    await page.click('text=نقل للسلة المؤقتة')
    await page.fill('[data-testid="move-reason"]', 'إعادة تنظيم')
    await page.click('text=تأكيد النقل')
    
    // Verify move was successful
    await expect(page.locator('text=تم نقل الموظف إلى السلة المؤقتة')).toBeVisible()
    
    // Check free bucket
    await page.click('text=السلة المؤقتة')
    await expect(page.locator('[data-testid="free-bucket-employee"]')).toBeVisible()
  })

  test('should assign employees from free bucket to department', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select employees in free bucket
    await page.check('[data-testid="free-bucket-checkbox"]:first-child')
    
    // Click assign to department
    await page.click('text=تخصيص لقسم')
    
    // Select target department
    await page.click('[data-testid="target-department"]')
    await page.click('text=قسم تقنية المعلومات')
    
    // Confirm assignment
    await page.click('text=تأكيد التخصيص')
    
    // Verify assignment was successful
    await expect(page.locator('text=تم تخصيص الموظف بنجاح')).toBeVisible()
  })

  test('should perform smart distribution from free bucket', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select multiple employees
    await page.check('[data-testid="select-all-free-bucket"]')
    
    // Click smart distribution
    await page.click('text=توزيع ذكي')
    
    // Select distribution strategy
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=الأقل تحميلاً')
    
    // Start distribution
    await page.click('text=بدء التوزيع')
    
    // Verify distribution completed
    await expect(page.locator('text=تم توزيع الموظفين بنجاح')).toBeVisible()
  })

  test('should archive employees from free bucket', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select employees to archive
    await page.check('[data-testid="free-bucket-checkbox"]:first-child')
    
    // Click archive
    await page.click('text=أرشفة المحدد')
    
    // Confirm archive action
    await page.click('text=تأكيد الأرشفة')
    
    // Verify archive was successful
    await expect(page.locator('text=تم أرشفة الموظف')).toBeVisible()
  })

  test('should display free bucket statistics', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Check statistics cards
    await expect(page.locator('[data-testid="total-free-bucket"]')).toBeVisible()
    await expect(page.locator('[data-testid="average-stay-duration"]')).toBeVisible()
    await expect(page.locator('[data-testid="recently-added"]')).toBeVisible()
    await expect(page.locator('[data-testid="pending-removal"]')).toBeVisible()
  })

  test('should show recommendations for free bucket management', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Check if recommendations are displayed
    const recommendationsCard = page.locator('[data-testid="recommendations"]')
    if (await recommendationsCard.isVisible()) {
      await expect(recommendationsCard).toContainText('توصيات')
      
      // Check recommendation actions
      const distributeButton = page.locator('text=توزيع ذكي')
      const cleanupButton = page.locator('text=تنظيف')
      
      if (await distributeButton.isVisible()) {
        await expect(distributeButton).toBeEnabled()
      }
      
      if (await cleanupButton.isVisible()) {
        await expect(cleanupButton).toBeEnabled()
      }
    }
  })

  test('should track employee stay duration in free bucket', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Check stay duration display
    await expect(page.locator('[data-testid="stay-duration"]')).toBeVisible()
    
    // Verify duration format (should show days, hours, etc.)
    const durationText = await page.locator('[data-testid="stay-duration"]').first().textContent()
    expect(durationText).toMatch(/(يوم|ساعة|دقيقة)/)
  })

  test('should handle bulk operations on free bucket employees', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select multiple employees
    await page.check('[data-testid="free-bucket-checkbox"]:nth-child(1)')
    await page.check('[data-testid="free-bucket-checkbox"]:nth-child(2)')
    
    // Verify bulk actions bar appears
    await expect(page.locator('[data-testid="bulk-actions-bar"]')).toBeVisible()
    await expect(page.locator('text=تم اختيار')).toBeVisible()
    
    // Test cancel selection
    await page.click('text=إلغاء التحديد')
    await expect(page.locator('[data-testid="bulk-actions-bar"]')).not.toBeVisible()
  })

  test('should perform cleanup of old free bucket employees', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Click cleanup button (if available)
    const cleanupButton = page.locator('text=تنظيف')
    if (await cleanupButton.isVisible()) {
      await cleanupButton.click()
      
      // Should show confirmation dialog
      await expect(page.locator('text=تم العثور على')).toBeVisible()
      
      // Confirm cleanup
      await page.click('text=موافق')
      
      // Verify cleanup completed
      await expect(page.locator('text=تم تنظيف السلة المؤقتة')).toBeVisible()
    }
  })
})

test.describe('Free Bucket - Capacity Management', () => {
  test('should prevent assignment when department is at capacity', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select employee
    await page.check('[data-testid="free-bucket-checkbox"]:first-child')
    
    // Try to assign to full department
    await page.click('text=تخصيص لقسم')
    await page.click('[data-testid="target-department"]')
    await page.click('text=قسم ممتلئ') // Assuming this department is at capacity
    
    await page.click('text=تأكيد التخصيص')
    
    // Should show capacity error
    await expect(page.locator('text=القسم وصل للسعة القصوى')).toBeVisible()
  })

  test('should show available capacity when assigning', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select employee
    await page.check('[data-testid="free-bucket-checkbox"]:first-child')
    
    // Click assign to department
    await page.click('text=تخصيص لقسم')
    
    // Check that available capacity is displayed for each department
    await expect(page.locator('[data-testid="department-capacity-info"]')).toBeVisible()
    await expect(page.locator('text=متاح')).toBeVisible()
  })

  test('should handle overflow in smart distribution', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Select more employees than total available capacity
    await page.check('[data-testid="select-all-free-bucket"]')
    
    // Start smart distribution
    await page.click('text=توزيع ذكي')
    await page.click('[data-testid="distribution-strategy"]')
    await page.click('text=مرجح بالسعة')
    await page.click('text=بدء التوزيع')
    
    // Should handle overflow gracefully
    await expect(page.locator('text=تم توزيع')).toBeVisible()
    
    // Check if some employees remain in free bucket
    const remainingEmployees = page.locator('[data-testid="free-bucket-employee"]')
    if (await remainingEmployees.count() > 0) {
      await expect(page.locator('text=لم يتم توزيعهم بسبب عدم توفر مقاعد')).toBeVisible()
    }
  })
})

test.describe('Free Bucket - Real-time Updates', () => {
  test('should update statistics in real-time', async ({ page }) => {
    await page.click('text=السلة المؤقتة')
    await page.waitForLoadState('networkidle')

    // Get initial count
    const initialCount = await page.locator('[data-testid="total-free-bucket"]').textContent()
    
    // Move employee to free bucket from another page
    await page.click('text=الموظفون')
    await page.check('[data-testid="employee-checkbox"]:first-child')
    await page.click('text=العمليات الجماعية')
    await page.click('text=نقل للسلة المؤقتة')
    await page.fill('[data-testid="move-reason"]', 'اختبار')
    await page.click('text=تأكيد النقل')
    
    // Go back to free bucket
    await page.click('text=السلة المؤقتة')
    
    // Verify count updated
    await page.waitForTimeout(1000) // Wait for real-time update
    const newCount = await page.locator('[data-testid="total-free-bucket"]').textContent()
    expect(newCount).not.toBe(initialCount)
  })
})

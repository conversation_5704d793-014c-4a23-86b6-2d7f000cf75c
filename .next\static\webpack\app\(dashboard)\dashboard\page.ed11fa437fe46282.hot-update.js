"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./lib/store/hr-store.ts":
/*!*******************************!*\
  !*** ./lib/store/hr-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHRStore: () => (/* binding */ useHRStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/services/employee-service */ \"(app-pages-browser)/./lib/services/employee-service.ts\");\n/* harmony import */ var _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/distribution-service */ \"(app-pages-browser)/./lib/services/distribution-service.ts\");\n/* harmony import */ var _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/id-generator */ \"(app-pages-browser)/./lib/utils/id-generator.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\nconst useHRStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        // Initial state\n        employees: [],\n        departments: [],\n        freeBucket: [],\n        selectedEmployees: [],\n        isLoading: false,\n        searchQuery: '',\n        // Data actions\n        setEmployees: (employees)=>set((state)=>({\n                    employees,\n                    freeBucket: employees.filter((emp)=>emp.departmentId === null)\n                })),\n        setDepartments: (departments)=>set({\n                departments\n            }),\n        addEmployee: (employee)=>set((state)=>({\n                    employees: [\n                        ...state.employees,\n                        employee\n                    ]\n                })),\n        updateEmployee: (id, updates)=>set((state)=>({\n                    employees: state.employees.map((emp)=>emp.id === id ? {\n                            ...emp,\n                            ...updates\n                        } : emp)\n                })),\n        removeEmployee: (id)=>set((state)=>({\n                    employees: state.employees.filter((emp)=>emp.id !== id),\n                    selectedEmployees: state.selectedEmployees.filter((empId)=>empId !== id)\n                })),\n        // Department actions\n        addDepartment: (department)=>set((state)=>({\n                    departments: [\n                        ...state.departments,\n                        department\n                    ]\n                })),\n        updateDepartment: (id, updates)=>set((state)=>({\n                    departments: state.departments.map((dept)=>dept.id === id ? {\n                            ...dept,\n                            ...updates\n                        } : dept)\n                })),\n        removeDepartment: (id)=>set((state)=>({\n                    departments: state.departments.filter((dept)=>dept.id !== id)\n                })),\n        // Enhanced Employee Operations\n        createEmployee: async (data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.createEmployee(data, departments, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: [\n                                ...state.employees,\n                                result.data\n                            ],\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket,\n                                result.data\n                            ] : state.freeBucket\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم إنشاء الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في إنشاء الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في إنشاء الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateEmployeeData: async (id, data)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.updateEmployee(id, data, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم تحديث الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في تحديث الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في تحديث الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        transferEmployee: async (id, data)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.transferEmployee(id, data, employees, departments);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: result.data.departmentId === null ? [\n                                ...state.freeBucket.filter((emp)=>emp.id !== id),\n                                result.data\n                            ] : state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم نقل الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في نقل الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في نقل الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        archiveEmployee: async (id, userId)=>{\n            const { employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.archiveEmployee(id, userId, employees);\n                if (result.success && result.data) {\n                    set((state)=>({\n                            employees: state.employees.map((emp)=>emp.id === id ? result.data : emp),\n                            freeBucket: state.freeBucket.filter((emp)=>emp.id !== id)\n                        }));\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('تم أرشفة الموظف بنجاح');\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(result.error || 'فشل في أرشفة الموظف');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في أرشفة الموظف');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Selection actions\n        toggleEmployeeSelection: (id)=>set((state)=>({\n                    selectedEmployees: state.selectedEmployees.includes(id) ? state.selectedEmployees.filter((empId)=>empId !== id) : [\n                        ...state.selectedEmployees,\n                        id\n                    ]\n                })),\n        selectAllEmployees: (ids)=>set({\n                selectedEmployees: ids\n            }),\n        clearSelection: ()=>set({\n                selectedEmployees: []\n            }),\n        // Bulk operations\n        executeBulkOperation: async (operation, userId)=>{\n            const { employees, departments } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_employee_service__WEBPACK_IMPORTED_MODULE_0__.employeeService.bulkOperation(operation, employees, departments, userId);\n                if (result.success && result.data) {\n                    // Update employees with the results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        result.data.forEach((updatedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === updatedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = updatedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null),\n                            selectedEmployees: []\n                        };\n                    });\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في العملية الجماعية');\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Search & filter\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        getFilteredEmployees: ()=>{\n            const { employees, searchQuery } = get();\n            if (!searchQuery) return employees;\n            return employees.filter((emp)=>emp.name.toLowerCase().includes(searchQuery.toLowerCase()) || emp.email.toLowerCase().includes(searchQuery.toLowerCase()) || emp.id.toLowerCase().includes(searchQuery.toLowerCase()));\n        },\n        // Free bucket operations\n        moveToFreeBucket: (employeeIds)=>set((state)=>{\n                const movedEmployees = state.employees.filter((emp)=>employeeIds.includes(emp.id)).map((emp)=>({\n                        ...emp,\n                        departmentId: null\n                    }));\n                return {\n                    employees: state.employees.map((emp)=>employeeIds.includes(emp.id) ? {\n                            ...emp,\n                            departmentId: null\n                        } : emp),\n                    freeBucket: [\n                        ...state.freeBucket,\n                        ...movedEmployees\n                    ]\n                };\n            }),\n        removeFromFreeBucket: (employeeIds)=>set((state)=>({\n                    freeBucket: state.freeBucket.filter((emp)=>!employeeIds.includes(emp.id)),\n                    employees: state.employees.filter((emp)=>!employeeIds.includes(emp.id))\n                })),\n        // Distribution\n        distributeEmployees: async (config, userId)=>{\n            const { departments, freeBucket } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.distributeEmployees(freeBucket, departments, config, userId);\n                if (result.success) {\n                    // Update employees with distributed results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        // Update overflow employees\n                        result.overflow.forEach((overflowEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === overflowEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = overflowEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('فشل في عملية التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في عملية التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        rebalanceEmployees: async (config, userId)=>{\n            const { departments, employees } = get();\n            set({\n                isLoading: true\n            });\n            try {\n                const result = await _lib_services_distribution_service__WEBPACK_IMPORTED_MODULE_1__.distributionService.rebalanceEmployees(departments, employees, config, userId);\n                if (result.success) {\n                    // Update employees with rebalanced results\n                    set((state)=>{\n                        const updatedEmployees = [\n                            ...state.employees\n                        ];\n                        // Update distributed employees\n                        result.distributed.forEach((distributedEmp)=>{\n                            const index = updatedEmployees.findIndex((emp)=>emp.id === distributedEmp.id);\n                            if (index !== -1) {\n                                updatedEmployees[index] = distributedEmp;\n                            }\n                        });\n                        return {\n                            employees: updatedEmployees,\n                            freeBucket: updatedEmployees.filter((emp)=>emp.departmentId === null)\n                        };\n                    });\n                    return true;\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('فشل في إعادة التوزيع');\n                    return false;\n                }\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('خطأ في إعادة التوزيع');\n                return false;\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Initialization\n        initializeIDGenerator: ()=>{\n            const { departments, employees } = get();\n            _lib_utils_id_generator__WEBPACK_IMPORTED_MODULE_2__.idGenerator.initializeCounters(departments, employees);\n        }\n    })));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/store/hr-store.ts\n"));

/***/ })

});
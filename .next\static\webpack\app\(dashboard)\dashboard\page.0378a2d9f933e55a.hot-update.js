"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/compiler-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/compiler-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/department-card */ \"(app-pages-browser)/./components/dashboard/department-card.tsx\");\n/* harmony import */ var _components_employees_add_employee_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/employees/add-employee-dialog */ \"(app-pages-browser)/./components/employees/add-employee-dialog.tsx\");\n/* harmony import */ var _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/store/hr-store */ \"(app-pages-browser)/./lib/store/hr-store.ts\");\n/* harmony import */ var _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/hooks/use-api-data */ \"(app-pages-browser)/./lib/hooks/use-api-data.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage(t0) {\n    var _session_user;\n    _s();\n    const $ = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__.c)(76);\n    if ($[0] !== \"7b5080e528656ee8fdd86dcc04d2d380752cdbe0c65cb0edb528040e7ac9d991\") {\n        for(let $i = 0; $i < 76; $i += 1){\n            $[$i] = Symbol.for(\"react.memo_cache_sentinel\");\n        }\n        $[0] = \"7b5080e528656ee8fdd86dcc04d2d380752cdbe0c65cb0edb528040e7ac9d991\";\n    }\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { departments, employees, freeBucket } = (0,_lib_store_hr_store__WEBPACK_IMPORTED_MODULE_9__.useHRStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showAddEmployeeDialog, setShowAddEmployeeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,_lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_10__.useApiData)();\n    let t1;\n    let t2;\n    if ($[1] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t1 = ()=>{\n            const loadData = async ()=>{\n                ;\n                try {\n                    await new Promise(_temp);\n                    setIsLoading(false);\n                } catch (t3) {\n                    const error = t3;\n                    console.error(\"Failed to load dashboard data:\", error);\n                    setIsLoading(false);\n                }\n            };\n            loadData();\n        };\n        t2 = [];\n        $[1] = t1;\n        $[2] = t2;\n    } else {\n        t1 = $[1];\n        t2 = $[2];\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(t1, t2);\n    let t3;\n    if ($[3] !== departments) {\n        t3 = ()=>{\n            const csvContent = [\n                [\n                    \"Department\",\n                    \"Employee Count\",\n                    \"Capacity\",\n                    \"Utilization\"\n                ],\n                ...departments.map(_temp2)\n            ].map(_temp3).join(\"\\n\");\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv\"\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"dashboard-report-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n        };\n        $[3] = departments;\n        $[4] = t3;\n    } else {\n        t3 = $[4];\n    }\n    const handleExportReport = t3;\n    const totalEmployees = employees.length;\n    const totalDepartments = departments.length;\n    const freeBucketCount = freeBucket.length;\n    let t4;\n    if ($[5] !== departments) {\n        t4 = departments.length > 0 ? Math.round(departments.reduce(_temp4, 0) / departments.length) : 0;\n        $[5] = departments;\n        $[6] = t4;\n    } else {\n        t4 = $[6];\n    }\n    const averageUtilization = t4;\n    let t5;\n    if ($[7] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t5 = [\n            {\n                action: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0645\\u0648\\u0638\\u0641\",\n                details: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u0647\\u0646\\u062F\\u0633\\u0629\",\n                time: \"\\u0645\\u0646\\u0630 \\u0633\\u0627\\u0639\\u062A\\u064A\\u0646\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0638\\u0641 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0627\\u0646\\u0636\\u0645\\u062A \\u0641\\u0627\\u0637\\u0645\\u0629 \\u0623\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642\",\n                time: \"\\u0645\\u0646\\u0630 4 \\u0633\\u0627\\u0639\\u0627\\u062A\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0642\\u0633\\u0645 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0648\\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631\",\n                time: \"\\u0645\\u0646\\u0630 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F\"\n            }\n        ];\n        $[7] = t5;\n    } else {\n        t5 = $[7];\n    }\n    const recentActivity = t5;\n    if (isLoading) {\n        let t6;\n        if ($[8] === Symbol.for(\"react.memo_cache_sentinel\")) {\n            t6 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        ...Array(4)\n                    ].map(_temp5)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 39\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 12\n            }, this);\n            $[8] = t6;\n        } else {\n            t6 = $[8];\n        }\n        return t6;\n    }\n    let t6;\n    if ($[9] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t6 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-4xl font-bold gradient-primary bg-clip-text text-transparent\",\n            children: \"لوحة التحكم\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 10\n        }, this);\n        $[9] = t6;\n    } else {\n        t6 = $[9];\n    }\n    const t7 = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name;\n    let t8;\n    if ($[10] !== t7) {\n        t8 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                t6,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground\",\n                    children: [\n                        \"أهلاً بك، \",\n                        t7,\n                        \". إليك ما يحدث في مؤسستك.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 41\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 133,\n            columnNumber: 10\n        }, this);\n        $[10] = t7;\n        $[11] = t8;\n    } else {\n        t8 = $[11];\n    }\n    let t9;\n    if ($[12] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t9 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-4 w-4 ml-2\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 141,\n            columnNumber: 10\n        }, this);\n        $[12] = t9;\n    } else {\n        t9 = $[12];\n    }\n    let t10;\n    if ($[13] !== handleExportReport) {\n        t10 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n            variant: \"outline\",\n            className: \"shadow-soft hover-lift border-2 border-border/50\",\n            onClick: handleExportReport,\n            children: [\n                t9,\n                \"تصدير التقرير\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 148,\n            columnNumber: 11\n        }, this);\n        $[13] = handleExportReport;\n        $[14] = t10;\n    } else {\n        t10 = $[14];\n    }\n    let t11;\n    if ($[15] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t11 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n            className: \"gradient-primary hover:opacity-90 shadow-medium hover-lift\",\n            onClick: ()=>setShowAddEmployeeDialog(true),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 ml-2\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 137\n                }, this),\n                \"إضافة موظف\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 11\n        }, this);\n        $[15] = t11;\n    } else {\n        t11 = $[15];\n    }\n    let t12;\n    if ($[16] !== t10) {\n        t12 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3\",\n            children: [\n                t10,\n                t11\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 11\n        }, this);\n        $[16] = t10;\n        $[17] = t12;\n    } else {\n        t12 = $[17];\n    }\n    let t13;\n    if ($[18] !== t12 || $[19] !== t8) {\n        t13 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                t8,\n                t12\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 11\n        }, this);\n        $[18] = t12;\n        $[19] = t8;\n        $[20] = t13;\n    } else {\n        t13 = $[20];\n    }\n    let t14;\n    if ($[21] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t14 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n            className: \"text-sm font-semibold text-primary\",\n            children: \"إجمالي الموظفين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 11\n        }, this);\n        $[21] = t14;\n    } else {\n        t14 = $[21];\n    }\n    let t15;\n    if ($[22] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t15 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t14,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 196\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 11\n        }, this);\n        $[22] = t15;\n    } else {\n        t15 = $[22];\n    }\n    let t16;\n    if ($[23] !== totalEmployees) {\n        t16 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-primary\",\n            children: totalEmployees\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 11\n        }, this);\n        $[23] = totalEmployees;\n        $[24] = t16;\n    } else {\n        t16 = $[24];\n    }\n    let t17;\n    if ($[25] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t17 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-primary/70 font-medium mt-1\",\n            children: \"+12% من الشهر الماضي\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 202,\n            columnNumber: 11\n        }, this);\n        $[25] = t17;\n    } else {\n        t17 = $[25];\n    }\n    let t18;\n    if ($[26] !== t16) {\n        t18 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-primary\",\n            children: [\n                t15,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        t16,\n                        t17\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 209,\n            columnNumber: 11\n        }, this);\n        $[26] = t16;\n        $[27] = t18;\n    } else {\n        t18 = $[27];\n    }\n    let t19;\n    if ($[28] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t19 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n            className: \"text-sm font-semibold text-secondary\",\n            children: \"الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 217,\n            columnNumber: 11\n        }, this);\n        $[28] = t19;\n    } else {\n        t19 = $[28];\n    }\n    let t20;\n    if ($[29] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t20 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t19,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-secondary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 192\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 224,\n            columnNumber: 11\n        }, this);\n        $[29] = t20;\n    } else {\n        t20 = $[29];\n    }\n    let t21;\n    if ($[30] !== totalDepartments) {\n        t21 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-secondary\",\n            children: totalDepartments\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 231,\n            columnNumber: 11\n        }, this);\n        $[30] = totalDepartments;\n        $[31] = t21;\n    } else {\n        t21 = $[31];\n    }\n    let t22;\n    if ($[32] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t22 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-secondary/70 font-medium mt-1\",\n            children: \"الأقسام النشطة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 239,\n            columnNumber: 11\n        }, this);\n        $[32] = t22;\n    } else {\n        t22 = $[32];\n    }\n    let t23;\n    if ($[33] !== t21) {\n        t23 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-secondary\",\n            children: [\n                t20,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        t21,\n                        t22\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 168\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 246,\n            columnNumber: 11\n        }, this);\n        $[33] = t21;\n        $[34] = t23;\n    } else {\n        t23 = $[34];\n    }\n    let t24;\n    if ($[35] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t24 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n            className: \"text-sm font-semibold text-accent\",\n            children: \"السلة المؤقتة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 254,\n            columnNumber: 11\n        }, this);\n        $[35] = t24;\n    } else {\n        t24 = $[35];\n    }\n    let t25;\n    if ($[36] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t25 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t24,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-accent rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 189\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 261,\n            columnNumber: 11\n        }, this);\n        $[36] = t25;\n    } else {\n        t25 = $[36];\n    }\n    let t26;\n    if ($[37] !== freeBucketCount) {\n        t26 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-accent\",\n            children: freeBucketCount\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 11\n        }, this);\n        $[37] = freeBucketCount;\n        $[38] = t26;\n    } else {\n        t26 = $[38];\n    }\n    let t27;\n    if ($[39] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t27 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-accent/70 font-medium mt-1\",\n            children: \"موظفون غير مخصصين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 11\n        }, this);\n        $[39] = t27;\n    } else {\n        t27 = $[39];\n    }\n    let t28;\n    if ($[40] !== t26) {\n        t28 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-accent\",\n            children: [\n                t25,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        t26,\n                        t27\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 165\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 11\n        }, this);\n        $[40] = t26;\n        $[41] = t28;\n    } else {\n        t28 = $[41];\n    }\n    let t29;\n    if ($[42] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t29 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n            className: \"text-sm font-semibold text-success\",\n            children: \"متوسط الاستخدام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 11\n        }, this);\n        $[42] = t29;\n    } else {\n        t29 = $[42];\n    }\n    let t30;\n    if ($[43] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t30 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t29,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-success rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 190\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 298,\n            columnNumber: 11\n        }, this);\n        $[43] = t30;\n    } else {\n        t30 = $[43];\n    }\n    let t31;\n    if ($[44] !== averageUtilization) {\n        t31 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-success\",\n            children: [\n                averageUtilization,\n                \"%\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 11\n        }, this);\n        $[44] = averageUtilization;\n        $[45] = t31;\n    } else {\n        t31 = $[45];\n    }\n    let t32;\n    if ($[46] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t32 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-success/70 font-medium mt-1\",\n            children: \"سعة الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 313,\n            columnNumber: 11\n        }, this);\n        $[46] = t32;\n    } else {\n        t32 = $[46];\n    }\n    let t33;\n    if ($[47] !== t31) {\n        t33 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-success\",\n            children: [\n                t30,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        t31,\n                        t32\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 11\n        }, this);\n        $[47] = t31;\n        $[48] = t33;\n    } else {\n        t33 = $[48];\n    }\n    let t34;\n    if ($[49] !== t18 || $[50] !== t23 || $[51] !== t28 || $[52] !== t33) {\n        t34 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                t18,\n                t23,\n                t28,\n                t33\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 11\n        }, this);\n        $[49] = t18;\n        $[50] = t23;\n        $[51] = t28;\n        $[52] = t33;\n        $[53] = t34;\n    } else {\n        t34 = $[53];\n    }\n    let t35;\n    if ($[54] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t35 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                className: \"text-2xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 218\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 122\n                    }, this),\n                    \"نظرة عامة على الأقسام\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 339,\n            columnNumber: 11\n        }, this);\n        $[54] = t35;\n    } else {\n        t35 = $[54];\n    }\n    let t36;\n    if ($[55] !== departments) {\n        t36 = departments.slice(0, 6).map(_temp6);\n        $[55] = departments;\n        $[56] = t36;\n    } else {\n        t36 = $[56];\n    }\n    let t37;\n    if ($[57] !== t36) {\n        t37 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2\",\n            children: t36\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 354,\n            columnNumber: 11\n        }, this);\n        $[57] = t36;\n        $[58] = t37;\n    } else {\n        t37 = $[58];\n    }\n    let t38;\n    if ($[59] !== departments.length) {\n        t38 = departments.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                variant: \"outline\",\n                className: \"shadow-soft hover-lift border-2 border-border/50\",\n                children: [\n                    \"عرض جميع الأقسام (\",\n                    departments.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 71\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 362,\n            columnNumber: 37\n        }, this);\n        $[59] = departments.length;\n        $[60] = t38;\n    } else {\n        t38 = $[60];\n    }\n    let t39;\n    if ($[61] !== t37 || $[62] !== t38) {\n        t39 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t35,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: [\n                            t37,\n                            t38\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 113\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 42\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 370,\n            columnNumber: 11\n        }, this);\n        $[61] = t37;\n        $[62] = t38;\n        $[63] = t39;\n    } else {\n        t39 = $[63];\n    }\n    let t40;\n    if ($[64] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t40 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                className: \"text-xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 216\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 121\n                    }, this),\n                    \"النشاط الأخير\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 379,\n            columnNumber: 11\n        }, this);\n        $[64] = t40;\n    } else {\n        t40 = $[64];\n    }\n    let t41;\n    if ($[65] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t41 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t40,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-spaced\",\n                            children: recentActivity.map(_temp7)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 100\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 87\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 386,\n                columnNumber: 16\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 386,\n            columnNumber: 11\n        }, this);\n        $[65] = t41;\n    } else {\n        t41 = $[65];\n    }\n    let t42;\n    if ($[66] !== t39) {\n        t42 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced lg:grid-cols-3\",\n            children: [\n                t39,\n                t41\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 393,\n            columnNumber: 11\n        }, this);\n        $[66] = t39;\n        $[67] = t42;\n    } else {\n        t42 = $[67];\n    }\n    let t43;\n    if ($[68] !== departments || $[69] !== showAddEmployeeDialog) {\n        t43 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employees_add_employee_dialog__WEBPACK_IMPORTED_MODULE_8__.AddEmployeeDialog, {\n            open: showAddEmployeeDialog,\n            onOpenChange: setShowAddEmployeeDialog,\n            departments: departments\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 401,\n            columnNumber: 11\n        }, this);\n        $[68] = departments;\n        $[69] = showAddEmployeeDialog;\n        $[70] = t43;\n    } else {\n        t43 = $[70];\n    }\n    let t44;\n    if ($[71] !== t13 || $[72] !== t34 || $[73] !== t42 || $[74] !== t43) {\n        t44 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-spaced p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen\",\n            children: [\n                t13,\n                t34,\n                t42,\n                t43\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 410,\n            columnNumber: 11\n        }, this);\n        $[71] = t13;\n        $[72] = t34;\n        $[73] = t42;\n        $[74] = t43;\n        $[75] = t44;\n    } else {\n        t44 = $[75];\n    }\n    return t44;\n}\n_s(DashboardPage, \"FvMkKCMaUljcrAwuWBT3QV8UvcE=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_9__.useHRStore,\n        _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_10__.useApiData\n    ];\n});\n_c1 = DashboardPage;\nfunction _temp7(activity, index) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start rtl-space-x p-4 rounded-xl gradient-green-soft border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-white rounded-full\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 308\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 422,\n                columnNumber: 194\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 container-spaced mr-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-semibold text-foreground\",\n                        children: activity.action\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 409\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: activity.details\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 487\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground font-medium\",\n                        children: activity.time\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 558\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 422,\n                columnNumber: 363\n            }, this)\n        ]\n    }, index, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 422,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp6(department) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_7__.DepartmentCard, {\n        department: department,\n        onAddEmployee: ()=>{\n            console.log(\"Add employee to\", department.name);\n        },\n        onViewDetails: ()=>{\n            console.log(\"View details for\", department.name);\n        }\n    }, department.id, false, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 425,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp5(_, i) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-muted rounded w-20\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 bg-muted rounded\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 177\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 432,\n                columnNumber: 50\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-16 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 247\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-muted rounded w-24\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 297\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 432,\n                columnNumber: 234\n            }, this)\n        ]\n    }, i, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 432,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp4(sum, dept_0) {\n    var _dept_0_employees;\n    const employeeCount = ((_dept_0_employees = dept_0.employees) === null || _dept_0_employees === void 0 ? void 0 : _dept_0_employees.length) || 0;\n    return sum + employeeCount / dept_0.capacity * 100;\n}\nfunction _temp3(row) {\n    return row.join(\",\");\n}\nfunction _temp2(dept) {\n    var _dept_employees, _dept_employees1;\n    return [\n        dept.name,\n        ((_dept_employees = dept.employees) === null || _dept_employees === void 0 ? void 0 : _dept_employees.length) || 0,\n        dept.capacity,\n        \"\".concat(Math.round((((_dept_employees1 = dept.employees) === null || _dept_employees1 === void 0 ? void 0 : _dept_employees1.length) || 0) / dept.capacity * 100), \"%\")\n    ];\n}\nfunction _temp(resolve) {\n    return setTimeout(resolve, 500);\n}\nvar _c1;\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRzpcXEF1Z21lbnQgY29kZVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ })

});
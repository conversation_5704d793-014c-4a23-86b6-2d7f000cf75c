{"name": "hr-synergy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^5.6.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "aws-sdk": "^2.1692.0", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "localforage": "^1.10.0", "lucide-react": "^0.294.0", "next": "^15.4.2", "next-auth": "^4.24.0", "papaparse": "^5.5.3", "prisma": "^5.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-virtuoso": "^4.6.0", "socket.io-client": "^4.8.1", "sonner": "^1.2.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "y-indexeddb": "^9.0.12", "y-websocket": "^1.5.4", "yjs": "^13.6.27", "zod": "^4.0.5", "zustand": "^4.4.0"}, "devDependencies": {"@types/localforage": "^0.0.33", "@types/node": "^20.0.0", "@types/papaparse": "^5.3.16", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "postcss": "^8.0.0", "typescript": "^5.0.0"}}
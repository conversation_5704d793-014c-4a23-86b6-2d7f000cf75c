"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/compiler-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/compiler-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/department-card */ \"(app-pages-browser)/./components/dashboard/department-card.tsx\");\n/* harmony import */ var _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/store/hr-store */ \"(app-pages-browser)/./lib/store/hr-store.ts\");\n/* harmony import */ var _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/use-api-data */ \"(app-pages-browser)/./lib/hooks/use-api-data.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage(t0) {\n    var _session_user;\n    _s();\n    const $ = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__.c)(65);\n    if ($[0] !== \"89f808b5fa244b2b497aa1f6af627a5506b9f97c151b343d94bd0aad7a6fe564\") {\n        for(let $i = 0; $i < 65; $i += 1){\n            $[$i] = Symbol.for(\"react.memo_cache_sentinel\");\n        }\n        $[0] = \"89f808b5fa244b2b497aa1f6af627a5506b9f97c151b343d94bd0aad7a6fe564\";\n    }\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { departments, employees, freeBucket } = (0,_lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [, setShowAddEmployeeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,_lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData)();\n    let t1;\n    let t2;\n    if ($[1] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t1 = ()=>{\n            const loadData = async ()=>{\n                ;\n                try {\n                    await new Promise(_temp);\n                    setIsLoading(false);\n                } catch (t3) {\n                    const error = t3;\n                    console.error(\"Failed to load dashboard data:\", error);\n                    setIsLoading(false);\n                }\n            };\n            loadData();\n        };\n        t2 = [];\n        $[1] = t1;\n        $[2] = t2;\n    } else {\n        t1 = $[1];\n        t2 = $[2];\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(t1, t2);\n    const totalEmployees = employees.length;\n    const totalDepartments = departments.length;\n    const freeBucketCount = freeBucket.length;\n    let t3;\n    if ($[3] !== departments) {\n        t3 = departments.length > 0 ? Math.round(departments.reduce(_temp2, 0) / departments.length) : 0;\n        $[3] = departments;\n        $[4] = t3;\n    } else {\n        t3 = $[4];\n    }\n    const averageUtilization = t3;\n    let t4;\n    if ($[5] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t4 = [\n            {\n                action: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0645\\u0648\\u0638\\u0641\",\n                details: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u0647\\u0646\\u062F\\u0633\\u0629\",\n                time: \"\\u0645\\u0646\\u0630 \\u0633\\u0627\\u0639\\u062A\\u064A\\u0646\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0638\\u0641 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0627\\u0646\\u0636\\u0645\\u062A \\u0641\\u0627\\u0637\\u0645\\u0629 \\u0623\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642\",\n                time: \"\\u0645\\u0646\\u0630 4 \\u0633\\u0627\\u0639\\u0627\\u062A\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0642\\u0633\\u0645 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0648\\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631\",\n                time: \"\\u0645\\u0646\\u0630 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F\"\n            }\n        ];\n        $[5] = t4;\n    } else {\n        t4 = $[5];\n    }\n    const recentActivity = t4;\n    if (isLoading) {\n        let t5;\n        if ($[6] === Symbol.for(\"react.memo_cache_sentinel\")) {\n            t5 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        ...Array(4)\n                    ].map(_temp3)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 39\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 12\n            }, this);\n            $[6] = t5;\n        } else {\n            t5 = $[6];\n        }\n        return t5;\n    }\n    let t5;\n    if ($[7] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t5 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-4xl font-bold gradient-primary bg-clip-text text-transparent\",\n            children: \"لوحة التحكم\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 10\n        }, this);\n        $[7] = t5;\n    } else {\n        t5 = $[7];\n    }\n    const t6 = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name;\n    let t7;\n    if ($[8] !== t6) {\n        t7 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                t5,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground\",\n                    children: [\n                        \"أهلاً بك، \",\n                        t6,\n                        \". إليك ما يحدث في مؤسستك.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 41\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 10\n        }, this);\n        $[8] = t6;\n        $[9] = t7;\n    } else {\n        t7 = $[9];\n    }\n    let t8;\n    if ($[10] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t8 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            variant: \"outline\",\n            className: \"shadow-soft hover-lift border-2 border-border/50\",\n            onClick: handleExportReport,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 ml-2\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 126\n                }, this),\n                \"تصدير التقرير\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 10\n        }, this);\n        $[10] = t8;\n    } else {\n        t8 = $[10];\n    }\n    let t9;\n    if ($[11] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t9 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3\",\n            children: [\n                t8,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    className: \"gradient-primary hover:opacity-90 shadow-medium hover-lift\",\n                    onClick: ()=>setShowAddEmployeeDialog(true),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4 ml-2\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 168\n                        }, this),\n                        \"إضافة موظف\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 42\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 10\n        }, this);\n        $[11] = t9;\n    } else {\n        t9 = $[11];\n    }\n    let t10;\n    if ($[12] !== t7) {\n        t10 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                t7,\n                t9\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 11\n        }, this);\n        $[12] = t7;\n        $[13] = t10;\n    } else {\n        t10 = $[13];\n    }\n    let t11;\n    if ($[14] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t11 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-primary\",\n            children: \"إجمالي الموظفين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, this);\n        $[14] = t11;\n    } else {\n        t11 = $[14];\n    }\n    let t12;\n    if ($[15] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t12 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t11,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 196\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 11\n        }, this);\n        $[15] = t12;\n    } else {\n        t12 = $[15];\n    }\n    let t13;\n    if ($[16] !== totalEmployees) {\n        t13 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-primary\",\n            children: totalEmployees\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 11\n        }, this);\n        $[16] = totalEmployees;\n        $[17] = t13;\n    } else {\n        t13 = $[17];\n    }\n    let t14;\n    if ($[18] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t14 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-primary/70 font-medium mt-1\",\n            children: \"+12% من الشهر الماضي\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 11\n        }, this);\n        $[18] = t14;\n    } else {\n        t14 = $[18];\n    }\n    let t15;\n    if ($[19] !== t13) {\n        t15 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-primary\",\n            children: [\n                t12,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t13,\n                        t14\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 11\n        }, this);\n        $[19] = t13;\n        $[20] = t15;\n    } else {\n        t15 = $[20];\n    }\n    let t16;\n    if ($[21] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t16 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-secondary\",\n            children: \"الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 11\n        }, this);\n        $[21] = t16;\n    } else {\n        t16 = $[21];\n    }\n    let t17;\n    if ($[22] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t17 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t16,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-secondary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 192\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 11\n        }, this);\n        $[22] = t17;\n    } else {\n        t17 = $[22];\n    }\n    let t18;\n    if ($[23] !== totalDepartments) {\n        t18 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-secondary\",\n            children: totalDepartments\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, this);\n        $[23] = totalDepartments;\n        $[24] = t18;\n    } else {\n        t18 = $[24];\n    }\n    let t19;\n    if ($[25] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t19 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-secondary/70 font-medium mt-1\",\n            children: \"الأقسام النشطة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 11\n        }, this);\n        $[25] = t19;\n    } else {\n        t19 = $[25];\n    }\n    let t20;\n    if ($[26] !== t18) {\n        t20 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-secondary\",\n            children: [\n                t17,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t18,\n                        t19\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 168\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 11\n        }, this);\n        $[26] = t18;\n        $[27] = t20;\n    } else {\n        t20 = $[27];\n    }\n    let t21;\n    if ($[28] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t21 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-accent\",\n            children: \"السلة المؤقتة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 11\n        }, this);\n        $[28] = t21;\n    } else {\n        t21 = $[28];\n    }\n    let t22;\n    if ($[29] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t22 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t21,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-accent rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 189\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 219,\n            columnNumber: 11\n        }, this);\n        $[29] = t22;\n    } else {\n        t22 = $[29];\n    }\n    let t23;\n    if ($[30] !== freeBucketCount) {\n        t23 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-accent\",\n            children: freeBucketCount\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 11\n        }, this);\n        $[30] = freeBucketCount;\n        $[31] = t23;\n    } else {\n        t23 = $[31];\n    }\n    let t24;\n    if ($[32] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t24 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-accent/70 font-medium mt-1\",\n            children: \"موظفون غير مخصصين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 11\n        }, this);\n        $[32] = t24;\n    } else {\n        t24 = $[32];\n    }\n    let t25;\n    if ($[33] !== t23) {\n        t25 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-accent\",\n            children: [\n                t22,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t23,\n                        t24\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 165\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 11\n        }, this);\n        $[33] = t23;\n        $[34] = t25;\n    } else {\n        t25 = $[34];\n    }\n    let t26;\n    if ($[35] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t26 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-success\",\n            children: \"متوسط الاستخدام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 11\n        }, this);\n        $[35] = t26;\n    } else {\n        t26 = $[35];\n    }\n    let t27;\n    if ($[36] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t27 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t26,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-success rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 190\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 11\n        }, this);\n        $[36] = t27;\n    } else {\n        t27 = $[36];\n    }\n    let t28;\n    if ($[37] !== averageUtilization) {\n        t28 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-success\",\n            children: [\n                averageUtilization,\n                \"%\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 263,\n            columnNumber: 11\n        }, this);\n        $[37] = averageUtilization;\n        $[38] = t28;\n    } else {\n        t28 = $[38];\n    }\n    let t29;\n    if ($[39] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t29 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-success/70 font-medium mt-1\",\n            children: \"سعة الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 271,\n            columnNumber: 11\n        }, this);\n        $[39] = t29;\n    } else {\n        t29 = $[39];\n    }\n    let t30;\n    if ($[40] !== t28) {\n        t30 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-success\",\n            children: [\n                t27,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t28,\n                        t29\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 278,\n            columnNumber: 11\n        }, this);\n        $[40] = t28;\n        $[41] = t30;\n    } else {\n        t30 = $[41];\n    }\n    let t31;\n    if ($[42] !== t15 || $[43] !== t20 || $[44] !== t25 || $[45] !== t30) {\n        t31 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                t15,\n                t20,\n                t25,\n                t30\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 286,\n            columnNumber: 11\n        }, this);\n        $[42] = t15;\n        $[43] = t20;\n        $[44] = t25;\n        $[45] = t30;\n        $[46] = t31;\n    } else {\n        t31 = $[46];\n    }\n    let t32;\n    if ($[47] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t32 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-2xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 218\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 122\n                    }, this),\n                    \"نظرة عامة على الأقسام\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 297,\n            columnNumber: 11\n        }, this);\n        $[47] = t32;\n    } else {\n        t32 = $[47];\n    }\n    let t33;\n    if ($[48] !== departments) {\n        t33 = departments.slice(0, 6).map(_temp4);\n        $[48] = departments;\n        $[49] = t33;\n    } else {\n        t33 = $[49];\n    }\n    let t34;\n    if ($[50] !== t33) {\n        t34 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2\",\n            children: t33\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 312,\n            columnNumber: 11\n        }, this);\n        $[50] = t33;\n        $[51] = t34;\n    } else {\n        t34 = $[51];\n    }\n    let t35;\n    if ($[52] !== departments.length) {\n        t35 = departments.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                variant: \"outline\",\n                className: \"shadow-soft hover-lift border-2 border-border/50\",\n                children: [\n                    \"عرض جميع الأقسام (\",\n                    departments.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 71\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 37\n        }, this);\n        $[52] = departments.length;\n        $[53] = t35;\n    } else {\n        t35 = $[53];\n    }\n    let t36;\n    if ($[54] !== t34 || $[55] !== t35) {\n        t36 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t32,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            t34,\n                            t35\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 113\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 42\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 11\n        }, this);\n        $[54] = t34;\n        $[55] = t35;\n        $[56] = t36;\n    } else {\n        t36 = $[56];\n    }\n    let t37;\n    if ($[57] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t37 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 216\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 121\n                    }, this),\n                    \"النشاط الأخير\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 11\n        }, this);\n        $[57] = t37;\n    } else {\n        t37 = $[57];\n    }\n    let t38;\n    if ($[58] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t38 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t37,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-spaced\",\n                            children: recentActivity.map(_temp5)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 100\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 87\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 16\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 344,\n            columnNumber: 11\n        }, this);\n        $[58] = t38;\n    } else {\n        t38 = $[58];\n    }\n    let t39;\n    if ($[59] !== t36) {\n        t39 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced lg:grid-cols-3\",\n            children: [\n                t36,\n                t38\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 11\n        }, this);\n        $[59] = t36;\n        $[60] = t39;\n    } else {\n        t39 = $[60];\n    }\n    let t40;\n    if ($[61] !== t10 || $[62] !== t31 || $[63] !== t39) {\n        t40 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-spaced p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen\",\n            children: [\n                t10,\n                t31,\n                t39\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 11\n        }, this);\n        $[61] = t10;\n        $[62] = t31;\n        $[63] = t39;\n        $[64] = t40;\n    } else {\n        t40 = $[64];\n    }\n    return t40;\n}\n_s(DashboardPage, \"QHGLOyGxaFwZ2BhKJTmW9LdK1Ug=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore,\n        _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData\n    ];\n});\n_c1 = DashboardPage;\nfunction _temp5(activity, index) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start rtl-space-x p-4 rounded-xl gradient-green-soft border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-white rounded-full\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 308\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 194\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 container-spaced mr-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-semibold text-foreground\",\n                        children: activity.action\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 409\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: activity.details\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 487\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground font-medium\",\n                        children: activity.time\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 558\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 363\n            }, this)\n        ]\n    }, index, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 370,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp4(department) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__.DepartmentCard, {\n        department: department,\n        onAddEmployee: ()=>{\n            console.log(\"Add employee to\", department.name);\n        },\n        onViewDetails: ()=>{\n            console.log(\"View details for\", department.name);\n        }\n    }, department.id, false, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 373,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp3(_, i) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-muted rounded w-20\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 bg-muted rounded\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 177\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 50\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-16 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 247\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-muted rounded w-24\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 297\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 234\n            }, this)\n        ]\n    }, i, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 380,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp2(sum, dept) {\n    var _dept_employees;\n    const employeeCount = ((_dept_employees = dept.employees) === null || _dept_employees === void 0 ? void 0 : _dept_employees.length) || 0;\n    return sum + employeeCount / dept.capacity * 100;\n}\nfunction _temp(resolve) {\n    return setTimeout(resolve, 500);\n}\nvar _c1;\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC8oZGFzaGJvYXJkKS9kYXNoYm9hcmQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQVk7QUFFK0I7QUFDQztBQUNtQztBQUNoQztBQUN3QjtBQUV0QjtBQUNJO0FBU2hDO0FBT04sdUJBQUFxQixFQUFBOzs7SUFBQSxNQUFBQyxDQUFBLEdBQUFyQix5REFBQTtJQUFBLElBQUFxQixDQUFBO1FBQUEsUUFBQUMsRUFBQSxNQUFBQSxFQUFBLE9BQUFBLEVBQUE7WUFBQUQsQ0FBQSxDQUFBQyxFQUFBLElBQUFDLE1BQUEsQ0FBQUMsR0FBQTtRQUFBO1FBQUFILENBQUE7SUFBQTtJQUNiLFFBQUFJLElBQUEsRUFBQUMsT0FBQUEsRUFBQTtJQUNBLFFBQUFDLFdBQUEsRUFBQUMsU0FBQSxFQUFBQyxVQUFBQSxFQUFBO0lBQ0EsT0FBQUMsU0FBQSxFQUFBQyxZQUFBLElBQWtDN0IsK0NBQUEsS0FBYSxDQUFDO0lBQ2hELFNBQUE4Qix3QkFBQSxJQUEwRDlCLCtDQUFBLE1BQWMsQ0FBQzs7SUFHN0QsSUFBQStCLEVBQUE7SUFBQSxJQUFBQyxFQUFBO0lBQUEsSUFBQWIsQ0FBQSxRQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFFRlMsRUFBQSxHQUFBQSxDQUFBO1lBRVIsTUFBQUUsUUFBQSxTQUFBQSxDQUFBOztnQkFBQTtvQkFBQSxVQUFBQyxPQUFBLENBQUFDLEtBQUE7b0JBSUlOLFlBQVksTUFBTSxDQUFDO2dCQUFBLFNBQUFPLEVBQUE7b0JBQ1pDLEtBQUEsQ0FBQUEsS0FBQSxDQUFBQSxDQUFBLENBQUFBLEVBQUs7b0JBQ1pDLE9BQUEsQ0FBQUQsS0FBQSxDQUFjLGdDQUFnQyxFQUFFQSxLQUFLLENBQUM7b0JBQ3REUixZQUFZLE1BQU0sQ0FBQztnQkFBQTtZQUFBO1lBSXZCSSxRQUFRLENBQUMsQ0FBQztRQUFBO1FBQ1RELEVBQUE7UUFBRWIsQ0FBQSxNQUFBWSxFQUFBO1FBQUFaLENBQUEsTUFBQWEsRUFBQTtJQUFBO1FBQUFELEVBQUEsR0FBQVosQ0FBQTtRQUFBYSxFQUFBLEdBQUFiLENBQUE7SUFBQTtJQWRMcEIsZ0RBQUEsQ0FBVWdDLEVBY1QsRUFBRUMsRUFBRSxDQUFDO0lBRU4sTUFBQU8sY0FBQSxHQUF1QmIsU0FBUyxDQUFBYyxNQUFBO0lBQ2hDLE1BQUFDLGdCQUFBLEdBQXlCaEIsV0FBVyxDQUFBZSxNQUFBO0lBQ3BDLE1BQUFFLGVBQUEsR0FBd0JmLFVBQVUsQ0FBQWEsTUFBQTtJQUFPLElBQUFKLEVBQUE7SUFBQSxJQUFBakIsQ0FBQSxRQUFBTSxXQUFBO1FBQ2RXLEVBQUEsR0FBQVgsV0FBVyxDQUFBZSxNQUFBLElBQVcsR0FDN0NHLElBQUEsQ0FBQUMsS0FBQSxDQUNFbkIsV0FBVyxDQUFBb0IsTUFBQSxDQUFBQyxNQUFBLEdBR1AsQ0FBQyxHQUFHckIsV0FBVyxDQUFBZSxNQUNyQixDQUFDLElBQ0E7UUFBQXJCLENBQUEsTUFBQU0sV0FBQTtRQUFBTixDQUFBLE1BQUFpQixFQUFBO0lBQUE7UUFBQUEsRUFBQSxHQUFBakIsQ0FBQTtJQUFBO0lBUEwsTUFBQTRCLGtCQUFBLEdBQTJCWCxFQU90QjtJQUFBLElBQUFZLEVBQUE7SUFBQSxJQUFBN0IsQ0FBQSxRQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFFa0IwQixFQUFBO1lBQUE7Z0JBQUFDLE1BQUEsRUFDWCwwREFBYTtnQkFBQUMsT0FBQSxFQUFXLG9LQUFrQztnQkFBQUMsSUFBQSxFQUFRO1lBQVk7WUFBQTtnQkFBQUYsTUFBQSxFQUM5RSwrRkFBb0I7Z0JBQUFDLE9BQUEsRUFBVyx5S0FBa0M7Z0JBQUFDLElBQUEsRUFBUTtZQUFhO1lBQUE7Z0JBQUFGLE1BQUEsRUFDdEYseUZBQW1CO2dCQUFBQyxPQUFBLEVBQVcsb0dBQW9CO2dCQUFBQyxJQUFBLEVBQVE7WUFBYztTQUFBO1FBQ25GaEMsQ0FBQSxNQUFBNkIsRUFBQTtJQUFBO1FBQUFBLEVBQUEsR0FBQTdCLENBQUE7SUFBQTtJQUpELE1BQUFpQyxjQUFBLEdBQXVCSixFQUl0QjtJQUFBLElBRUdwQixTQUFTO1FBQUEsSUFBQXlCLEVBQUE7UUFBQSxJQUFBbEMsQ0FBQSxRQUFBRSxNQUFBLENBQUFDLEdBQUE7WUFFVCtCLEVBQUEsa0ZBZU07Z0JBZlMsU0FBVyxFQUFYLFdBQVc7d0NBQ3hCLGlFQWFNO29CQWJTLFNBQTBDLEVBQTFDLDBDQUEwQyxDQUN0RDs4QkFBQTsyQkFBSUMsS0FBQSxFQUFPLENBQUM7cUJBQUEsQ0FBQUMsR0FBQSxDQUFBQyxNQVdaOzs7Ozs7Ozs7OztZQUVDckMsQ0FBQSxNQUFBa0MsRUFBQTtRQUFBO1lBQUFBLEVBQUEsR0FBQWxDLENBQUE7UUFBQTtRQUFBLE9BZk5rQyxFQWVNO0lBQUE7SUFBQSxJQUFBQSxFQUFBO0lBQUEsSUFBQWxDLENBQUEsUUFBQUUsTUFBQSxDQUFBQyxHQUFBO1FBU0YrQixFQUFBLGlGQUVLO1lBRlMsU0FBbUUsRUFBbkUsbUVBQW1FO3NCQUFDLFdBRWxGLEVBRkE7Ozs7OztRQUVLbEMsQ0FBQSxNQUFBa0MsRUFBQTtJQUFBO1FBQUFBLEVBQUEsR0FBQWxDLENBQUE7SUFBQTtJQUVRLE1BQUFzQyxFQUFBLHNEQUFBakMsT0FBTyxpQkFBQWtDLElBQUEsZ0VBQUFDLElBQUE7SUFBWSxJQUFBQyxFQUFBO0lBQUEsSUFBQXpDLENBQUEsUUFBQXNDLEVBQUE7UUFMbENHLEVBQUEsa0ZBT007WUFQUyxTQUFXLEVBQVgsV0FBVyxDQUN4Qjs7Z0JBQUFQLEVBRUk7OEJBQ0osK0RBRUk7b0JBRlMsU0FBK0IsRUFBL0IsK0JBQStCOzt3QkFBQyxVQUNoQzt3QkFBQUksRUFBa0I7d0JBQUUseUJBQ2pDLEVBRkE7Ozs7Ozs7Ozs7Ozs7UUFHSXRDLENBQUEsTUFBQXNDLEVBQUE7UUFBQXRDLENBQUEsTUFBQXlDLEVBQUE7SUFBQTtRQUFBQSxFQUFBLEdBQUF6QyxDQUFBO0lBQUE7SUFBQSxJQUFBMEMsRUFBQTtJQUFBLElBQUExQyxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQUdKdUMsRUFBQSwrRUFBQyx5REFBTTtZQUNHLE9BQVMsRUFBVCxTQUFTO1lBQ1AsU0FBa0QsRUFBbEQsa0RBQWtEO1lBQ25EQyxPQUFrQixDQUFsQkEsQ0FBQUEsa0JBQWlCQSxDQUFDOzs4QkFFM0IsOERBQUMseUlBQVE7b0JBQVcsU0FBYyxFQUFkLGNBQWM7Ozs7OztnQkFBRyxhQUV2QyxFQVBDOzs7Ozs7O1FBT1EzQyxDQUFBLE9BQUEwQyxFQUFBO0lBQUE7UUFBQUEsRUFBQSxHQUFBMUMsQ0FBQTtJQUFBO0lBQUEsSUFBQTRDLEVBQUE7SUFBQSxJQUFBNUMsQ0FBQSxTQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFSWHlDLEVBQUEsa0ZBZ0JNO1lBaEJTLFNBQVksRUFBWixZQUFZLENBQ3pCOztnQkFBQUYsRUFPUTs4QkFDUiw4REFBQyx5REFBTTtvQkFDSyxTQUE0RCxFQUE1RCw0REFBNEQ7b0JBQzdELE9BQW9DLENBQXBDLEtBQU0vQix3QkFBd0IsS0FBSyxFQUFDOztzQ0FFN0MsOERBQUMsMElBQVE7NEJBQVcsU0FBYyxFQUFkLGNBQWM7Ozs7Ozt3QkFBRyxVQUV2QyxFQU5DOzs7Ozs7Ozs7Ozs7O1FBT0dYLENBQUEsT0FBQTRDLEVBQUE7SUFBQTtRQUFBQSxFQUFBLEdBQUE1QyxDQUFBO0lBQUE7SUFBQSxJQUFBNkMsR0FBQTtJQUFBLElBQUE3QyxDQUFBLFNBQUF5QyxFQUFBO1FBMUJSSSxHQUFBLGtGQTJCTTtZQTNCUyxTQUFtQyxFQUFuQyxtQ0FBbUMsQ0FDaEQ7O2dCQUFBSixFQU9LLENBRUw7Z0JBQUFHLEVBZ0JLOzs7Ozs7O1FBQ0Q1QyxDQUFBLE9BQUF5QyxFQUFBO1FBQUF6QyxDQUFBLE9BQUE2QyxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBN0MsQ0FBQTtJQUFBO0lBQUEsSUFBQThDLEdBQUE7SUFBQSxJQUFBOUMsQ0FBQSxTQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFNQTJDLEdBQUEsK0VBQUMsMERBQVM7WUFBVyxTQUFvQyxFQUFwQyxvQ0FBb0M7c0JBQUMsZUFBZSxFQUF4RTs7Ozs7O1FBQW9GOUMsQ0FBQSxPQUFBOEMsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQTlDLENBQUE7SUFBQTtJQUFBLElBQUErQyxHQUFBO0lBQUEsSUFBQS9DLENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBRHZGNEMsR0FBQSwrRUFBQywyREFBVTtZQUFXLFNBQTJELEVBQTNELDJEQUEyRCxDQUMvRTs7Z0JBQUFELEdBQW9GOzhCQUNwRixpRUFFTTtvQkFGUyxTQUFrRixFQUFsRixrRkFBa0Y7NENBQy9GLDhEQUFDLDBJQUFLO3dCQUFXLFNBQW9CLEVBQXBCLG9CQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFFNUI5QyxDQUFBLE9BQUErQyxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBL0MsQ0FBQTtJQUFBO0lBQUEsSUFBQWdELEdBQUE7SUFBQSxJQUFBaEQsQ0FBQSxTQUFBb0IsY0FBQTtRQUVYNEIsR0FBQSxrRkFBdUU7WUFBeEQsU0FBaUMsRUFBakMsaUNBQWlDLENBQUU1QjtzQkFBQUEsY0FBYTs7Ozs7O1FBQVFwQixDQUFBLE9BQUFvQixjQUFBO1FBQUFwQixDQUFBLE9BQUFnRCxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBaEQsQ0FBQTtJQUFBO0lBQUEsSUFBQWlELEdBQUE7SUFBQSxJQUFBakQsQ0FBQSxTQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFDdkU4QyxHQUFBLGdGQUVJO1lBRlMsU0FBMEMsRUFBMUMsMENBQTBDO3NCQUFDLG9CQUV4RCxFQUZBOzs7Ozs7UUFFSWpELENBQUEsT0FBQWlELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUFqRCxDQUFBO0lBQUE7SUFBQSxJQUFBa0QsR0FBQTtJQUFBLElBQUFsRCxDQUFBLFNBQUFnRCxHQUFBO1FBWFJFLEdBQUEsK0VBQUMscURBQUk7WUFBVyxTQUFxSSxFQUFySSxxSUFBcUksQ0FDbko7O2dCQUFBSCxHQUtZOzhCQUNaLDhEQUFDLDREQUFXLENBQ1Y7O3dCQUFBQyxHQUFzRSxDQUN0RTt3QkFBQUMsR0FFRzs7Ozs7Ozs7Ozs7OztRQUVBakQsQ0FBQSxPQUFBZ0QsR0FBQTtRQUFBaEQsQ0FBQSxPQUFBa0QsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQWxELENBQUE7SUFBQTtJQUFBLElBQUFtRCxHQUFBO0lBQUEsSUFBQW5ELENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBSUhnRCxHQUFBLCtFQUFDLDBEQUFTO1lBQVcsU0FBc0MsRUFBdEMsc0NBQXNDO3NCQUFDLE9BQU8sRUFBbEU7Ozs7OztRQUE4RW5ELENBQUEsT0FBQW1ELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUFuRCxDQUFBO0lBQUE7SUFBQSxJQUFBb0QsR0FBQTtJQUFBLElBQUFwRCxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQURqRmlELEdBQUEsK0VBQUMsMkRBQVU7WUFBVyxTQUEyRCxFQUEzRCwyREFBMkQsQ0FDL0U7O2dCQUFBRCxHQUE4RTs4QkFDOUUsaUVBRU07b0JBRlMsU0FBOEUsRUFBOUUsOEVBQThFOzRDQUMzRiw4REFBQywwSUFBUzt3QkFBVyxTQUFvQixFQUFwQixvQkFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBRWhDbkQsQ0FBQSxPQUFBb0QsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXBELENBQUE7SUFBQTtJQUFBLElBQUFxRCxHQUFBO0lBQUEsSUFBQXJELENBQUEsU0FBQXNCLGdCQUFBO1FBRVgrQixHQUFBLGtGQUEyRTtZQUE1RCxTQUFtQyxFQUFuQyxtQ0FBbUMsQ0FBRS9CO3NCQUFBQSxnQkFBZTs7Ozs7O1FBQVF0QixDQUFBLE9BQUFzQixnQkFBQTtRQUFBdEIsQ0FBQSxPQUFBcUQsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXJELENBQUE7SUFBQTtJQUFBLElBQUFzRCxHQUFBO0lBQUEsSUFBQXRELENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBQzNFbUQsR0FBQSxnRkFFSTtZQUZTLFNBQTRDLEVBQTVDLDRDQUE0QztzQkFBQyxjQUUxRCxFQUZBOzs7Ozs7UUFFSXRELENBQUEsT0FBQXNELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUF0RCxDQUFBO0lBQUE7SUFBQSxJQUFBdUQsR0FBQTtJQUFBLElBQUF2RCxDQUFBLFNBQUFxRCxHQUFBO1FBWFJFLEdBQUEsK0VBQUMscURBQUk7WUFBVyxTQUF1SSxFQUF2SSx1SUFBdUksQ0FDcko7O2dCQUFBSCxHQUtZOzhCQUNaLDhEQUFDLDREQUFXLENBQ1Y7O3dCQUFBQyxHQUEwRSxDQUMxRTt3QkFBQUMsR0FFRzs7Ozs7Ozs7Ozs7OztRQUVBdEQsQ0FBQSxPQUFBcUQsR0FBQTtRQUFBckQsQ0FBQSxPQUFBdUQsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXZELENBQUE7SUFBQTtJQUFBLElBQUF3RCxHQUFBO0lBQUEsSUFBQXhELENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBSUhxRCxHQUFBLCtFQUFDLDBEQUFTO1lBQVcsU0FBbUMsRUFBbkMsbUNBQW1DO3NCQUFDLGFBQWEsRUFBckU7Ozs7OztRQUFpRnhELENBQUEsT0FBQXdELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUF4RCxDQUFBO0lBQUE7SUFBQSxJQUFBeUQsR0FBQTtJQUFBLElBQUF6RCxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQURwRnNELEdBQUEsK0VBQUMsMkRBQVU7WUFBVyxTQUEyRCxFQUEzRCwyREFBMkQsQ0FDL0U7O2dCQUFBRCxHQUFpRjs4QkFDakYsaUVBRU07b0JBRlMsU0FBMkUsRUFBM0UsMkVBQTJFOzRDQUN4Riw4REFBQywwSUFBTzt3QkFBVyxTQUFvQixFQUFwQixvQkFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBRTlCeEQsQ0FBQSxPQUFBeUQsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXpELENBQUE7SUFBQTtJQUFBLElBQUEwRCxHQUFBO0lBQUEsSUFBQTFELENBQUEsU0FBQXVCLGVBQUE7UUFFWG1DLEdBQUEsa0ZBQXVFO1lBQXhELFNBQWdDLEVBQWhDLGdDQUFnQyxDQUFFbkM7c0JBQUFBLGVBQWM7Ozs7OztRQUFRdkIsQ0FBQSxPQUFBdUIsZUFBQTtRQUFBdkIsQ0FBQSxPQUFBMEQsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQTFELENBQUE7SUFBQTtJQUFBLElBQUEyRCxHQUFBO0lBQUEsSUFBQTNELENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBQ3ZFd0QsR0FBQSxnRkFFSTtZQUZTLFNBQXlDLEVBQXpDLHlDQUF5QztzQkFBQyxpQkFFdkQsRUFGQTs7Ozs7O1FBRUkzRCxDQUFBLE9BQUEyRCxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBM0QsQ0FBQTtJQUFBO0lBQUEsSUFBQTRELEdBQUE7SUFBQSxJQUFBNUQsQ0FBQSxTQUFBMEQsR0FBQTtRQVhSRSxHQUFBLCtFQUFDLHFEQUFJO1lBQVcsU0FBb0ksRUFBcEksb0lBQW9JLENBQ2xKOztnQkFBQUgsR0FLWTs4QkFDWiw4REFBQyw0REFBVyxDQUNWOzt3QkFBQUMsR0FBc0UsQ0FDdEU7d0JBQUFDLEdBRUc7Ozs7Ozs7Ozs7Ozs7UUFFQTNELENBQUEsT0FBQTBELEdBQUE7UUFBQTFELENBQUEsT0FBQTRELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUE1RCxDQUFBO0lBQUE7SUFBQSxJQUFBNkQsR0FBQTtJQUFBLElBQUE3RCxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQUlIMEQsR0FBQSwrRUFBQywwREFBUztZQUFXLFNBQW9DLEVBQXBDLG9DQUFvQztzQkFBQyxlQUFlLEVBQXhFOzs7Ozs7UUFBb0Y3RCxDQUFBLE9BQUE2RCxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBN0QsQ0FBQTtJQUFBO0lBQUEsSUFBQThELEdBQUE7SUFBQSxJQUFBOUQsQ0FBQSxTQUFBRSxNQUFBLENBQUFDLEdBQUE7UUFEdkYyRCxHQUFBLCtFQUFDLDJEQUFVO1lBQVcsU0FBMkQsRUFBM0QsMkRBQTJELENBQy9FOztnQkFBQUQsR0FBb0Y7OEJBQ3BGLGlFQUVNO29CQUZTLFNBQTRFLEVBQTVFLDRFQUE0RTs0Q0FDekYsOERBQUMsMElBQVU7d0JBQVcsU0FBb0IsRUFBcEIsb0JBQW9COzs7Ozs7Ozs7Ozs7Ozs7OztRQUVqQzdELENBQUEsT0FBQThELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUE5RCxDQUFBO0lBQUE7SUFBQSxJQUFBK0QsR0FBQTtJQUFBLElBQUEvRCxDQUFBLFNBQUE0QixrQkFBQTtRQUVYbUMsR0FBQSxrRkFBNEU7WUFBN0QsU0FBaUMsRUFBakMsaUNBQWlDLENBQUVuQzs7Z0JBQUFBLGtCQUFpQjtnQkFBRSxDQUFDLEVBQXRFOzs7Ozs7O1FBQTRFNUIsQ0FBQSxPQUFBNEIsa0JBQUE7UUFBQTVCLENBQUEsT0FBQStELEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUEvRCxDQUFBO0lBQUE7SUFBQSxJQUFBZ0UsR0FBQTtJQUFBLElBQUFoRSxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQUM1RTZELEdBQUEsZ0ZBRUk7WUFGUyxTQUEwQyxFQUExQywwQ0FBMEM7c0JBQUMsV0FFeEQsRUFGQTs7Ozs7O1FBRUloRSxDQUFBLE9BQUFnRSxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBaEUsQ0FBQTtJQUFBO0lBQUEsSUFBQWlFLEdBQUE7SUFBQSxJQUFBakUsQ0FBQSxTQUFBK0QsR0FBQTtRQVhSRSxHQUFBLCtFQUFDLHFEQUFJO1lBQVcsU0FBcUksRUFBckkscUlBQXFJLENBQ25KOztnQkFBQUgsR0FLWTs4QkFDWiw4REFBQyw0REFBVyxDQUNWOzt3QkFBQUMsR0FBMkUsQ0FDM0U7d0JBQUFDLEdBRUc7Ozs7Ozs7Ozs7Ozs7UUFFQWhFLENBQUEsT0FBQStELEdBQUE7UUFBQS9ELENBQUEsT0FBQWlFLEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUFqRSxDQUFBO0lBQUE7SUFBQSxJQUFBa0UsR0FBQTtJQUFBLElBQUFsRSxDQUFBLFNBQUFrRCxHQUFBLElBQUFsRCxDQUFBLFNBQUF1RCxHQUFBLElBQUF2RCxDQUFBLFNBQUE0RCxHQUFBLElBQUE1RCxDQUFBLFNBQUFpRSxHQUFBO1FBM0RUQyxHQUFBLGtGQTRETTtZQTVEUyxTQUEyQyxFQUEzQywyQ0FBMkMsQ0FDeEQ7O2dCQUFBaEIsR0FhTSxDQUVOO2dCQUFBSyxHQWFNLENBRU47Z0JBQUFLLEdBYU0sQ0FFTjtnQkFBQUssR0FhTTs7Ozs7OztRQUNGakUsQ0FBQSxPQUFBa0QsR0FBQTtRQUFBbEQsQ0FBQSxPQUFBdUQsR0FBQTtRQUFBdkQsQ0FBQSxPQUFBNEQsR0FBQTtRQUFBNUQsQ0FBQSxPQUFBaUUsR0FBQTtRQUFBakUsQ0FBQSxPQUFBa0UsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQWxFLENBQUE7SUFBQTtJQUFBLElBQUFtRSxHQUFBO0lBQUEsSUFBQW5FLENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBTUFnRSxHQUFBLCtFQUFDLDJEQUFVO1lBQVcsU0FBTSxFQUFOLE1BQU07b0NBQzFCLDhEQUFDLDBEQUFTO2dCQUFXLFNBQTRELEVBQTVELDREQUE0RDs7a0NBQy9FLGlFQUVNO3dCQUZTLFNBQWdGLEVBQWhGLGdGQUFnRjtnREFDN0YsOERBQUMsMElBQVM7NEJBQVcsU0FBb0IsRUFBcEIsb0JBQW9COzs7Ozs7Ozs7OztvQkFDckMscUJBRVIsRUFMQzs7Ozs7Ozs7Ozs7O1FBTVVuRSxDQUFBLE9BQUFtRSxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBbkUsQ0FBQTtJQUFBO0lBQUEsSUFBQW9FLEdBQUE7SUFBQSxJQUFBcEUsQ0FBQSxTQUFBTSxXQUFBO1FBR1I4RCxHQUFBLEdBQUE5RCxXQUFXLENBQUErRCxLQUFBLEtBQVcsQ0FBQyxDQUFBakMsR0FBQSxDQUFBa0MsTUFhdkIsQ0FBQztRQUFBdEUsQ0FBQSxPQUFBTSxXQUFBO1FBQUFOLENBQUEsT0FBQW9FLEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUFwRSxDQUFBO0lBQUE7SUFBQSxJQUFBdUUsR0FBQTtJQUFBLElBQUF2RSxDQUFBLFNBQUFvRSxHQUFBO1FBZEpHLEdBQUEsa0ZBZU07WUFmUyxTQUE0QixFQUE1Qiw0QkFBNEIsQ0FDeEM7c0JBQUFILEdBYUE7Ozs7OztRQUNHcEUsQ0FBQSxPQUFBb0UsR0FBQTtRQUFBcEUsQ0FBQSxPQUFBdUUsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXZFLENBQUE7SUFBQTtJQUFBLElBQUF3RSxHQUFBO0lBQUEsSUFBQXhFLENBQUEsU0FBQU0sV0FBQSxDQUFBZSxNQUFBO1FBRUxtRCxHQUFBLEdBQUFsRSxXQUFXLENBQUFlLE1BQUEsSUFBVyxrQkFDckIsaUVBSU07WUFKUyxTQUFrQixFQUFsQixrQkFBa0I7b0NBQy9CLDhEQUFDLHlEQUFNO2dCQUFTLE9BQVMsRUFBVCxTQUFTO2dCQUFXLFNBQWtELEVBQWxELGtEQUFrRDs7b0JBQUMsa0JBQ2xFO29CQUFBZixXQUFXLENBQUFlLE1BQU07b0JBQUUsQ0FDeEMsRUFGQzs7Ozs7Ozs7Ozs7O1FBSUpyQixDQUFBLE9BQUFNLFdBQUEsQ0FBQWUsTUFBQTtRQUFBckIsQ0FBQSxPQUFBd0UsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXhFLENBQUE7SUFBQTtJQUFBLElBQUF5RSxHQUFBO0lBQUEsSUFBQXpFLENBQUEsU0FBQXVFLEdBQUEsSUFBQXZFLENBQUEsU0FBQXdFLEdBQUE7UUFsQ1BDLEdBQUEsa0ZBcUNNO1lBckNTLFNBQWUsRUFBZixlQUFlO29DQUM1Qiw4REFBQyxxREFBSTtnQkFBVyxTQUFpRCxFQUFqRCxpREFBaUQsQ0FDL0Q7O29CQUFBTixHQU9ZO2tDQUNaLDhEQUFDLDREQUFXLENBQ1Y7OzRCQUFBSSxHQWVLLENBRUo7NEJBQUFDLEdBTUQ7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQUdBeEUsQ0FBQSxPQUFBdUUsR0FBQTtRQUFBdkUsQ0FBQSxPQUFBd0UsR0FBQTtRQUFBeEUsQ0FBQSxPQUFBeUUsR0FBQTtJQUFBO1FBQUFBLEdBQUEsR0FBQXpFLENBQUE7SUFBQTtJQUFBLElBQUEwRSxHQUFBO0lBQUEsSUFBQTFFLENBQUEsU0FBQUUsTUFBQSxDQUFBQyxHQUFBO1FBS0Z1RSxHQUFBLCtFQUFDLDJEQUFVO1lBQVcsU0FBTSxFQUFOLE1BQU07b0NBQzFCLDhEQUFDLDBEQUFTO2dCQUFXLFNBQTJELEVBQTNELDJEQUEyRDs7a0NBQzlFLGlFQUVNO3dCQUZTLFNBQStFLEVBQS9FLCtFQUErRTtrQ0FDNUYsNEVBQUMsMElBQVE7NEJBQVcsU0FBb0IsRUFBcEIsb0JBQW9COzs7Ozs7Ozs7OztvQkFDcEMsYUFFUixFQUxDOzs7Ozs7Ozs7Ozs7UUFNVTFFLENBQUEsT0FBQTBFLEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUExRSxDQUFBO0lBQUE7SUFBQSxJQUFBMkUsR0FBQTtJQUFBLElBQUEzRSxDQUFBLFNBQUFFLE1BQUEsQ0FBQUMsR0FBQTtRQVRqQndFLEdBQUEsa0ZBK0JNO29DQTlCSiw4REFBQyxxREFBSTtnQkFBVyxTQUFpRCxFQUFqRCxpREFBaUQsQ0FDL0Q7O29CQUFBRCxHQU9ZO2tDQUNaLDhEQUFDLDREQUFXO2dEQUNWLGlFQWlCTTs0QkFqQlMsU0FBa0IsRUFBbEIsa0JBQWtCLENBQzlCO3NDQUFBekMsY0FBYyxDQUFBRyxHQUFBLENBQUF3QyxNQWVkOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBSUg1RSxDQUFBLE9BQUEyRSxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBM0UsQ0FBQTtJQUFBO0lBQUEsSUFBQTZFLEdBQUE7SUFBQSxJQUFBN0UsQ0FBQSxTQUFBeUUsR0FBQTtRQXhFUkksR0FBQSxrRkF5RU07WUF6RVMsU0FBNEIsRUFBNUIsNEJBQTRCLENBQ3pDOztnQkFBQUosR0FxQ0ssQ0FHTDtnQkFBQUUsR0ErQks7Ozs7Ozs7UUFDRDNFLENBQUEsT0FBQXlFLEdBQUE7UUFBQXpFLENBQUEsT0FBQTZFLEdBQUE7SUFBQTtRQUFBQSxHQUFBLEdBQUE3RSxDQUFBO0lBQUE7SUFBQSxJQUFBOEUsR0FBQTtJQUFBLElBQUE5RSxDQUFBLFNBQUE2QyxHQUFBLElBQUE3QyxDQUFBLFNBQUFrRSxHQUFBLElBQUFsRSxDQUFBLFNBQUE2RSxHQUFBO1FBeEtSQyxHQUFBLGtGQXlLTTtZQXpLUyxTQUErRSxFQUEvRSwrRUFBK0UsQ0FFNUY7O2dCQUFBakMsR0EyQkssQ0FHTDtnQkFBQXFCLEdBNERLLENBR0w7Z0JBQUFXLEdBeUVLOzs7Ozs7O1FBQ0Q3RSxDQUFBLE9BQUE2QyxHQUFBO1FBQUE3QyxDQUFBLE9BQUFrRSxHQUFBO1FBQUFsRSxDQUFBLE9BQUE2RSxHQUFBO1FBQUE3RSxDQUFBLE9BQUE4RSxHQUFBO0lBQUE7UUFBQUEsR0FBQSxHQUFBOUUsQ0FBQTtJQUFBO0lBQUEsT0F6S044RSxHQXlLTTtBQUFBOzs7UUF6T2tCaEcsdURBQUEsQ0FBVztRQUNVTywyREFBQSxDQUFXO1FBSzFEQywrREFBQTs7O01BUGFRO0FBQUEsU0FBQThFLE9BQUFHLFFBQUEsRUFBQUMsS0FBQTtJQUFBLHFCQXNORyxpRUFhTSxDQWJJQSxHQUFLLENBQUxBO1FBQWlCLFNBQTRKLEVBQTVKLDRKQUE0Sjs7MEJBQ3JMLGlFQUVNO2dCQUZTLFNBQWtHLEVBQWxHLGtHQUFrRzt3Q0FDL0csaUVBQXFEO29CQUF0QyxTQUErQixFQUEvQiwrQkFBK0I7Ozs7Ozs7Ozs7OzBCQUVoRCxpRUFRTTtnQkFSUyxTQUE4QixFQUE5Qiw4QkFBOEI7O2tDQUMzQyxpRUFBOEU7d0JBQS9ELFNBQXVDLEVBQXZDLHVDQUF1QyxDQUFFO2tDQUFBRCxRQUFRLENBQUFqRCxNQUFNOzs7Ozs7a0NBQ3RFLGlFQUVNO3dCQUZTLFNBQStCLEVBQS9CLCtCQUErQixDQUMzQztrQ0FBQWlELFFBQVEsQ0FBQWhELE9BQU87Ozs7OztrQ0FFbEIsaUVBRU07d0JBRlMsU0FBMkMsRUFBM0MsMkNBQTJDLENBQ3ZEO2tDQUFBZ0QsUUFBUSxDQUFBL0MsSUFBSTs7Ozs7Ozs7Ozs7OztPQVZUZ0QsS0FBSSxDQUFDOzs7OztBQWFUO0FBbk9ULFNBQUFWLE9BQUFXLFVBQUE7SUFBQSxxQkE4S0csOERBQUMsaUZBQWMsQ0FDUjtRQUNPQSxVQUFVLENBQVZBLENBQUFBLFVBQVMsQ0FBQztRQUNQLGFBR2QsQ0FIYztZQUViOUQsT0FBQSxDQUFBZ0UsR0FBQSxDQUFZLGlCQUFpQixFQUFFRixVQUFVLENBQUF6QyxJQUFLLENBQUM7UUFBQSxDQUNqRCxDQUFDO1FBQ2MsYUFHZCxDQUhjO1lBRWJyQixPQUFBLENBQUFnRSxHQUFBLENBQVksa0JBQWtCLEVBQUVGLFVBQVUsQ0FBQXpDLElBQUssQ0FBQztRQUFBLENBQ2xEO09BVEt5QyxVQUFVLENBQUFDLEVBQUUsQ0FBQzs7Ozs7QUFVbEI7QUF6TEwsU0FBQTdDLE9BQUErQyxDQUFBLEVBQUFDLENBQUE7SUFBQSxxQkFnREgsOERBQUMscURBQUksQ0FBTUE7UUFBYSxTQUFlLEVBQWYsZUFBZTs7MEJBQ3JDLDhEQUFDLDJEQUFVO2dCQUFXLFNBQTJELEVBQTNELDJEQUEyRDs7a0NBQy9FLGlFQUFpRDt3QkFBbEMsU0FBMkIsRUFBM0IsMkJBQTJCOzs7Ozs7a0NBQzFDLGlFQUFnRDt3QkFBakMsU0FBMEIsRUFBMUIsMEJBQTBCOzs7Ozs7Ozs7Ozs7MEJBRTNDLDhEQUFDLDREQUFXOztrQ0FDVixpRUFBc0Q7d0JBQXZDLFNBQWdDLEVBQWhDLGdDQUFnQzs7Ozs7O2tDQUMvQyxpRUFBaUQ7d0JBQWxDLFNBQTJCLEVBQTNCLDJCQUEyQjs7Ozs7Ozs7Ozs7OztPQVBuQ0EsQ0FBQSxDQUFDOzs7OztBQVNMO0FBekRKLFNBQUExRCxPQUFBMkQsR0FBQSxFQUFBQyxJQUFBOztJQStCTCxNQUFBQyxhQUFBLDRCQUEwQmpGLFNBQUEsb0RBQUpnRixJQUFJLFlBQUFsRSxNQUFBLE1BQXVCO0lBQUEsT0FDMUNpRSxHQUFHLEdBQUlFLGFBQWEsR0FBR0QsSUFBSSxDQUFBRSxRQUFTLE1BQU87QUFBQTtBQWhDN0MsU0FBQXpFLE1BQUEwRSxPQUFBO0lBQUEsT0Fjc0JDLFVBQUEsQ0FBV0QsT0FBTyxLQUFLLENBQUM7QUFBQSIsInNvdXJjZXMiOlsiRzpcXEF1Z21lbnQgY29kZVxcYXBwXFwoZGFzaGJvYXJkKVxcZGFzaGJvYXJkXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgRGVwYXJ0bWVudENhcmQgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9kZXBhcnRtZW50LWNhcmRcIlxuaW1wb3J0IHsgRGVwYXJ0bWVudCwgRW1wbG95ZWUgfSBmcm9tIFwiQC9saWIvdHlwZXNcIlxuaW1wb3J0IHsgdXNlSFJTdG9yZSB9IGZyb20gXCJAL2xpYi9zdG9yZS9oci1zdG9yZVwiXG5pbXBvcnQgeyB1c2VBcGlEYXRhIH0gZnJvbSBcIkAvbGliL2hvb2tzL3VzZS1hcGktZGF0YVwiXG5pbXBvcnQge1xuICBVc2VycyxcbiAgQnVpbGRpbmcyLFxuICBVc2VyUGx1cyxcbiAgQXJjaGl2ZSxcbiAgVHJlbmRpbmdVcCxcbiAgRG93bmxvYWQsXG4gIEFjdGl2aXR5XG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuXG5pbnRlcmZhY2UgRGFzaGJvYXJkUGFnZVByb3BzIHtcbiAgcGFyYW1zPzogUHJvbWlzZTxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+PlxuICBzZWFyY2hQYXJhbXM/OiBQcm9taXNlPFJlY29yZDxzdHJpbmcsIHN0cmluZyB8IHN0cmluZ1tdPj5cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkUGFnZSh7IHBhcmFtcywgc2VhcmNoUGFyYW1zIH06IERhc2hib2FyZFBhZ2VQcm9wcykge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24gfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCB7IGRlcGFydG1lbnRzLCBlbXBsb3llZXMsIGZyZWVCdWNrZXQgfSA9IHVzZUhSU3RvcmUoKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3Nob3dBZGRFbXBsb3llZURpYWxvZywgc2V0U2hvd0FkZEVtcGxveWVlRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIExvYWQgQVBJIGRhdGFcbiAgdXNlQXBpRGF0YSgpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTaW11bGF0ZSBsb2FkaW5nIGRhdGFcbiAgICBjb25zdCBsb2FkRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFNhbXBsZSBkYXRhIGlzIGxvYWRlZCB2aWEgdXNlU2FtcGxlRGF0YSBob29rXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKVxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGxvYWQgZGFzaGJvYXJkIGRhdGE6XCIsIGVycm9yKVxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICB9XG4gICAgfVxuXG4gICAgbG9hZERhdGEoKVxuICB9LCBbXSlcblxuICBjb25zdCB0b3RhbEVtcGxveWVlcyA9IGVtcGxveWVlcy5sZW5ndGhcbiAgY29uc3QgdG90YWxEZXBhcnRtZW50cyA9IGRlcGFydG1lbnRzLmxlbmd0aFxuICBjb25zdCBmcmVlQnVja2V0Q291bnQgPSBmcmVlQnVja2V0Lmxlbmd0aFxuICBjb25zdCBhdmVyYWdlVXRpbGl6YXRpb24gPSBkZXBhcnRtZW50cy5sZW5ndGggPiAwIFxuICAgID8gTWF0aC5yb3VuZChcbiAgICAgICAgZGVwYXJ0bWVudHMucmVkdWNlKChzdW0sIGRlcHQpID0+IHtcbiAgICAgICAgICBjb25zdCBlbXBsb3llZUNvdW50ID0gZGVwdC5lbXBsb3llZXM/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgcmV0dXJuIHN1bSArIChlbXBsb3llZUNvdW50IC8gZGVwdC5jYXBhY2l0eSkgKiAxMDBcbiAgICAgICAgfSwgMCkgLyBkZXBhcnRtZW50cy5sZW5ndGhcbiAgICAgIClcbiAgICA6IDBcblxuICBjb25zdCByZWNlbnRBY3Rpdml0eSA9IFtcbiAgICB7IGFjdGlvbjogXCLYqtmFINmG2YLZhCDZhdmI2LjZgVwiLCBkZXRhaWxzOiBcItiq2YUg2YbZgtmEINij2K3ZhdivINmF2K3ZhdivINil2YTZiSDZgtiz2YUg2KfZhNmH2YbYr9iz2KlcIiwgdGltZTogXCLZhdmG2LAg2LPYp9i52KrZitmGXCIgfSxcbiAgICB7IGFjdGlvbjogXCLYqtmFINil2LbYp9mB2Kkg2YXZiNi42YEg2KzYr9mK2K9cIiwgZGV0YWlsczogXCLYp9mG2LbZhdiqINmB2KfYt9mF2Kkg2KPYrdmF2K8g2KXZhNmJINmC2LPZhSDYp9mE2KrYs9mI2YrZglwiLCB0aW1lOiBcItmF2YbYsCA0INiz2KfYudin2KpcIiB9LFxuICAgIHsgYWN0aW9uOiBcItiq2YUg2KXZhti02KfYoSDZgtiz2YUg2KzYr9mK2K9cIiwgZGV0YWlsczogXCLZgtiz2YUg2KfZhNio2K3YqyDZiNin2YTYqti32YjZitixXCIsIHRpbWU6IFwi2YXZhtiwINmK2YjZhSDZiNin2K3Yr1wiIH0sXG4gIF1cblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNFwiPlxuICAgICAgICAgIHtbLi4uQXJyYXkoNCldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgPENhcmQga2V5PXtpfSBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0yMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCBiZy1tdXRlZCByb3VuZGVkXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLW11dGVkIHJvdW5kZWQgdy0xNiBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctbXV0ZWQgcm91bmRlZCB3LTI0XCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1zcGFjZWQgcC04IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAvNTAgdG8td2hpdGUgbWluLWgtc2NyZWVuXCI+XG4gICAgICB7LyogV2VsY29tZSBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIGdyYWRpZW50LXByaW1hcnkgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgINmE2YjYrdipINin2YTYqtit2YPZhVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgINij2YfZhNin2Ysg2KjZg9iMIHtzZXNzaW9uPy51c2VyPy5uYW1lfS4g2KXZhNmK2YMg2YXYpyDZitit2K/YqyDZgdmKINmF2KTYs9iz2KrZgy5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNoYWRvdy1zb2Z0IGhvdmVyLWxpZnQgYm9yZGVyLTIgYm9yZGVyLWJvcmRlci81MFwiXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFeHBvcnRSZXBvcnR9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMlwiIC8+XG4gICAgICAgICAgICDYqti12K/ZitixINin2YTYqtmC2LHZitixXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JhZGllbnQtcHJpbWFyeSBob3ZlcjpvcGFjaXR5LTkwIHNoYWRvdy1tZWRpdW0gaG92ZXItbGlmdFwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkRW1wbG95ZWVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVzZXJQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMlwiIC8+XG4gICAgICAgICAgICDYpdi22KfZgdipINmF2YjYuNmBXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTdGF0cyBDYXJkcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1zcGFjZWQgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTEgYm9yZGVyLTAgZ3JhZGllbnQtZ3JlZW4tc29mdCBib3JkZXItci00IGJvcmRlci1yLXByaW1hcnlcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTNcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeVwiPtil2KzZhdin2YTZiiDYp9mE2YXZiNi42YHZitmGPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBncmFkaWVudC1wcmltYXJ5IHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPnt0b3RhbEVtcGxveWVlc308L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LzcwIGZvbnQtbWVkaXVtIG10LTFcIj5cbiAgICAgICAgICAgICAgKzEyJSDZhdmGINin2YTYtNmH2LEg2KfZhNmF2KfYttmKXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOi10cmFuc2xhdGUteS0xIGJvcmRlci0wIGdyYWRpZW50LWdyZWVuLXNvZnQgYm9yZGVyLXItNCBib3JkZXItci1zZWNvbmRhcnlcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTNcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtc2Vjb25kYXJ5XCI+2KfZhNij2YLYs9in2YU8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLXNlY29uZGFyeSByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1zZWNvbmRhcnlcIj57dG90YWxEZXBhcnRtZW50c308L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zZWNvbmRhcnkvNzAgZm9udC1tZWRpdW0gbXQtMVwiPlxuICAgICAgICAgICAgICDYp9mE2KPZgtiz2KfZhSDYp9mE2YbYtNi32KlcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTEgYm9yZGVyLTAgZ3JhZGllbnQtZ3JlZW4tc29mdCBib3JkZXItci00IGJvcmRlci1yLWFjY2VudFwiPlxuICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItM1wiPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1hY2NlbnRcIj7Yp9mE2LPZhNipINin2YTZhdik2YLYqtipPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1hY2NlbnQgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgPEFyY2hpdmUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWFjY2VudFwiPntmcmVlQnVja2V0Q291bnR9PC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYWNjZW50LzcwIGZvbnQtbWVkaXVtIG10LTFcIj5cbiAgICAgICAgICAgICAg2YXZiNi42YHZiNmGINi62YrYsSDZhdiu2LXYtdmK2YZcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTEgYm9yZGVyLTAgZ3JhZGllbnQtZ3JlZW4tc29mdCBib3JkZXItci00IGJvcmRlci1yLXN1Y2Nlc3NcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTNcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtc3VjY2Vzc1wiPtmF2KrZiNiz2Lcg2KfZhNin2LPYqtiu2K/Yp9mFPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1zdWNjZXNzIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1zdWNjZXNzXCI+e2F2ZXJhZ2VVdGlsaXphdGlvbn0lPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc3VjY2Vzcy83MCBmb250LW1lZGl1bSBtdC0xXCI+XG4gICAgICAgICAgICAgINiz2LnYqSDYp9mE2KPZgtiz2KfZhVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGVwYXJ0bWVudCBPdmVydmlldyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1zcGFjZWQgbGc6Z3JpZC1jb2xzLTNcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LXhsIGJvcmRlci0wIGJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTZcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3JlZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGdyYWRpZW50LXByaW1hcnkgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxCdWlsZGluZzIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICDZhti42LHYqSDYudin2YXYqSDYudmE2Ykg2KfZhNij2YLYs9in2YVcbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1zcGFjZWQgbWQ6Z3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgICB7ZGVwYXJ0bWVudHMuc2xpY2UoMCwgNikubWFwKChkZXBhcnRtZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8RGVwYXJ0bWVudENhcmRcbiAgICAgICAgICAgICAgICAgICAga2V5PXtkZXBhcnRtZW50LmlkfVxuICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50PXtkZXBhcnRtZW50fVxuICAgICAgICAgICAgICAgICAgICBvbkFkZEVtcGxveWVlPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogSW1wbGVtZW50IGFkZCBlbXBsb3llZSB0byBkZXBhcnRtZW50XG4gICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJBZGQgZW1wbG95ZWUgdG9cIiwgZGVwYXJ0bWVudC5uYW1lKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBvblZpZXdEZXRhaWxzPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogTmF2aWdhdGUgdG8gZGVwYXJ0bWVudCBkZXRhaWxzXG4gICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJWaWV3IGRldGFpbHMgZm9yXCIsIGRlcGFydG1lbnQubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtkZXBhcnRtZW50cy5sZW5ndGggPiA2ICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJzaGFkb3ctc29mdCBob3Zlci1saWZ0IGJvcmRlci0yIGJvcmRlci1ib3JkZXIvNTBcIj5cbiAgICAgICAgICAgICAgICAgICAg2LnYsdi2INis2YXZiti5INin2YTYo9mC2LPYp9mFICh7ZGVwYXJ0bWVudHMubGVuZ3RofSlcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZWNlbnQgQWN0aXZpdHkgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LXhsIGJvcmRlci0wIGJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTZcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggZ3JhZGllbnQtYWNjZW50IHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICDYp9mE2YbYtNin2Lcg2KfZhNij2K7ZitixXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1zcGFjZWRcIj5cbiAgICAgICAgICAgICAgICB7cmVjZW50QWN0aXZpdHkubWFwKChhY3Rpdml0eSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBydGwtc3BhY2UteCBwLTQgcm91bmRlZC14bCBncmFkaWVudC1ncmVlbi1zb2Z0IGJvcmRlciBib3JkZXItYm9yZGVyLzUwIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBncmFkaWVudC1wcmltYXJ5IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGcgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy13aGl0ZSByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGNvbnRhaW5lci1zcGFjZWQgbXItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZm9yZWdyb3VuZFwiPnthY3Rpdml0eS5hY3Rpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LmRldGFpbHN9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LnRpbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiYyIsIl9jIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VTZXNzaW9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkRlcGFydG1lbnRDYXJkIiwidXNlSFJTdG9yZSIsInVzZUFwaURhdGEiLCJVc2VycyIsIkJ1aWxkaW5nMiIsIlVzZXJQbHVzIiwiQXJjaGl2ZSIsIlRyZW5kaW5nVXAiLCJEb3dubG9hZCIsIkFjdGl2aXR5IiwiRGFzaGJvYXJkUGFnZSIsInQwIiwiJCIsIiRpIiwiU3ltYm9sIiwiZm9yIiwiZGF0YSIsInNlc3Npb24iLCJkZXBhcnRtZW50cyIsImVtcGxveWVlcyIsImZyZWVCdWNrZXQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZXRTaG93QWRkRW1wbG95ZWVEaWFsb2ciLCJ0MSIsInQyIiwibG9hZERhdGEiLCJQcm9taXNlIiwiX3RlbXAiLCJ0MyIsImVycm9yIiwiY29uc29sZSIsInRvdGFsRW1wbG95ZWVzIiwibGVuZ3RoIiwidG90YWxEZXBhcnRtZW50cyIsImZyZWVCdWNrZXRDb3VudCIsIk1hdGgiLCJyb3VuZCIsInJlZHVjZSIsIl90ZW1wMiIsImF2ZXJhZ2VVdGlsaXphdGlvbiIsInQ0IiwiYWN0aW9uIiwiZGV0YWlscyIsInRpbWUiLCJyZWNlbnRBY3Rpdml0eSIsInQ1IiwiQXJyYXkiLCJtYXAiLCJfdGVtcDMiLCJ0NiIsInVzZXIiLCJuYW1lIiwidDciLCJ0OCIsImhhbmRsZUV4cG9ydFJlcG9ydCIsInQ5IiwidDEwIiwidDExIiwidDEyIiwidDEzIiwidDE0IiwidDE1IiwidDE2IiwidDE3IiwidDE4IiwidDE5IiwidDIwIiwidDIxIiwidDIyIiwidDIzIiwidDI0IiwidDI1IiwidDI2IiwidDI3IiwidDI4IiwidDI5IiwidDMwIiwidDMxIiwidDMyIiwidDMzIiwic2xpY2UiLCJfdGVtcDQiLCJ0MzQiLCJ0MzUiLCJ0MzYiLCJ0MzciLCJ0MzgiLCJfdGVtcDUiLCJ0MzkiLCJ0NDAiLCJhY3Rpdml0eSIsImluZGV4IiwiZGVwYXJ0bWVudCIsImlkIiwibG9nIiwiXyIsImkiLCJzdW0iLCJkZXB0IiwiZW1wbG95ZWVDb3VudCIsImNhcGFjaXR5IiwicmVzb2x2ZSIsInNldFRpbWVvdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});
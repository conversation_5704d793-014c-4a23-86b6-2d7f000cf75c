import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { Employee, Department, BulkOperation, DistributionConfig } from '@/lib/types'
import { employeeService, CreateEmployeeData, UpdateEmployeeData, TransferEmployeeData } from '@/lib/services/employee-service'
import { distributionService } from '@/lib/services/distribution-service'
import { localStorageService } from '@/lib/services/local-storage-service'
import { syncService } from '@/lib/services/sync-service'
import { collaborationService } from '@/lib/services/collaboration-service'
import { idGenerator } from '@/lib/utils/id-generator'
import { toast } from 'sonner'

interface HRState {
  // Data
  employees: Employee[]
  departments: Department[]
  freeBucket: Employee[]

  // UI State
  selectedEmployees: string[]
  isLoading: boolean
  searchQuery: string

  // Actions
  setEmployees: (employees: Employee[]) => void
  setDepartments: (departments: Department[]) => void
  addEmployee: (employee: Employee) => void
  updateEmployee: (id: string, updates: Partial<Employee>) => void
  removeEmployee: (id: string) => void

  // Department Actions
  addDepartment: (department: Department) => void
  updateDepartment: (id: string, updates: Partial<Department>) => void
  removeDepartment: (id: string) => void

  // Enhanced Employee Operations
  createEmployee: (data: CreateEmployeeData) => Promise<boolean>
  updateEmployeeData: (id: string, data: UpdateEmployeeData) => Promise<boolean>
  transferEmployee: (id: string, data: TransferEmployeeData) => Promise<boolean>
  archiveEmployee: (id: string, userId: string) => Promise<boolean>

  // Selection
  toggleEmployeeSelection: (id: string) => void
  selectAllEmployees: (ids: string[]) => void
  clearSelection: () => void

  // Bulk Operations
  executeBulkOperation: (operation: BulkOperation, userId: string) => Promise<void>

  // Search & Filter
  setSearchQuery: (query: string) => void
  getFilteredEmployees: () => Employee[]

  // Free Bucket
  moveToFreeBucket: (employeeIds: string[]) => void
  removeFromFreeBucket: (employeeIds: string[]) => void

  // Distribution
  distributeEmployees: (config: DistributionConfig, userId: string) => Promise<boolean>
  rebalanceEmployees: (config: DistributionConfig, userId: string) => Promise<boolean>

  // Local Storage & Sync
  loadFromLocalStorage: () => Promise<void>
  saveToLocalStorage: () => Promise<void>
  syncData: () => Promise<void>

  // Collaboration
  initializeCollaboration: (user: any) => Promise<void>
  syncWithCollaboration: () => void

  // Initialization
  initializeIDGenerator: () => void
}

export const useHRStore = create<HRState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    employees: [],
    departments: [],
    freeBucket: [],
    selectedEmployees: [],
    isLoading: false,
    searchQuery: '',

    // Data actions
    setEmployees: (employees) => set((state) => ({
      employees,
      freeBucket: employees.filter(emp => emp.departmentId === null)
    })),
    setDepartments: (departments) => set({ departments }),
    
    addEmployee: (employee) => set((state) => ({
      employees: [...state.employees, employee]
    })),
    
    updateEmployee: (id, updates) => set((state) => ({
      employees: state.employees.map(emp => 
        emp.id === id ? { ...emp, ...updates } : emp
      )
    })),
    
    removeEmployee: (id) => set((state) => ({
      employees: state.employees.filter(emp => emp.id !== id),
      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)
    })),

    // Department actions
    addDepartment: (department) => set((state) => ({
      departments: [...state.departments, department]
    })),

    updateDepartment: (id, updates) => set((state) => ({
      departments: state.departments.map(dept =>
        dept.id === id ? { ...dept, ...updates } : dept
      )
    })),

    removeDepartment: (id) => set((state) => ({
      departments: state.departments.filter(dept => dept.id !== id)
    })),

    // Enhanced Employee Operations
    createEmployee: async (data) => {
      const { employees, departments } = get()
      set({ isLoading: true })

      try {
        const result = await employeeService.createEmployee(data, departments, employees)

        if (result.success && result.data) {
          set((state) => ({
            employees: [...state.employees, result.data!],
            freeBucket: result.data!.departmentId === null
              ? [...state.freeBucket, result.data!]
              : state.freeBucket
          }))
          toast.success('تم إنشاء الموظف بنجاح')
          return true
        } else {
          toast.error(result.error || 'فشل في إنشاء الموظف')
          return false
        }
      } catch (error) {
        toast.error('خطأ في إنشاء الموظف')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    updateEmployeeData: async (id, data) => {
      const { employees } = get()
      set({ isLoading: true })

      try {
        const result = await employeeService.updateEmployee(id, data, employees)

        if (result.success && result.data) {
          set((state) => ({
            employees: state.employees.map(emp =>
              emp.id === id ? result.data! : emp
            )
          }))
          toast.success('تم تحديث الموظف بنجاح')
          return true
        } else {
          toast.error(result.error || 'فشل في تحديث الموظف')
          return false
        }
      } catch (error) {
        toast.error('خطأ في تحديث الموظف')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    transferEmployee: async (id, data) => {
      const { employees, departments } = get()
      set({ isLoading: true })

      try {
        const result = await employeeService.transferEmployee(id, data, employees, departments)

        if (result.success && result.data) {
          set((state) => ({
            employees: state.employees.map(emp =>
              emp.id === id ? result.data! : emp
            ),
            freeBucket: result.data!.departmentId === null
              ? [...state.freeBucket.filter(emp => emp.id !== id), result.data!]
              : state.freeBucket.filter(emp => emp.id !== id)
          }))
          toast.success('تم نقل الموظف بنجاح')
          return true
        } else {
          toast.error(result.error || 'فشل في نقل الموظف')
          return false
        }
      } catch (error) {
        toast.error('خطأ في نقل الموظف')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    archiveEmployee: async (id, userId) => {
      const { employees } = get()
      set({ isLoading: true })

      try {
        const result = await employeeService.archiveEmployee(id, userId, employees)

        if (result.success && result.data) {
          set((state) => ({
            employees: state.employees.map(emp =>
              emp.id === id ? result.data! : emp
            ),
            freeBucket: state.freeBucket.filter(emp => emp.id !== id)
          }))
          toast.success('تم أرشفة الموظف بنجاح')
          return true
        } else {
          toast.error(result.error || 'فشل في أرشفة الموظف')
          return false
        }
      } catch (error) {
        toast.error('خطأ في أرشفة الموظف')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    // Selection actions
    toggleEmployeeSelection: (id) => set((state) => ({
      selectedEmployees: state.selectedEmployees.includes(id)
        ? state.selectedEmployees.filter(empId => empId !== id)
        : [...state.selectedEmployees, id]
    })),
    
    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),
    clearSelection: () => set({ selectedEmployees: [] }),

    // Bulk operations
    executeBulkOperation: async (operation, userId) => {
      const { employees, departments } = get()
      set({ isLoading: true })

      try {
        const result = await employeeService.bulkOperation(operation, employees, departments, userId)

        if (result.success && result.data) {
          // Update employees with the results
          set((state) => {
            const updatedEmployees = [...state.employees]
            result.data!.forEach(updatedEmp => {
              const index = updatedEmployees.findIndex(emp => emp.id === updatedEmp.id)
              if (index !== -1) {
                updatedEmployees[index] = updatedEmp
              }
            })

            return {
              employees: updatedEmployees,
              freeBucket: updatedEmployees.filter(emp => emp.departmentId === null),
              selectedEmployees: []
            }
          })
        }
      } catch (error) {
        toast.error('خطأ في العملية الجماعية')
      } finally {
        set({ isLoading: false })
      }
    },

    // Search & filter
    setSearchQuery: (query) => set({ searchQuery: query }),
    
    getFilteredEmployees: () => {
      const { employees, searchQuery } = get()
      if (!searchQuery) return employees
      
      return employees.filter(emp =>
        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.id.toLowerCase().includes(searchQuery.toLowerCase())
      )
    },

    // Free bucket operations
    moveToFreeBucket: (employeeIds) => set((state) => {
      const movedEmployees = state.employees
        .filter(emp => employeeIds.includes(emp.id))
        .map(emp => ({ ...emp, departmentId: null }))
      
      return {
        employees: state.employees.map(emp =>
          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp
        ),
        freeBucket: [...state.freeBucket, ...movedEmployees]
      }
    }),
    
    removeFromFreeBucket: (employeeIds) => set((state) => ({
      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),
      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))
    })),

    // Distribution
    distributeEmployees: async (config, userId) => {
      const { departments, freeBucket } = get()
      set({ isLoading: true })

      try {
        const result = await distributionService.distributeEmployees(
          freeBucket,
          departments,
          config,
          userId
        )

        if (result.success) {
          // Update employees with distributed results
          set((state) => {
            const updatedEmployees = [...state.employees]

            // Update distributed employees
            result.distributed.forEach(distributedEmp => {
              const index = updatedEmployees.findIndex(emp => emp.id === distributedEmp.id)
              if (index !== -1) {
                updatedEmployees[index] = distributedEmp
              }
            })

            // Update overflow employees
            result.overflow.forEach(overflowEmp => {
              const index = updatedEmployees.findIndex(emp => emp.id === overflowEmp.id)
              if (index !== -1) {
                updatedEmployees[index] = overflowEmp
              }
            })

            return {
              employees: updatedEmployees,
              freeBucket: updatedEmployees.filter(emp => emp.departmentId === null)
            }
          })

          return true
        } else {
          toast.error('فشل في عملية التوزيع')
          return false
        }
      } catch (error) {
        toast.error('خطأ في عملية التوزيع')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    rebalanceEmployees: async (config, userId) => {
      const { departments, employees } = get()
      set({ isLoading: true })

      try {
        const result = await distributionService.rebalanceEmployees(
          departments,
          employees,
          config,
          userId
        )

        if (result.success) {
          // Update employees with rebalanced results
          set((state) => {
            const updatedEmployees = [...state.employees]

            // Update distributed employees
            result.distributed.forEach(distributedEmp => {
              const index = updatedEmployees.findIndex(emp => emp.id === distributedEmp.id)
              if (index !== -1) {
                updatedEmployees[index] = distributedEmp
              }
            })

            return {
              employees: updatedEmployees,
              freeBucket: updatedEmployees.filter(emp => emp.departmentId === null)
            }
          })

          return true
        } else {
          toast.error('فشل في إعادة التوزيع')
          return false
        }
      } catch (error) {
        toast.error('خطأ في إعادة التوزيع')
        return false
      } finally {
        set({ isLoading: false })
      }
    },

    // Local Storage & Sync
    loadFromLocalStorage: async () => {
      set({ isLoading: true })
      try {
        const [employees, departments] = await Promise.all([
          localStorageService.loadEmployees(),
          localStorageService.loadDepartments()
        ])

        set({
          employees,
          departments,
          freeBucket: employees.filter(emp => emp.departmentId === null)
        })

        // Initialize ID generator with loaded data
        idGenerator.initializeCounters(departments, employees)

        console.log('Data loaded from local storage')
      } catch (error) {
        console.error('Failed to load from local storage:', error)
        toast.error('فشل في تحميل البيانات المحلية')
      } finally {
        set({ isLoading: false })
      }
    },

    saveToLocalStorage: async () => {
      const { employees, departments } = get()
      try {
        await Promise.all([
          localStorageService.saveEmployees(employees),
          localStorageService.saveDepartments(departments)
        ])
        console.log('Data saved to local storage')
      } catch (error) {
        console.error('Failed to save to local storage:', error)
        toast.error('فشل في حفظ البيانات المحلية')
      }
    },

    syncData: async () => {
      set({ isLoading: true })
      try {
        const result = await syncService.syncData({
          forceSync: true,
          resolveConflicts: 'local'
        })

        if (result.success) {
          // Reload data after sync
          const [employees, departments] = await Promise.all([
            localStorageService.loadEmployees(),
            localStorageService.loadDepartments()
          ])

          set({
            employees,
            departments,
            freeBucket: employees.filter(emp => emp.departmentId === null)
          })
        }
      } catch (error) {
        console.error('Sync failed:', error)
        toast.error('فشلت عملية المزامنة')
      } finally {
        set({ isLoading: false })
      }
    },

    // Collaboration
    initializeCollaboration: async (user) => {
      try {
        await collaborationService.initialize(user)

        // Set up collaboration event listeners
        collaborationService.on('employee_changed', (data: any) => {
          if (data.action === 'add' || data.action === 'update') {
            const { _lastModified, _modifiedBy, _deviceId, ...employee } = data.employee
            set((state) => ({
              employees: state.employees.map(emp =>
                emp.id === employee.id ? employee : emp
              ).concat(state.employees.find(emp => emp.id === employee.id) ? [] : [employee]),
              freeBucket: state.employees.filter(emp => emp.departmentId === null)
            }))
          } else if (data.action === 'delete') {
            set((state) => ({
              employees: state.employees.filter(emp => emp.id !== data.employeeId),
              freeBucket: state.freeBucket.filter(emp => emp.id !== data.employeeId)
            }))
          }
        })

        collaborationService.on('department_changed', (data: any) => {
          if (data.action === 'add' || data.action === 'update') {
            const { _lastModified, _modifiedBy, _deviceId, ...department } = data.department
            set((state) => ({
              departments: state.departments.map(dept =>
                dept.id === department.id ? department : dept
              ).concat(state.departments.find(dept => dept.id === department.id) ? [] : [department])
            }))
          } else if (data.action === 'delete') {
            set((state) => ({
              departments: state.departments.filter(dept => dept.id !== data.departmentId)
            }))
          }
        })

        console.log('Collaboration initialized')
      } catch (error) {
        console.error('Failed to initialize collaboration:', error)
        toast.error('فشل في تهيئة التعاون الفوري')
      }
    },

    syncWithCollaboration: () => {
      const { employees, departments } = get()

      // Sync current data with collaboration service
      employees.forEach(employee => {
        collaborationService.addEmployee(employee)
      })

      departments.forEach(department => {
        collaborationService.addDepartment(department)
      })
    },

    // Initialization
    initializeIDGenerator: () => {
      const { departments, employees } = get()
      idGenerator.initializeCounters(departments, employees)
    }
  }))
)
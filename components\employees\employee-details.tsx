"use client"

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useHRStore } from '@/lib/store/hr-store'
import { Employee, TransferRecord } from '@/lib/types'
import { 
  User, 
  Mail, 
  Calendar, 
  Building2,
  Clock,
  ArrowRight,
  Edit,
  Archive,
  Shuffle,
  History,
  MapPin,
  Phone,
  FileText
} from 'lucide-react'
import { toast } from 'sonner'

interface EmployeeDetailsProps {
  employee: Employee
  onEdit?: () => void
  onClose?: () => void
  showActions?: boolean
}

export function EmployeeDetails({ 
  employee, 
  onEdit, 
  onClose,
  showActions = true 
}: EmployeeDetailsProps) {
  const { data: session } = useSession()
  const { departments, transferEmployee, archiveEmployee, isLoading } = useHRStore()
  const [isTransferring, setIsTransferring] = useState(false)
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>('')

  const currentDepartment = departments.find(d => d.id === employee.departmentId)

  const handleTransfer = async () => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    if (!selectedDepartmentId) {
      toast.error('يرجى اختيار القسم المستهدف')
      return
    }

    setIsTransferring(true)
    try {
      const success = await transferEmployee(employee.id, {
        fromDepartmentId: employee.departmentId,
        toDepartmentId: selectedDepartmentId === 'free-bucket' ? null : selectedDepartmentId,
        reason: 'نقل إداري',
        userId: session.user.id
      })

      if (success) {
        toast.success('تم نقل الموظف بنجاح')
        setSelectedDepartmentId('')
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء النقل')
    } finally {
      setIsTransferring(false)
    }
  }

  const handleArchive = async () => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    const confirmed = window.confirm('هل أنت متأكد من أرشفة هذا الموظف؟')
    if (!confirmed) return

    try {
      const success = await archiveEmployee(employee.id, session.user.id)
      if (success) {
        toast.success('تم أرشفة الموظف بنجاح')
        onClose?.()
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء الأرشفة')
    }
  }

  const getStatusBadge = (status: Employee['status']) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      TRANSFERRED: { label: 'منقول', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      PENDING_REMOVAL: { label: 'في انتظار الإزالة', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      ARCHIVED: { label: 'مؤرشف', variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const formatTransferHistory = (transfers: TransferRecord[]) => {
    return transfers.map((transfer, index) => {
      const fromDept = transfer.fromDepartmentId 
        ? departments.find(d => d.id === transfer.fromDepartmentId)?.name || 'قسم محذوف'
        : 'غير مخصص'
      
      const toDept = transfer.toDepartmentId 
        ? departments.find(d => d.id === transfer.toDepartmentId)?.name || 'قسم محذوف'
        : 'السلة المؤقتة'

      return {
        ...transfer,
        fromDeptName: fromDept,
        toDeptName: toDept,
        isInitial: index === 0 && !transfer.fromDepartmentId
      }
    }).reverse() // Show most recent first
  }

  const transferHistory = formatTransferHistory(employee.transferHistory)

  return (
    <div className="container-spaced max-w-4xl mx-auto">
      {/* Employee Header */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 border-4 border-white shadow-lg">
                <AvatarImage src="" alt={employee.name} />
                <AvatarFallback className="gradient-primary text-white text-xl font-bold">
                  {employee.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="container-spaced">
                <div className="flex items-center gap-3">
                  <h1 className="text-2xl font-bold text-foreground">{employee.name}</h1>
                  {getStatusBadge(employee.status)}
                </div>
                <p className="text-lg font-mono text-primary">{employee.id}</p>
                <p className="text-muted-foreground">{employee.email}</p>
              </div>
            </div>

            {showActions && onClose && (
              <Button variant="outline" onClick={onClose}>
                إغلاق
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      <div className="grid-spaced lg:grid-cols-3">
        {/* Basic Information */}
        <div className="lg:col-span-2 container-spaced">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
                  <User className="h-4 w-4 text-white" />
                </div>
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="container-spaced">
              <div className="grid-spaced md:grid-cols-2">
                <div className="flex items-center gap-3 p-3 rounded-lg gradient-green-soft">
                  <Mail className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">البريد الإلكتروني</p>
                    <p className="font-medium">{employee.email}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg gradient-green-soft">
                  <Calendar className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">تاريخ التوظيف</p>
                    <p className="font-medium">
                      {new Date(employee.hireDate).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg gradient-green-soft">
                  <Building2 className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">القسم الحالي</p>
                    <p className="font-medium">
                      {currentDepartment?.name || 'غير مخصص'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg gradient-green-soft">
                  <Clock className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">آخر تحديث</p>
                    <p className="font-medium">
                      {new Date(employee.updatedAt).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Transfer History */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg">
                  <History className="h-4 w-4 text-white" />
                </div>
                تاريخ النقل ({transferHistory.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {transferHistory.length > 0 ? (
                <div className="container-spaced">
                  {transferHistory.map((transfer, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-4 p-4 rounded-xl gradient-green-soft border border-border/50"
                    >
                      <div className="w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                        {transfer.isInitial ? (
                          <User className="h-4 w-4 text-white" />
                        ) : (
                          <Shuffle className="h-4 w-4 text-white" />
                        )}
                      </div>
                      
                      <div className="flex-1 container-spaced">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium">{transfer.fromDeptName}</span>
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{transfer.toDeptName}</span>
                        </div>
                        
                        <p className="text-sm text-muted-foreground">
                          {transfer.reason || 'بدون سبب محدد'}
                        </p>
                        
                        <p className="text-xs text-muted-foreground">
                          {new Date(transfer.timestamp).toLocaleString('ar-SA')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">لا يوجد تاريخ نقل</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Actions Panel */}
        {showActions && (
          <div className="container-spaced">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 gradient-secondary rounded-lg flex items-center justify-center shadow-lg">
                    <FileText className="h-4 w-4 text-white" />
                  </div>
                  الإجراءات
                </CardTitle>
              </CardHeader>
              <CardContent className="container-spaced">
                {onEdit && (
                  <Button
                    onClick={onEdit}
                    className="w-full gradient-primary hover:opacity-90 shadow-medium hover-lift"
                  >
                    <Edit className="h-4 w-4 ml-2" />
                    تعديل البيانات
                  </Button>
                )}

                {employee.status === 'ACTIVE' && (
                  <>
                    <div className="container-spaced">
                      <label className="text-sm font-medium">نقل إلى قسم آخر</label>
                      <Select
                        value={selectedDepartmentId}
                        onValueChange={setSelectedDepartmentId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر القسم المستهدف" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="free-bucket">السلة المؤقتة</SelectItem>
                          {departments
                            .filter(dept => dept.id !== employee.departmentId)
                            .map((dept) => (
                              <SelectItem key={dept.id} value={dept.id}>
                                {dept.name} ({dept.code})
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      
                      <Button
                        onClick={handleTransfer}
                        disabled={!selectedDepartmentId || isTransferring || isLoading}
                        variant="outline"
                        className="w-full shadow-soft hover-lift border-2 border-border/50"
                      >
                        <Shuffle className="h-4 w-4 ml-2" />
                        {isTransferring ? 'جاري النقل...' : 'نقل الموظف'}
                      </Button>
                    </div>

                    <Button
                      onClick={handleArchive}
                      variant="destructive"
                      className="w-full"
                    >
                      <Archive className="h-4 w-4 ml-2" />
                      أرشفة الموظف
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

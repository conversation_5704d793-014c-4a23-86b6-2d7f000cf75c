"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CSVImportSection } from "@/components/bulk/csv-import-section"
import { CSVExportSection } from "@/components/bulk/csv-export-section"
import { BulkTransferSection } from "@/components/bulk/bulk-transfer-section"
import { BulkStatusUpdateSection } from "@/components/bulk/bulk-status-update-section"
import { useHRStore } from "@/lib/store/hr-store"
import { useApiData } from "@/lib/hooks/use-api-data"
import { 
  Upload, 
  Download, 
  ArrowRight,
  Users,
  FileText,
  AlertCircle
} from "lucide-react"

interface BulkOperationsPageProps {
  params?: Promise<Record<string, string>>
  searchParams?: Promise<Record<string, string | string[]>>
}

export default function BulkOperationsPage({ params, searchParams }: BulkOperationsPageProps) {
  const { data: session } = useSession()
  const { employees, departments, selectedEmployees } = useHRStore()
  const [activeTab, setActiveTab] = useState<'import' | 'export' | 'transfer' | 'status'>('import')

  // Load API data
  useApiData()

  const tabs = [
    {
      id: 'import' as const,
      label: 'Import CSV',
      icon: Upload,
      description: 'Import employees from CSV file'
    },
    {
      id: 'export' as const,
      label: 'Export CSV',
      icon: Download,
      description: 'Export employees to CSV file'
    },
    {
      id: 'transfer' as const,
      label: 'Bulk Transfer',
      icon: ArrowRight,
      description: 'Transfer multiple employees between departments'
    },
    {
      id: 'status' as const,
      label: 'Status Update',
      icon: Users,
      description: 'Update status for multiple employees'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bulk Operations</h1>
          <p className="text-muted-foreground">
            Perform bulk operations on employee data including CSV import/export and mass updates.
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.length}</div>
            <p className="text-xs text-muted-foreground">
              Available for operations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Selected</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{selectedEmployees.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently selected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
            <p className="text-xs text-muted-foreground">
              Available departments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unassigned</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter(emp => emp.departmentId === null).length}
            </div>
            <p className="text-xs text-muted-foreground">
              In free bucket
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <Card>
        <CardHeader>
          <div className="flex space-x-1 rounded-lg bg-muted p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab(tab.id)}
                  className="flex-1"
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </Button>
              )
            })}
          </div>
        </CardHeader>

        <CardContent>
          <div className="mb-4">
            <h3 className="text-lg font-medium">
              {tabs.find(tab => tab.id === activeTab)?.label}
            </h3>
            <p className="text-sm text-muted-foreground">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>

          {/* Tab Content */}
          {activeTab === 'import' && (
            <CSVImportSection departments={departments} />
          )}

          {activeTab === 'export' && (
            <CSVExportSection 
              employees={employees} 
              departments={departments} 
            />
          )}

          {activeTab === 'transfer' && (
            <BulkTransferSection 
              employees={employees}
              departments={departments}
              selectedEmployees={selectedEmployees}
            />
          )}

          {activeTab === 'status' && (
            <BulkStatusUpdateSection 
              employees={employees}
              selectedEmployees={selectedEmployees}
            />
          )}
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Bulk Operations Guidelines
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-blue-700">
            <p><strong>CSV Import:</strong> Upload a CSV file with employee data. Required columns: name, email. Optional: departmentCode, status.</p>
            <p><strong>CSV Export:</strong> Download employee data in CSV format with filtering options.</p>
            <p><strong>Bulk Transfer:</strong> Move multiple employees between departments. Select employees from the Employees page first.</p>
            <p><strong>Status Update:</strong> Change the status of multiple employees at once (Active, Transferred, Archived, etc.).</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

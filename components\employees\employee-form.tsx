"use client"

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useHRStore } from '@/lib/store/hr-store'
import { Employee } from '@/lib/types'
import { previewNextId } from '@/lib/utils/id-generator'
import { 
  User, 
  Mail, 
  Calendar, 
  Building2,
  Save,
  X,
  Eye
} from 'lucide-react'
import { toast } from 'sonner'

const employeeSchema = z.object({
  name: z.string().min(2, 'اسم الموظف يجب أن يكون على الأقل حرفين'),
  email: z.string().email('البريد الإلكتروني غير صالح'),
  departmentId: z.string().optional(),
  hireDate: z.string().min(1, 'تاريخ التوظيف مطلوب'),
})

type EmployeeFormData = z.infer<typeof employeeSchema>

interface EmployeeFormProps {
  employee?: Employee
  onSuccess?: () => void
  onCancel?: () => void
  mode?: 'create' | 'edit'
}

export function EmployeeForm({ 
  employee, 
  onSuccess, 
  onCancel, 
  mode = 'create' 
}: EmployeeFormProps) {
  const { data: session } = useSession()
  const { departments, createEmployee, updateEmployeeData, isLoading } = useHRStore()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [previewId, setPreviewId] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      name: employee?.name || '',
      email: employee?.email || '',
      departmentId: employee?.departmentId || '',
      hireDate: employee?.hireDate 
        ? new Date(employee.hireDate).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0]
    }
  })

  const selectedDepartmentId = watch('departmentId')

  // Update preview ID when department changes
  React.useEffect(() => {
    if (mode === 'create' && selectedDepartmentId) {
      const department = departments.find(d => d.id === selectedDepartmentId)
      if (department) {
        const preview = previewNextId(department.code)
        setPreviewId(preview)
      }
    } else {
      setPreviewId(null)
    }
  }, [selectedDepartmentId, departments, mode])

  const onSubmit = async (data: EmployeeFormData) => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    setIsSubmitting(true)
    try {
      let success = false

      if (mode === 'create') {
        success = await createEmployee({
          name: data.name,
          email: data.email,
          departmentId: data.departmentId || null,
          hireDate: new Date(data.hireDate),
          userId: session.user.id
        })
      } else if (employee) {
        success = await updateEmployeeData(employee.id, {
          name: data.name,
          email: data.email,
          userId: session.user.id
        })
      }

      if (success) {
        toast.success(mode === 'create' ? 'تم إنشاء الموظف بنجاح' : 'تم تحديث الموظف بنجاح')
        onSuccess?.()
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
            <User className="h-4 w-4 text-white" />
          </div>
          {mode === 'create' ? 'إضافة موظف جديد' : 'تعديل بيانات الموظف'}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="container-spaced">
          {/* Employee ID Preview (for create mode) */}
          {mode === 'create' && previewId && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-4">
                <div className="flex items-center gap-3">
                  <Eye className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      معرف الموظف المتوقع
                    </p>
                    <p className="text-lg font-bold text-blue-700">{previewId}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Current Employee ID (for edit mode) */}
          {mode === 'edit' && employee && (
            <Card className="gradient-green-soft border-primary/20">
              <CardContent className="pt-4">
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium text-primary">
                      معرف الموظف
                    </p>
                    <p className="text-lg font-bold text-primary">{employee.id}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Name Field */}
          <div className="container-spaced">
            <Label htmlFor="name" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              اسم الموظف
            </Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="أدخل اسم الموظف"
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="container-spaced">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              البريد الإلكتروني
            </Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="أدخل البريد الإلكتروني"
              className={errors.email ? 'border-destructive' : ''}
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          {/* Department Field (only for create mode) */}
          {mode === 'create' && (
            <div className="container-spaced">
              <Label htmlFor="department" className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                القسم (اختياري)
              </Label>
              <Select
                value={selectedDepartmentId || ''}
                onValueChange={(value) => setValue('departmentId', value || undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر القسم أو اتركه فارغاً للسلة المؤقتة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">السلة المؤقتة</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name} ({dept.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Hire Date Field */}
          <div className="container-spaced">
            <Label htmlFor="hireDate" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              تاريخ التوظيف
            </Label>
            <Input
              id="hireDate"
              type="date"
              {...register('hireDate')}
              className={errors.hireDate ? 'border-destructive' : ''}
            />
            {errors.hireDate && (
              <p className="text-sm text-destructive">{errors.hireDate.message}</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="gradient-primary hover:opacity-90 shadow-medium hover-lift"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
            </Button>

            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="shadow-soft hover-lift border-2 border-border/50"
              >
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

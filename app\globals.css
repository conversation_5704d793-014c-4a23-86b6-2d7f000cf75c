@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Enterprise Color Palette */
    --background: 240 10% 98%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary: Professional Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 217 91% 55%;

    /* Secondary: Sophisticated Purple */
    --secondary: 262 83% 58%;
    --secondary-foreground: 0 0% 98%;

    /* Accent: Modern Teal */
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 98%;

    /* Neutral Grays */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;

    /* Status Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    /* Enhanced Radius */
    --radius: 0.75rem;

    /* Chart Colors - Modern Palette */
    --chart-1: 217 91% 60%;
    --chart-2: 262 83% 58%;
    --chart-3: 173 80% 40%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 240 10% 3.9%;
    --primary-hover: 217 91% 65%;

    --secondary: 262 83% 58%;
    --secondary-foreground: 240 10% 3.9%;

    --accent: 173 80% 40%;
    --accent-foreground: 240 10% 3.9%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 60%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 240 10% 3.9%;

    --chart-1: 217 91% 60%;
    --chart-2: 262 83% 58%;
    --chart-3: 173 80% 40%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }
}

@layer components {
  /* Modern Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  }

  /* Glass Morphism Effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
  }

  /* Hover Animations */
  .hover-lift {
    transition: all 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px -5px rgba(0, 0, 0, 0.15);
  }

  /* Status Indicators */
  .status-success {
    @apply bg-green-50 text-green-700 border-green-200;
  }

  .status-warning {
    @apply bg-yellow-50 text-yellow-700 border-yellow-200;
  }

  .status-error {
    @apply bg-red-50 text-red-700 border-red-200;
  }

  .status-info {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes pulse-soft {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-soft {
    animation: pulse-soft 2s infinite;
  }

  /* Responsive Grid Background */
  .bg-grid-slate-100 {
    background-image:
      linear-gradient(to right, rgb(241 245 249 / 0.5) 1px, transparent 1px),
      linear-gradient(to bottom, rgb(241 245 249 / 0.5) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Smooth Scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus Styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }
}
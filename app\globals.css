@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Enterprise Green Color Palette */
    --background: 120 10% 98%;
    --foreground: 120 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 120 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 120 10% 3.9%;

    /* Primary: Professional Forest Green */
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 142 71% 40%;

    /* Secondary: Sophisticated Emerald */
    --secondary: 160 84% 39%;
    --secondary-foreground: 0 0% 98%;

    /* Accent: Modern Sage Green */
    --accent: 120 60% 50%;
    --accent-foreground: 0 0% 98%;

    /* Neutral Grays with Green Undertones */
    --muted: 120 4.8% 95.9%;
    --muted-foreground: 120 3.8% 46.1%;
    --border: 120 5.9% 90%;
    --input: 120 5.9% 90%;
    --ring: 142 71% 45%;

    /* Status Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    /* Enhanced Radius */
    --radius: 0.75rem;

    /* Chart Colors - Green Palette */
    --chart-1: 142 71% 45%;
    --chart-2: 160 84% 39%;
    --chart-3: 120 60% 50%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }

  .dark {
    --background: 120 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 120 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 120 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 71% 55%;
    --primary-foreground: 120 10% 3.9%;
    --primary-hover: 142 71% 60%;

    --secondary: 160 84% 49%;
    --secondary-foreground: 120 10% 3.9%;

    --accent: 120 60% 60%;
    --accent-foreground: 120 10% 3.9%;

    --muted: 120 3.7% 15.9%;
    --muted-foreground: 120 5% 64.9%;
    --border: 120 3.7% 15.9%;
    --input: 120 3.7% 15.9%;
    --ring: 142 71% 55%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 120 10% 3.9%;

    --chart-1: 142 71% 55%;
    --chart-2: 160 84% 49%;
    --chart-3: 120 60% 60%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Reset and normalize margins to prevent interference */
  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    @apply bg-background text-foreground font-tajawal antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: 1.6;
  }

  /* Enhanced Typography with proper spacing isolation */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    margin-block: 0; /* Prevent margin collapse */
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  /* Paragraph spacing isolation */
  p {
    margin-block: 0;
    line-height: 1.7;
  }

  /* List spacing isolation */
  ul, ol {
    margin-block: 0;
    padding-inline-start: 1.5rem;
  }

  li {
    margin-block: 0;
  }

  /* Button and form element spacing */
  button, input, select, textarea {
    margin: 0;
  }
}

@layer components {
  /* Modern Green Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  }

  /* Green-specific gradients */
  .gradient-green-soft {
    background: linear-gradient(135deg, hsl(142 71% 45% / 0.1) 0%, hsl(160 84% 39% / 0.1) 100%);
  }

  .gradient-green-vibrant {
    background: linear-gradient(135deg, hsl(142 71% 45%) 0%, hsl(120 60% 50%) 100%);
  }

  /* Glass Morphism Effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
  }

  /* Hover Animations */
  .hover-lift {
    transition: all 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px -5px rgba(0, 0, 0, 0.15);
  }

  /* Status Indicators */
  .status-success {
    @apply bg-green-50 text-green-700 border-green-200;
  }

  .status-warning {
    @apply bg-yellow-50 text-yellow-700 border-yellow-200;
  }

  .status-error {
    @apply bg-red-50 text-red-700 border-red-200;
  }

  .status-info {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes pulse-soft {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-soft {
    animation: pulse-soft 2s infinite;
  }

  /* Responsive Grid Background */
  .bg-grid-slate-100 {
    background-image:
      linear-gradient(to right, rgb(241 245 249 / 0.5) 1px, transparent 1px),
      linear-gradient(to bottom, rgb(241 245 249 / 0.5) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Smooth Scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus Styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Modern Spacing Utilities - Prevent Margin Interference */
  .space-y-isolated > * + * {
    margin-top: var(--space-y, 1rem);
    margin-bottom: 0;
  }

  .space-x-isolated > * + * {
    margin-inline-start: var(--space-x, 1rem);
    margin-inline-end: 0;
  }

  /* Container spacing with gap instead of margins */
  .container-spaced {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .grid-spaced {
    display: grid;
    gap: 1rem;
  }

  /* Card spacing isolation */
  .card-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .card-content > * {
    margin: 0;
  }

  /* RTL-aware spacing */
  .rtl-space-x > * + * {
    margin-inline-start: 1rem;
  }

  .rtl-space-y > * + * {
    margin-block-start: 1rem;
  }

  /* Flexbox gap utilities for better spacing control */
  .flex-gap-1 { gap: 0.25rem; }
  .flex-gap-2 { gap: 0.5rem; }
  .flex-gap-3 { gap: 0.75rem; }
  .flex-gap-4 { gap: 1rem; }
  .flex-gap-6 { gap: 1.5rem; }
  .flex-gap-8 { gap: 2rem; }

  /* Grid gap utilities */
  .grid-gap-1 { gap: 0.25rem; }
  .grid-gap-2 { gap: 0.5rem; }
  .grid-gap-3 { gap: 0.75rem; }
  .grid-gap-4 { gap: 1rem; }
  .grid-gap-6 { gap: 1.5rem; }
  .grid-gap-8 { gap: 2rem; }
}
"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DepartmentCard } from "@/components/dashboard/department-card"
import { AddEmployeeDialog } from "@/components/employees/add-employee-dialog"
import { Department, Employee } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { useApiData } from "@/lib/hooks/use-api-data"
import {
  Users,
  Building2,
  UserPlus,
  Archive,
  TrendingUp,
  Download,
  Activity
} from "lucide-react"

interface DashboardPageProps {
  params?: Promise<Record<string, string>>
  searchParams?: Promise<Record<string, string | string[]>>
}

export default function DashboardPage({ params, searchParams }: DashboardPageProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { departments, employees, freeBucket } = useHRStore()
  const [isLoading, setIsLoading] = useState(true)
  const [showAddEmployeeDialog, setShowAddEmployeeDialog] = useState(false)

  // Load API data
  useApiData()

  useEffect(() => {
    // Simulate loading data
    const loadData = async () => {
      try {
        // Sample data is loaded via useSampleData hook
        await new Promise(resolve => setTimeout(resolve, 500))
        setIsLoading(false)
      } catch (error) {
        console.error("Failed to load dashboard data:", error)
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Handler functions
  const handleExportReport = () => {
    // Create CSV content
    const csvContent = [
      ['Department', 'Employee Count', 'Capacity', 'Utilization'],
      ...departments.map(dept => [
        dept.name,
        dept.employees?.length || 0,
        dept.capacity,
        `${Math.round(((dept.employees?.length || 0) / dept.capacity) * 100)}%`
      ])
    ].map(row => row.join(',')).join('\n')

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `dashboard-report-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  const totalEmployees = employees.length
  const totalDepartments = departments.length
  const freeBucketCount = freeBucket.length
  const averageUtilization = departments.length > 0 
    ? Math.round(
        departments.reduce((sum, dept) => {
          const employeeCount = dept.employees?.length || 0
          return sum + (employeeCount / dept.capacity) * 100
        }, 0) / departments.length
      )
    : 0

  const recentActivity = [
    { action: "تم نقل موظف", details: "تم نقل أحمد محمد إلى قسم الهندسة", time: "منذ ساعتين" },
    { action: "تم إضافة موظف جديد", details: "انضمت فاطمة أحمد إلى قسم التسويق", time: "منذ 4 ساعات" },
    { action: "تم إنشاء قسم جديد", details: "قسم البحث والتطوير", time: "منذ يوم واحد" },
  ]

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded w-20"></div>
                <div className="h-4 w-4 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container-spaced p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold gradient-primary bg-clip-text text-transparent">
            لوحة التحكم
          </h1>
          <p className="text-lg text-muted-foreground">
            أهلاً بك، {session?.user?.name}. إليك ما يحدث في مؤسستك.
          </p>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-soft hover-lift border-2 border-border/50"
            onClick={handleExportReport}
          >
            <Download className="h-4 w-4 ml-2" />
            تصدير التقرير
          </Button>
          <Button
            className="gradient-primary hover:opacity-90 shadow-medium hover-lift"
            onClick={() => setShowAddEmployeeDialog(true)}
          >
            <UserPlus className="h-4 w-4 ml-2" />
            إضافة موظف
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid-spaced md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-primary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-primary">إجمالي الموظفين</CardTitle>
            <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg">
              <Users className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">{totalEmployees}</div>
            <p className="text-sm text-primary/70 font-medium mt-1">
              +12% من الشهر الماضي
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-secondary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-secondary">الأقسام</CardTitle>
            <div className="w-10 h-10 bg-secondary rounded-xl flex items-center justify-center shadow-lg">
              <Building2 className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-secondary">{totalDepartments}</div>
            <p className="text-sm text-secondary/70 font-medium mt-1">
              الأقسام النشطة
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-accent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-accent">السلة المؤقتة</CardTitle>
            <div className="w-10 h-10 bg-accent rounded-xl flex items-center justify-center shadow-lg">
              <Archive className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-accent">{freeBucketCount}</div>
            <p className="text-sm text-accent/70 font-medium mt-1">
              موظفون غير مخصصين
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-success">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-success">متوسط الاستخدام</CardTitle>
            <div className="w-10 h-10 bg-success rounded-xl flex items-center justify-center shadow-lg">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-success">{averageUtilization}%</div>
            <p className="text-sm text-success/70 font-medium mt-1">
              سعة الأقسام
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Department Overview */}
      <div className="grid-spaced lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-3">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
                  <Building2 className="h-4 w-4 text-white" />
                </div>
                نظرة عامة على الأقسام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid-spaced md:grid-cols-2">
                {departments.slice(0, 6).map((department) => (
                  <DepartmentCard
                    key={department.id}
                    department={department}
                    onAddEmployee={() => {
                      // TODO: Implement add employee to department
                      console.log("Add employee to", department.name)
                    }}
                    onViewDetails={() => {
                      router.push('/departments')
                    }}
                  />
                ))}
              </div>

              {departments.length > 6 && (
                <div className="mt-6 text-center">
                  <Button
                    variant="outline"
                    className="shadow-soft hover-lift border-2 border-border/50"
                    onClick={() => router.push('/departments')}
                  >
                    عرض جميع الأقسام ({departments.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl font-bold text-foreground flex items-center gap-3">
                <div className="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                النشاط الأخير
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="container-spaced">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start rtl-space-x p-4 rounded-xl gradient-green-soft border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
                    <div className="w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <div className="flex-1 container-spaced mr-4">
                      <div className="text-sm font-semibold text-foreground">{activity.action}</div>
                      <div className="text-sm text-muted-foreground">
                        {activity.details}
                      </div>
                      <div className="text-xs text-muted-foreground font-medium">
                        {activity.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Employee Dialog */}
      <AddEmployeeDialog
        open={showAddEmployeeDialog}
        onOpenChange={setShowAddEmployeeDialog}
        departments={departments}
      />
    </div>
  )
}
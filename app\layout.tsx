import type { Metada<PERSON> } from "next"
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import { Providers } from "@/components/providers"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })
const tajawal = Tajawal({
  subsets: ["arabic", "latin"],
  weight: ["200", "300", "400", "500", "700", "800", "900"],
  variable: "--font-tajawal",
  display: "swap"
})

export const metadata: Metadata = {
  title: "HR Synergy - نظام إدارة الموظفين المؤسسي",
  description: "نظام إدارة الموارد البشرية الشامل للمؤسسات الكبيرة",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${tajawal.variable} ${inter.className} font-sans`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
{"name": "y-indexeddb", "version": "9.0.12", "description": "IndexedDB database adapter for Yjs", "type": "module", "main": "./dist/y-indexeddb.cjs", "module": "./src/y-indexeddb.js", "types": "./dist/src/y-indexeddb.d.ts", "sideEffects": false, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "scripts": {"clean": "rm -rf dist", "test": "npm run lint", "dist": "rollup -c", "lint": "markdownlint README.md && standard && tsc", "preversion": "npm run clean && npm run lint && npm run dist", "debug": "concurrently 'rollup -wc' 'http-server -o .'"}, "files": ["dist/*", "src/*"], "exports": {".": {"types": "./dist/src/y-indexeddb.d.ts", "module": "./src/y-indexeddb.js", "import": "./src/y-indexeddb.js", "require": "./dist/y-indexeddb.cjs", "default": "./src/y-indexeddb.js"}, "./package.json": "./package.json"}, "standard": {"ignore": ["/dist", "/node_modules", "/docs"]}, "repository": {"type": "git", "url": "git+https://github.com/yjs/y-indexeddb.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "CRDT", "offline", "shared editing", "collaboration", "concurrency"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yjs/y-indexeddb/issues"}, "homepage": "https://yjs.dev", "dependencies": {"lib0": "^0.2.74"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "concurrently": "^3.6.1", "http-server": "^0.12.3", "jsdoc": "^3.6.6", "markdownlint-cli": "^0.19.0", "rollup": "^1.32.1", "standard": "^11.0.1", "typescript": "^5.0.4", "y-protocols": "^1.0.1", "yjs": "^13.4.7"}, "peerDependencies": {"yjs": "^13.0.0"}, "engines": {"npm": ">=8.0.0", "node": ">=16.0.0"}}
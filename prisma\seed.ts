import { PrismaClient } from '@prisma/client'
import { sampleDepartments, sampleEmployees } from '../lib/sample-data'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  console.log('🧹 Clearing existing data...')
  await prisma.employee.deleteMany()
  await prisma.department.deleteMany()

  // Seed departments
  console.log('🏢 Seeding departments...')
  for (const dept of sampleDepartments) {
    await prisma.department.create({
      data: {
        id: dept.id,
        name: dept.name,
        code: dept.code,
        capacity: dept.capacity,
        createdAt: dept.createdAt,
        updatedAt: dept.updatedAt
      }
    })
    console.log(`  ✅ Created department: ${dept.name} (${dept.code})`)
  }

  // Seed employees
  console.log('👥 Seeding employees...')
  for (const emp of sampleEmployees) {
    await prisma.employee.create({
      data: {
        id: emp.id,
        name: emp.name,
        email: emp.email,
        departmentId: emp.departmentId,
        status: emp.status,
        hireDate: emp.hireDate,
        transferHistory: emp.transferHistory,
        createdAt: emp.createdAt,
        updatedAt: emp.updatedAt
      }
    })
    console.log(`  ✅ Created employee: ${emp.name} (${emp.id})`)
  }

  // Create admin user
  console.log('👤 Creating admin user...')
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'ADMIN',
      password: 'admin123' // In production, this should be hashed
    }
  })

  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'HR Manager',
      role: 'HR_MANAGER',
      password: 'hr123' // In production, this should be hashed
    }
  })

  console.log('✅ Database seeding completed successfully!')

  // Print summary
  const departmentCount = await prisma.department.count()
  const employeeCount = await prisma.employee.count()
  const userCount = await prisma.user.count()

  console.log('\n📊 Seeding Summary:')
  console.log(`  Departments: ${departmentCount}`)
  console.log(`  Employees: ${employeeCount}`)
  console.log(`  Users: ${userCount}`)
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

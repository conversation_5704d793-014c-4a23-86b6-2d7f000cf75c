import { Employee, Department, BulkOperation } from '@/lib/types'
import { employeeService } from '@/lib/services/employee-service'
import { distributionService } from '@/lib/services/distribution-service'
import { toast } from 'sonner'

export interface FreeBucketStats {
  totalEmployees: number
  recentlyAdded: Employee[]
  longestStaying: Employee[]
  byStatus: Record<Employee['status'], number>
  averageStayDuration: number
}

export interface FreeBucketOperation {
  type: 'assign' | 'distribute' | 'archive' | 'restore'
  employeeIds: string[]
  targetDepartmentId?: string
  distributionStrategy?: 'round-robin' | 'least-loaded' | 'capacity-weighted'
}

export interface FreeBucketServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
  processed?: number
  failed?: number
}

export class FreeBucketService {
  private static instance: FreeBucketService

  private constructor() {}

  static getInstance(): FreeBucketService {
    if (!FreeBucketService.instance) {
      FreeBucketService.instance = new FreeBucketService()
    }
    return FreeBucketService.instance
  }

  /**
   * Get Free Bucket statistics
   */
  getFreeBucketStats(employees: Employee[]): FreeBucketStats {
    const freeBucketEmployees = employees.filter(emp => 
      emp.departmentId === null && emp.status !== 'ARCHIVED'
    )

    // Calculate average stay duration
    const now = new Date()
    const stayDurations = freeBucketEmployees.map(emp => {
      const lastTransfer = emp.transferHistory
        .filter(t => t.toDepartmentId === null)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
      
      const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(emp.createdAt)
      return now.getTime() - transferDate.getTime()
    })

    const averageStayDuration = stayDurations.length > 0
      ? stayDurations.reduce((sum, duration) => sum + duration, 0) / stayDurations.length
      : 0

    // Recently added (last 7 days)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const recentlyAdded = freeBucketEmployees.filter(emp => {
      const lastTransfer = emp.transferHistory
        .filter(t => t.toDepartmentId === null)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
      
      const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(emp.createdAt)
      return transferDate >= sevenDaysAgo
    }).sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())

    // Longest staying employees
    const longestStaying = freeBucketEmployees
      .map(emp => {
        const lastTransfer = emp.transferHistory
          .filter(t => t.toDepartmentId === null)
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
        
        const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(emp.createdAt)
        return {
          ...emp,
          stayDuration: now.getTime() - transferDate.getTime()
        }
      })
      .sort((a, b) => b.stayDuration - a.stayDuration)
      .slice(0, 5)

    // Group by status
    const byStatus = freeBucketEmployees.reduce((acc, emp) => {
      acc[emp.status] = (acc[emp.status] || 0) + 1
      return acc
    }, {} as Record<Employee['status'], number>)

    return {
      totalEmployees: freeBucketEmployees.length,
      recentlyAdded: recentlyAdded.slice(0, 5),
      longestStaying,
      byStatus,
      averageStayDuration: Math.round(averageStayDuration / (1000 * 60 * 60 * 24)) // Convert to days
    }
  }

  /**
   * Move employees to Free Bucket
   */
  async moveToFreeBucket(
    employeeIds: string[],
    reason: string,
    userId: string,
    employees: Employee[]
  ): Promise<FreeBucketServiceResult<Employee[]>> {
    const results: Employee[] = []
    const errors: string[] = []

    for (const employeeId of employeeIds) {
      try {
        const employee = employees.find(emp => emp.id === employeeId)
        if (!employee) {
          errors.push(`الموظف ${employeeId} غير موجود`)
          continue
        }

        if (employee.departmentId === null) {
          errors.push(`الموظف ${employee.name} موجود بالفعل في السلة المؤقتة`)
          continue
        }

        // Create transfer record
        const transferRecord = {
          fromDepartmentId: employee.departmentId,
          toDepartmentId: null,
          timestamp: new Date(),
          reason: reason || 'نقل إلى السلة المؤقتة',
          userId
        }

        const updatedEmployee: Employee = {
          ...employee,
          departmentId: null,
          status: 'PENDING_REMOVAL',
          transferHistory: [...employee.transferHistory, transferRecord],
          updatedAt: new Date()
        }

        results.push(updatedEmployee)
      } catch (error) {
        errors.push(`خطأ في نقل الموظف ${employeeId}: ${error}`)
      }
    }

    if (errors.length > 0) {
      toast.error(`فشل في نقل ${errors.length} موظف`)
    }

    if (results.length > 0) {
      toast.success(`تم نقل ${results.length} موظف إلى السلة المؤقتة`)
    }

    return {
      success: results.length > 0,
      data: results,
      processed: results.length,
      failed: errors.length,
      error: errors.length > 0 ? errors.join('\n') : undefined
    }
  }

  /**
   * Assign employees from Free Bucket to departments
   */
  async assignFromFreeBucket(
    employeeIds: string[],
    targetDepartmentId: string,
    userId: string,
    employees: Employee[],
    departments: Department[]
  ): Promise<FreeBucketServiceResult<Employee[]>> {
    const targetDepartment = departments.find(d => d.id === targetDepartmentId)
    if (!targetDepartment) {
      return {
        success: false,
        error: 'القسم المستهدف غير موجود'
      }
    }

    // Check capacity
    const currentEmployeeCount = employees.filter(emp => 
      emp.departmentId === targetDepartmentId && emp.status === 'ACTIVE'
    ).length

    const availableSlots = targetDepartment.capacity - currentEmployeeCount
    if (availableSlots < employeeIds.length) {
      return {
        success: false,
        error: `القسم يحتوي على ${availableSlots} مقعد متاح فقط، ولكن تم اختيار ${employeeIds.length} موظف`
      }
    }

    const results: Employee[] = []
    const errors: string[] = []

    for (const employeeId of employeeIds) {
      try {
        const employee = employees.find(emp => emp.id === employeeId)
        if (!employee) {
          errors.push(`الموظف ${employeeId} غير موجود`)
          continue
        }

        if (employee.departmentId !== null) {
          errors.push(`الموظف ${employee.name} ليس في السلة المؤقتة`)
          continue
        }

        // Create transfer record
        const transferRecord = {
          fromDepartmentId: null,
          toDepartmentId: targetDepartmentId,
          timestamp: new Date(),
          reason: `تخصيص من السلة المؤقتة إلى ${targetDepartment.name}`,
          userId
        }

        const updatedEmployee: Employee = {
          ...employee,
          departmentId: targetDepartmentId,
          status: 'ACTIVE',
          transferHistory: [...employee.transferHistory, transferRecord],
          updatedAt: new Date()
        }

        results.push(updatedEmployee)
      } catch (error) {
        errors.push(`خطأ في تخصيص الموظف ${employeeId}: ${error}`)
      }
    }

    if (errors.length > 0) {
      toast.error(`فشل في تخصيص ${errors.length} موظف`)
    }

    if (results.length > 0) {
      toast.success(`تم تخصيص ${results.length} موظف إلى ${targetDepartment.name}`)
    }

    return {
      success: results.length > 0,
      data: results,
      processed: results.length,
      failed: errors.length,
      error: errors.length > 0 ? errors.join('\n') : undefined
    }
  }

  /**
   * Smart distribute employees from Free Bucket
   */
  async smartDistributeFromFreeBucket(
    employeeIds: string[],
    strategy: 'round-robin' | 'least-loaded' | 'capacity-weighted',
    userId: string,
    employees: Employee[],
    departments: Department[]
  ): Promise<FreeBucketServiceResult<Employee[]>> {
    const freeBucketEmployees = employees.filter(emp => 
      employeeIds.includes(emp.id) && emp.departmentId === null
    )

    if (freeBucketEmployees.length === 0) {
      return {
        success: false,
        error: 'لا توجد موظفين في السلة المؤقتة للتوزيع'
      }
    }

    try {
      const result = await distributionService.distributeEmployees(
        freeBucketEmployees,
        departments,
        {
          strategy,
          respectCapacity: true,
          allowOverflow: false
        },
        userId
      )

      if (result.success) {
        toast.success(`تم توزيع ${result.summary.successfullyDistributed} موظف بنجاح`)
        
        if (result.summary.overflowCount > 0) {
          toast.warning(`${result.summary.overflowCount} موظف لم يتم توزيعهم بسبب عدم توفر مقاعد`)
        }
      }

      return {
        success: result.success,
        data: result.distributed,
        processed: result.summary.successfullyDistributed,
        failed: result.summary.errorCount,
        error: result.errors.length > 0 ? result.errors.join('\n') : undefined
      }
    } catch (error) {
      return {
        success: false,
        error: `خطأ في التوزيع الذكي: ${error}`
      }
    }
  }

  /**
   * Archive employees from Free Bucket
   */
  async archiveFromFreeBucket(
    employeeIds: string[],
    userId: string,
    employees: Employee[]
  ): Promise<FreeBucketServiceResult<Employee[]>> {
    const results: Employee[] = []
    const errors: string[] = []

    for (const employeeId of employeeIds) {
      try {
        const result = await employeeService.archiveEmployee(employeeId, userId, employees)
        
        if (result.success && result.data) {
          results.push(result.data)
        } else {
          errors.push(`فشل في أرشفة الموظف ${employeeId}: ${result.error}`)
        }
      } catch (error) {
        errors.push(`خطأ في أرشفة الموظف ${employeeId}: ${error}`)
      }
    }

    if (errors.length > 0) {
      toast.error(`فشل في أرشفة ${errors.length} موظف`)
    }

    if (results.length > 0) {
      toast.success(`تم أرشفة ${results.length} موظف من السلة المؤقتة`)
    }

    return {
      success: results.length > 0,
      data: results,
      processed: results.length,
      failed: errors.length,
      error: errors.length > 0 ? errors.join('\n') : undefined
    }
  }

  /**
   * Clean up old employees from Free Bucket
   */
  async cleanupFreeBucket(
    daysThreshold: number,
    userId: string,
    employees: Employee[]
  ): Promise<FreeBucketServiceResult<Employee[]>> {
    const now = new Date()
    const thresholdDate = new Date(now.getTime() - daysThreshold * 24 * 60 * 60 * 1000)

    const oldEmployees = employees.filter(emp => {
      if (emp.departmentId !== null || emp.status === 'ARCHIVED') return false

      const lastTransfer = emp.transferHistory
        .filter(t => t.toDepartmentId === null)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
      
      const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(emp.createdAt)
      return transferDate <= thresholdDate
    })

    if (oldEmployees.length === 0) {
      return {
        success: true,
        data: [],
        processed: 0,
        failed: 0
      }
    }

    const confirmed = window.confirm(
      `تم العثور على ${oldEmployees.length} موظف في السلة المؤقتة لأكثر من ${daysThreshold} يوم. هل تريد أرشفتهم؟`
    )

    if (!confirmed) {
      return {
        success: false,
        error: 'تم إلغاء عملية التنظيف'
      }
    }

    return this.archiveFromFreeBucket(
      oldEmployees.map(emp => emp.id),
      userId,
      employees
    )
  }

  /**
   * Get Free Bucket recommendations
   */
  getFreeBucketRecommendations(
    employees: Employee[],
    departments: Department[]
  ): {
    shouldDistribute: boolean
    shouldCleanup: boolean
    recommendedActions: string[]
  } {
    const stats = this.getFreeBucketStats(employees)
    const recommendations: string[] = []
    let shouldDistribute = false
    let shouldCleanup = false

    // Check if there are too many employees in free bucket
    if (stats.totalEmployees > 10) {
      shouldDistribute = true
      recommendations.push(`يوجد ${stats.totalEmployees} موظف في السلة المؤقتة. يُنصح بتوزيعهم على الأقسام.`)
    }

    // Check for old employees
    if (stats.averageStayDuration > 30) {
      shouldCleanup = true
      recommendations.push(`متوسط البقاء في السلة المؤقتة هو ${stats.averageStayDuration} يوم. يُنصح بمراجعة الموظفين القدامى.`)
    }

    // Check for pending removal employees
    if (stats.byStatus['PENDING_REMOVAL'] > 5) {
      recommendations.push(`يوجد ${stats.byStatus['PENDING_REMOVAL']} موظف في انتظار الإزالة. يُنصح بمراجعتهم.`)
    }

    // Check department capacity
    const totalCapacity = departments.reduce((sum, dept) => sum + dept.capacity, 0)
    const totalAssigned = employees.filter(emp => emp.departmentId && emp.status === 'ACTIVE').length
    const availableCapacity = totalCapacity - totalAssigned

    if (availableCapacity < stats.totalEmployees) {
      recommendations.push(`السعة المتاحة (${availableCapacity}) أقل من عدد الموظفين في السلة المؤقتة. قد تحتاج لزيادة سعة الأقسام.`)
    }

    return {
      shouldDistribute,
      shouldCleanup,
      recommendedActions: recommendations
    }
  }

  /**
   * Format stay duration for display
   */
  formatStayDuration(employee: Employee): string {
    const now = new Date()
    const lastTransfer = employee.transferHistory
      .filter(t => t.toDepartmentId === null)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
    
    const transferDate = lastTransfer ? new Date(lastTransfer.timestamp) : new Date(employee.createdAt)
    const durationMs = now.getTime() - transferDate.getTime()
    const days = Math.floor(durationMs / (1000 * 60 * 60 * 24))
    const hours = Math.floor((durationMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) {
      return `${days} يوم${days > 1 ? '' : ''}`
    } else if (hours > 0) {
      return `${hours} ساعة${hours > 1 ? '' : ''}`
    } else {
      return 'أقل من ساعة'
    }
  }
}

// Export singleton instance
export const freeBucketService = FreeBucketService.getInstance()

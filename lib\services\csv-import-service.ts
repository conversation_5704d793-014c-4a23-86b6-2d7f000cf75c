import Papa from 'papaparse'
import { Employee, Department, CSVImportResult } from '@/lib/types'
import { employeeService } from '@/lib/services/employee-service'
import { idGenerator } from '@/lib/utils/id-generator'

export interface CSVImportProgress {
  total: number
  processed: number
  successful: number
  failed: number
  percentage: number
  currentRow?: number
  status: 'parsing' | 'validating' | 'importing' | 'completed' | 'error'
}

export interface CSVValidationError {
  row: number
  field: string
  value: any
  error: string
  data: any
}

export interface CSVImportOptions {
  skipFirstRow: boolean
  departmentMapping: 'name' | 'code' | 'id'
  createMissingDepartments: boolean
  defaultDepartmentId?: string
  onProgress?: (progress: CSVImportProgress) => void
}

export interface CSVTemplate {
  name: string
  description: string
  headers: string[]
  example: Record<string, string>
  required: string[]
}

export class CSVImportService {
  private static instance: CSVImportService

  private constructor() {}

  static getInstance(): CSVImportService {
    if (!CSVImportService.instance) {
      CSVImportService.instance = new CSVImportService()
    }
    return CSVImportService.instance
  }

  /**
   * Get CSV templates for different import scenarios
   */
  getTemplates(): CSVTemplate[] {
    return [
      {
        name: 'قالب أساسي',
        description: 'قالب بسيط لاستيراد الموظفين مع المعلومات الأساسية',
        headers: ['الاسم', 'البريد الإلكتروني', 'القسم', 'تاريخ التوظيف'],
        example: {
          'الاسم': 'أحمد محمد',
          'البريد الإلكتروني': '<EMAIL>',
          'القسم': 'الهندسة',
          'تاريخ التوظيف': '2024-01-15'
        },
        required: ['الاسم', 'البريد الإلكتروني']
      },
      {
        name: 'قالب متقدم',
        description: 'قالب شامل مع جميع المعلومات المتاحة',
        headers: ['الاسم', 'البريد الإلكتروني', 'رمز القسم', 'اسم القسم', 'تاريخ التوظيف', 'الحالة'],
        example: {
          'الاسم': 'فاطمة أحمد',
          'البريد الإلكتروني': '<EMAIL>',
          'رمز القسم': 'ENG',
          'اسم القسم': 'الهندسة',
          'تاريخ التوظيف': '2024-02-01',
          'الحالة': 'نشط'
        },
        required: ['الاسم', 'البريد الإلكتروني']
      }
    ]
  }

  /**
   * Generate CSV template file
   */
  generateTemplate(template: CSVTemplate): string {
    const csv = Papa.unparse([
      template.headers,
      Object.values(template.example)
    ])
    return csv
  }

  /**
   * Parse CSV file and validate data
   */
  async parseCSV(
    file: File,
    options: CSVImportOptions
  ): Promise<{
    data: any[]
    errors: CSVValidationError[]
    headers: string[]
  }> {
    return new Promise((resolve, reject) => {
      const errors: CSVValidationError[] = []
      let headers: string[] = []

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        encoding: 'UTF-8',
        transformHeader: (header) => header.trim(),
        complete: (results) => {
          headers = results.meta.fields || []
          
          // Validate headers
          const requiredHeaders = ['الاسم', 'البريد الإلكتروني']
          const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))
          
          if (missingHeaders.length > 0) {
            errors.push({
              row: 0,
              field: 'headers',
              value: headers,
              error: `الأعمدة المطلوبة مفقودة: ${missingHeaders.join(', ')}`,
              data: {}
            })
          }

          resolve({
            data: results.data as any[],
            errors,
            headers
          })
        },
        error: (error) => {
          reject(new Error(`خطأ في تحليل ملف CSV: ${error.message}`))
        }
      })
    })
  }

  /**
   * Validate CSV data
   */
  async validateCSVData(
    data: any[],
    departments: Department[],
    existingEmployees: Employee[],
    options: CSVImportOptions
  ): Promise<CSVValidationError[]> {
    const errors: CSVValidationError[] = []
    const emailSet = new Set(existingEmployees.map(emp => emp.email.toLowerCase()))
    const csvEmails = new Set<string>()

    for (let i = 0; i < data.length; i++) {
      const row = data[i]
      const rowNumber = i + 1

      // Validate name
      if (!row['الاسم'] || typeof row['الاسم'] !== 'string' || row['الاسم'].trim().length < 2) {
        errors.push({
          row: rowNumber,
          field: 'الاسم',
          value: row['الاسم'],
          error: 'اسم الموظف مطلوب ويجب أن يكون على الأقل حرفين',
          data: row
        })
      }

      // Validate email
      const email = row['البريد الإلكتروني']
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      
      if (!email || !emailRegex.test(email)) {
        errors.push({
          row: rowNumber,
          field: 'البريد الإلكتروني',
          value: email,
          error: 'البريد الإلكتروني غير صالح',
          data: row
        })
      } else {
        const normalizedEmail = email.toLowerCase()
        
        // Check for duplicates in existing employees
        if (emailSet.has(normalizedEmail)) {
          errors.push({
            row: rowNumber,
            field: 'البريد الإلكتروني',
            value: email,
            error: 'البريد الإلكتروني موجود بالفعل في النظام',
            data: row
          })
        }
        
        // Check for duplicates within CSV
        if (csvEmails.has(normalizedEmail)) {
          errors.push({
            row: rowNumber,
            field: 'البريد الإلكتروني',
            value: email,
            error: 'البريد الإلكتروني مكرر في ملف CSV',
            data: row
          })
        } else {
          csvEmails.add(normalizedEmail)
        }
      }

      // Validate department
      const departmentValue = row['القسم'] || row['اسم القسم'] || row['رمز القسم']
      if (departmentValue) {
        const department = this.findDepartment(departmentValue, departments, options.departmentMapping)
        if (!department && !options.createMissingDepartments) {
          errors.push({
            row: rowNumber,
            field: 'القسم',
            value: departmentValue,
            error: 'القسم غير موجود في النظام',
            data: row
          })
        }
      }

      // Validate hire date
      const hireDate = row['تاريخ التوظيف']
      if (hireDate) {
        const date = new Date(hireDate)
        if (isNaN(date.getTime()) || date > new Date()) {
          errors.push({
            row: rowNumber,
            field: 'تاريخ التوظيف',
            value: hireDate,
            error: 'تاريخ التوظيف غير صالح',
            data: row
          })
        }
      }

      // Validate status
      const status = row['الحالة']
      if (status) {
        const validStatuses = ['نشط', 'منقول', 'في انتظار الإزالة', 'مؤرشف']
        if (!validStatuses.includes(status)) {
          errors.push({
            row: rowNumber,
            field: 'الحالة',
            value: status,
            error: `الحالة غير صالحة. القيم المسموحة: ${validStatuses.join(', ')}`,
            data: row
          })
        }
      }
    }

    return errors
  }

  /**
   * Import validated CSV data
   */
  async importCSVData(
    data: any[],
    departments: Department[],
    existingEmployees: Employee[],
    options: CSVImportOptions,
    userId: string
  ): Promise<CSVImportResult> {
    const result: CSVImportResult = {
      success: [],
      errors: []
    }

    const progress: CSVImportProgress = {
      total: data.length,
      processed: 0,
      successful: 0,
      failed: 0,
      percentage: 0,
      status: 'importing'
    }

    for (let i = 0; i < data.length; i++) {
      const row = data[i]
      const rowNumber = i + 1

      try {
        progress.currentRow = rowNumber
        progress.status = 'importing'
        options.onProgress?.(progress)

        // Find or create department
        let departmentId: string | null = null
        const departmentValue = row['القسم'] || row['اسم القسم'] || row['رمز القسم']
        
        if (departmentValue) {
          const department = this.findDepartment(departmentValue, departments, options.departmentMapping)
          departmentId = department?.id || options.defaultDepartmentId || null
        } else {
          departmentId = options.defaultDepartmentId || null
        }

        // Parse hire date
        const hireDateValue = row['تاريخ التوظيف']
        const hireDate = hireDateValue ? new Date(hireDateValue) : new Date()

        // Create employee
        const employeeResult = await employeeService.createEmployee(
          {
            name: row['الاسم'].trim(),
            email: row['البريد الإلكتروني'].toLowerCase().trim(),
            departmentId,
            hireDate,
            userId
          },
          departments,
          [...existingEmployees, ...result.success]
        )

        if (employeeResult.success && employeeResult.data) {
          result.success.push(employeeResult.data)
          progress.successful++
        } else {
          result.errors.push({
            row: rowNumber,
            data: row,
            error: employeeResult.error || 'فشل في إنشاء الموظف'
          })
          progress.failed++
        }
      } catch (error) {
        result.errors.push({
          row: rowNumber,
          data: row,
          error: `خطأ غير متوقع: ${error}`
        })
        progress.failed++
      }

      progress.processed++
      progress.percentage = Math.round((progress.processed / progress.total) * 100)
      options.onProgress?.(progress)

      // Add small delay to prevent UI blocking
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1))
      }
    }

    progress.status = 'completed'
    options.onProgress?.(progress)

    return result
  }

  /**
   * Find department by name, code, or ID
   */
  private findDepartment(
    value: string,
    departments: Department[],
    mapping: 'name' | 'code' | 'id'
  ): Department | undefined {
    const normalizedValue = value.trim()

    switch (mapping) {
      case 'name':
        return departments.find(d => d.name.toLowerCase() === normalizedValue.toLowerCase())
      case 'code':
        return departments.find(d => d.code.toUpperCase() === normalizedValue.toUpperCase())
      case 'id':
        return departments.find(d => d.id === normalizedValue)
      default:
        // Try all mappings
        return departments.find(d => 
          d.name.toLowerCase() === normalizedValue.toLowerCase() ||
          d.code.toUpperCase() === normalizedValue.toUpperCase() ||
          d.id === normalizedValue
        )
    }
  }

  /**
   * Download CSV template
   */
  downloadTemplate(template: CSVTemplate): void {
    const csv = this.generateTemplate(template)
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `${template.name}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  /**
   * Export employees to CSV
   */
  exportEmployees(employees: Employee[], departments: Department[]): void {
    const data = employees.map(emp => {
      const department = departments.find(d => d.id === emp.departmentId)
      return {
        'معرف الموظف': emp.id,
        'الاسم': emp.name,
        'البريد الإلكتروني': emp.email,
        'القسم': department?.name || 'غير مخصص',
        'رمز القسم': department?.code || '',
        'الحالة': this.translateStatus(emp.status),
        'تاريخ التوظيف': new Date(emp.hireDate).toLocaleDateString('ar-SA'),
        'تاريخ الإنشاء': new Date(emp.createdAt).toLocaleDateString('ar-SA'),
        'آخر تحديث': new Date(emp.updatedAt).toLocaleDateString('ar-SA')
      }
    })

    const csv = Papa.unparse(data)
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `employees_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  /**
   * Translate employee status to Arabic
   */
  private translateStatus(status: Employee['status']): string {
    const statusMap = {
      'ACTIVE': 'نشط',
      'TRANSFERRED': 'منقول',
      'PENDING_REMOVAL': 'في انتظار الإزالة',
      'ARCHIVED': 'مؤرشف'
    }
    return statusMap[status] || status
  }
}

// Export singleton instance
export const csvImportService = CSVImportService.getInstance()

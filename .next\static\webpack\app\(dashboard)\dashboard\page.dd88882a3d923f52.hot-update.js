"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/compiler-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/compiler-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/department-card */ \"(app-pages-browser)/./components/dashboard/department-card.tsx\");\n/* harmony import */ var _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/store/hr-store */ \"(app-pages-browser)/./lib/store/hr-store.ts\");\n/* harmony import */ var _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/use-api-data */ \"(app-pages-browser)/./lib/hooks/use-api-data.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,Building2,Download,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage(t0) {\n    var _session_user;\n    _s();\n    const $ = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_1__.c)(72);\n    if ($[0] !== \"49ae206026355890e667d8c49ebd4b8ac4c1894acdb4b356195479c27c1e6a51\") {\n        for(let $i = 0; $i < 72; $i += 1){\n            $[$i] = Symbol.for(\"react.memo_cache_sentinel\");\n        }\n        $[0] = \"49ae206026355890e667d8c49ebd4b8ac4c1894acdb4b356195479c27c1e6a51\";\n    }\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { departments, employees, freeBucket } = (0,_lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [, setShowAddEmployeeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,_lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData)();\n    let t1;\n    let t2;\n    if ($[1] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t1 = ()=>{\n            const loadData = async ()=>{\n                ;\n                try {\n                    await new Promise(_temp);\n                    setIsLoading(false);\n                } catch (t3) {\n                    const error = t3;\n                    console.error(\"Failed to load dashboard data:\", error);\n                    setIsLoading(false);\n                }\n            };\n            loadData();\n        };\n        t2 = [];\n        $[1] = t1;\n        $[2] = t2;\n    } else {\n        t1 = $[1];\n        t2 = $[2];\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(t1, t2);\n    let t3;\n    if ($[3] !== departments) {\n        t3 = ()=>{\n            const csvContent = [\n                [\n                    \"Department\",\n                    \"Employee Count\",\n                    \"Capacity\",\n                    \"Utilization\"\n                ],\n                ...departments.map(_temp2)\n            ].map(_temp3).join(\"\\n\");\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv\"\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"dashboard-report-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n        };\n        $[3] = departments;\n        $[4] = t3;\n    } else {\n        t3 = $[4];\n    }\n    const handleExportReport = t3;\n    const totalEmployees = employees.length;\n    const totalDepartments = departments.length;\n    const freeBucketCount = freeBucket.length;\n    let t4;\n    if ($[5] !== departments) {\n        t4 = departments.length > 0 ? Math.round(departments.reduce(_temp4, 0) / departments.length) : 0;\n        $[5] = departments;\n        $[6] = t4;\n    } else {\n        t4 = $[6];\n    }\n    const averageUtilization = t4;\n    let t5;\n    if ($[7] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t5 = [\n            {\n                action: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0645\\u0648\\u0638\\u0641\",\n                details: \"\\u062A\\u0645 \\u0646\\u0642\\u0644 \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u0647\\u0646\\u062F\\u0633\\u0629\",\n                time: \"\\u0645\\u0646\\u0630 \\u0633\\u0627\\u0639\\u062A\\u064A\\u0646\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0638\\u0641 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0627\\u0646\\u0636\\u0645\\u062A \\u0641\\u0627\\u0637\\u0645\\u0629 \\u0623\\u062D\\u0645\\u062F \\u0625\\u0644\\u0649 \\u0642\\u0633\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642\",\n                time: \"\\u0645\\u0646\\u0630 4 \\u0633\\u0627\\u0639\\u0627\\u062A\"\n            },\n            {\n                action: \"\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\",\n                details: \"\\u0642\\u0633\\u0645 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0648\\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631\",\n                time: \"\\u0645\\u0646\\u0630 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F\"\n            }\n        ];\n        $[7] = t5;\n    } else {\n        t5 = $[7];\n    }\n    const recentActivity = t5;\n    if (isLoading) {\n        let t6;\n        if ($[8] === Symbol.for(\"react.memo_cache_sentinel\")) {\n            t6 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        ...Array(4)\n                    ].map(_temp5)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 39\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 12\n            }, this);\n            $[8] = t6;\n        } else {\n            t6 = $[8];\n        }\n        return t6;\n    }\n    let t6;\n    if ($[9] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t6 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-4xl font-bold gradient-primary bg-clip-text text-transparent\",\n            children: \"لوحة التحكم\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 10\n        }, this);\n        $[9] = t6;\n    } else {\n        t6 = $[9];\n    }\n    const t7 = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name;\n    let t8;\n    if ($[10] !== t7) {\n        t8 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                t6,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground\",\n                    children: [\n                        \"أهلاً بك، \",\n                        t7,\n                        \". إليك ما يحدث في مؤسستك.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 41\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 10\n        }, this);\n        $[10] = t7;\n        $[11] = t8;\n    } else {\n        t8 = $[11];\n    }\n    let t9;\n    if ($[12] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t9 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-4 w-4 ml-2\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 10\n        }, this);\n        $[12] = t9;\n    } else {\n        t9 = $[12];\n    }\n    let t10;\n    if ($[13] !== handleExportReport) {\n        t10 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            variant: \"outline\",\n            className: \"shadow-soft hover-lift border-2 border-border/50\",\n            onClick: handleExportReport,\n            children: [\n                t9,\n                \"تصدير التقرير\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 11\n        }, this);\n        $[13] = handleExportReport;\n        $[14] = t10;\n    } else {\n        t10 = $[14];\n    }\n    let t11;\n    if ($[15] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t11 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            className: \"gradient-primary hover:opacity-90 shadow-medium hover-lift\",\n            onClick: ()=>setShowAddEmployeeDialog(true),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 ml-2\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 137\n                }, this),\n                \"إضافة موظف\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 11\n        }, this);\n        $[15] = t11;\n    } else {\n        t11 = $[15];\n    }\n    let t12;\n    if ($[16] !== t10) {\n        t12 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3\",\n            children: [\n                t10,\n                t11\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 11\n        }, this);\n        $[16] = t10;\n        $[17] = t12;\n    } else {\n        t12 = $[17];\n    }\n    let t13;\n    if ($[18] !== t12 || $[19] !== t8) {\n        t13 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                t8,\n                t12\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 11\n        }, this);\n        $[18] = t12;\n        $[19] = t8;\n        $[20] = t13;\n    } else {\n        t13 = $[20];\n    }\n    let t14;\n    if ($[21] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t14 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-primary\",\n            children: \"إجمالي الموظفين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 11\n        }, this);\n        $[21] = t14;\n    } else {\n        t14 = $[21];\n    }\n    let t15;\n    if ($[22] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t15 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t14,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 196\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 11\n        }, this);\n        $[22] = t15;\n    } else {\n        t15 = $[22];\n    }\n    let t16;\n    if ($[23] !== totalEmployees) {\n        t16 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-primary\",\n            children: totalEmployees\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 191,\n            columnNumber: 11\n        }, this);\n        $[23] = totalEmployees;\n        $[24] = t16;\n    } else {\n        t16 = $[24];\n    }\n    let t17;\n    if ($[25] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t17 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-primary/70 font-medium mt-1\",\n            children: \"+12% من الشهر الماضي\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 199,\n            columnNumber: 11\n        }, this);\n        $[25] = t17;\n    } else {\n        t17 = $[25];\n    }\n    let t18;\n    if ($[26] !== t16) {\n        t18 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-primary\",\n            children: [\n                t15,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t16,\n                        t17\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 206,\n            columnNumber: 11\n        }, this);\n        $[26] = t16;\n        $[27] = t18;\n    } else {\n        t18 = $[27];\n    }\n    let t19;\n    if ($[28] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t19 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-secondary\",\n            children: \"الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 11\n        }, this);\n        $[28] = t19;\n    } else {\n        t19 = $[28];\n    }\n    let t20;\n    if ($[29] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t20 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t19,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-secondary rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 192\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 11\n        }, this);\n        $[29] = t20;\n    } else {\n        t20 = $[29];\n    }\n    let t21;\n    if ($[30] !== totalDepartments) {\n        t21 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-secondary\",\n            children: totalDepartments\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 228,\n            columnNumber: 11\n        }, this);\n        $[30] = totalDepartments;\n        $[31] = t21;\n    } else {\n        t21 = $[31];\n    }\n    let t22;\n    if ($[32] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t22 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-secondary/70 font-medium mt-1\",\n            children: \"الأقسام النشطة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 11\n        }, this);\n        $[32] = t22;\n    } else {\n        t22 = $[32];\n    }\n    let t23;\n    if ($[33] !== t21) {\n        t23 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-secondary\",\n            children: [\n                t20,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t21,\n                        t22\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 168\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 243,\n            columnNumber: 11\n        }, this);\n        $[33] = t21;\n        $[34] = t23;\n    } else {\n        t23 = $[34];\n    }\n    let t24;\n    if ($[35] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t24 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-accent\",\n            children: \"السلة المؤقتة\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 251,\n            columnNumber: 11\n        }, this);\n        $[35] = t24;\n    } else {\n        t24 = $[35];\n    }\n    let t25;\n    if ($[36] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t25 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t24,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-accent rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 189\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 258,\n            columnNumber: 11\n        }, this);\n        $[36] = t25;\n    } else {\n        t25 = $[36];\n    }\n    let t26;\n    if ($[37] !== freeBucketCount) {\n        t26 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-accent\",\n            children: freeBucketCount\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 265,\n            columnNumber: 11\n        }, this);\n        $[37] = freeBucketCount;\n        $[38] = t26;\n    } else {\n        t26 = $[38];\n    }\n    let t27;\n    if ($[39] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t27 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-accent/70 font-medium mt-1\",\n            children: \"موظفون غير مخصصين\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 273,\n            columnNumber: 11\n        }, this);\n        $[39] = t27;\n    } else {\n        t27 = $[39];\n    }\n    let t28;\n    if ($[40] !== t26) {\n        t28 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-accent\",\n            children: [\n                t25,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t26,\n                        t27\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 165\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 11\n        }, this);\n        $[40] = t26;\n        $[41] = t28;\n    } else {\n        t28 = $[41];\n    }\n    let t29;\n    if ($[42] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t29 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n            className: \"text-sm font-semibold text-success\",\n            children: \"متوسط الاستخدام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 288,\n            columnNumber: 11\n        }, this);\n        $[42] = t29;\n    } else {\n        t29 = $[42];\n    }\n    let t30;\n    if ($[43] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t30 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-3\",\n            children: [\n                t29,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-success rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 190\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 98\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 295,\n            columnNumber: 11\n        }, this);\n        $[43] = t30;\n    } else {\n        t30 = $[43];\n    }\n    let t31;\n    if ($[44] !== averageUtilization) {\n        t31 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-3xl font-bold text-success\",\n            children: [\n                averageUtilization,\n                \"%\"\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 302,\n            columnNumber: 11\n        }, this);\n        $[44] = averageUtilization;\n        $[45] = t31;\n    } else {\n        t31 = $[45];\n    }\n    let t32;\n    if ($[46] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t32 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-success/70 font-medium mt-1\",\n            children: \"سعة الأقسام\"\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 310,\n            columnNumber: 11\n        }, this);\n        $[46] = t32;\n    } else {\n        t32 = $[46];\n    }\n    let t33;\n    if ($[47] !== t31) {\n        t33 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 gradient-green-soft border-r-4 border-r-success\",\n            children: [\n                t30,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: [\n                        t31,\n                        t32\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 166\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 317,\n            columnNumber: 11\n        }, this);\n        $[47] = t31;\n        $[48] = t33;\n    } else {\n        t33 = $[48];\n    }\n    let t34;\n    if ($[49] !== t18 || $[50] !== t23 || $[51] !== t28 || $[52] !== t33) {\n        t34 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                t18,\n                t23,\n                t28,\n                t33\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 325,\n            columnNumber: 11\n        }, this);\n        $[49] = t18;\n        $[50] = t23;\n        $[51] = t28;\n        $[52] = t33;\n        $[53] = t34;\n    } else {\n        t34 = $[53];\n    }\n    let t35;\n    if ($[54] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t35 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-2xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 218\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 122\n                    }, this),\n                    \"نظرة عامة على الأقسام\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 336,\n            columnNumber: 11\n        }, this);\n        $[54] = t35;\n    } else {\n        t35 = $[54];\n    }\n    let t36;\n    if ($[55] !== departments) {\n        t36 = departments.slice(0, 6).map(_temp6);\n        $[55] = departments;\n        $[56] = t36;\n    } else {\n        t36 = $[56];\n    }\n    let t37;\n    if ($[57] !== t36) {\n        t37 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced md:grid-cols-2\",\n            children: t36\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 11\n        }, this);\n        $[57] = t36;\n        $[58] = t37;\n    } else {\n        t37 = $[58];\n    }\n    let t38;\n    if ($[59] !== departments.length) {\n        t38 = departments.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                variant: \"outline\",\n                className: \"shadow-soft hover-lift border-2 border-border/50\",\n                children: [\n                    \"عرض جميع الأقسام (\",\n                    departments.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 71\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 37\n        }, this);\n        $[59] = departments.length;\n        $[60] = t38;\n    } else {\n        t38 = $[60];\n    }\n    let t39;\n    if ($[61] !== t37 || $[62] !== t38) {\n        t39 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t35,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            t37,\n                            t38\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 113\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 367,\n                columnNumber: 42\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 367,\n            columnNumber: 11\n        }, this);\n        $[61] = t37;\n        $[62] = t38;\n        $[63] = t39;\n    } else {\n        t39 = $[63];\n    }\n    let t40;\n    if ($[64] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t40 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n            className: \"pb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                className: \"text-xl font-bold text-foreground flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_Building2_Download_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 216\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 121\n                    }, this),\n                    \"النشاط الأخير\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 40\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 376,\n            columnNumber: 11\n        }, this);\n        $[64] = t40;\n    } else {\n        t40 = $[64];\n    }\n    let t41;\n    if ($[65] === Symbol.for(\"react.memo_cache_sentinel\")) {\n        t41 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                children: [\n                    t40,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-spaced\",\n                            children: recentActivity.map(_temp7)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 100\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 87\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 16\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 11\n        }, this);\n        $[65] = t41;\n    } else {\n        t41 = $[65];\n    }\n    let t42;\n    if ($[66] !== t39) {\n        t42 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid-spaced lg:grid-cols-3\",\n            children: [\n                t39,\n                t41\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 390,\n            columnNumber: 11\n        }, this);\n        $[66] = t39;\n        $[67] = t42;\n    } else {\n        t42 = $[67];\n    }\n    let t43;\n    if ($[68] !== t13 || $[69] !== t34 || $[70] !== t42) {\n        t43 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-spaced p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen\",\n            children: [\n                t13,\n                t34,\n                t42\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 398,\n            columnNumber: 11\n        }, this);\n        $[68] = t13;\n        $[69] = t34;\n        $[70] = t42;\n        $[71] = t43;\n    } else {\n        t43 = $[71];\n    }\n    return t43;\n}\n_s(DashboardPage, \"QHGLOyGxaFwZ2BhKJTmW9LdK1Ug=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        _lib_store_hr_store__WEBPACK_IMPORTED_MODULE_7__.useHRStore,\n        _lib_hooks_use_api_data__WEBPACK_IMPORTED_MODULE_8__.useApiData\n    ];\n});\n_c1 = DashboardPage;\nfunction _temp7(activity, index) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start rtl-space-x p-4 rounded-xl gradient-green-soft border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-10 gradient-primary rounded-full flex items-center justify-center shadow-lg flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-white rounded-full\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 308\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 194\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 container-spaced mr-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-semibold text-foreground\",\n                        children: activity.action\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 409\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: activity.details\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 487\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground font-medium\",\n                        children: activity.time\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 558\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 363\n            }, this)\n        ]\n    }, index, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 409,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp6(department) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_department_card__WEBPACK_IMPORTED_MODULE_6__.DepartmentCard, {\n        department: department,\n        onAddEmployee: ()=>{\n            console.log(\"Add employee to\", department.name);\n        },\n        onViewDetails: ()=>{\n            console.log(\"View details for\", department.name);\n        }\n    }, department.id, false, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 412,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp5(_, i) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-muted rounded w-20\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 bg-muted rounded\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 177\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 419,\n                columnNumber: 50\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-16 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 247\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-muted rounded w-24\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 297\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 419,\n                columnNumber: 234\n            }, this)\n        ]\n    }, i, true, {\n        fileName: \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 419,\n        columnNumber: 10\n    }, this);\n}\nfunction _temp4(sum, dept_0) {\n    var _dept_0_employees;\n    const employeeCount = ((_dept_0_employees = dept_0.employees) === null || _dept_0_employees === void 0 ? void 0 : _dept_0_employees.length) || 0;\n    return sum + employeeCount / dept_0.capacity * 100;\n}\nfunction _temp3(row) {\n    return row.join(\",\");\n}\nfunction _temp2(dept) {\n    var _dept_employees, _dept_employees1;\n    return [\n        dept.name,\n        ((_dept_employees = dept.employees) === null || _dept_employees === void 0 ? void 0 : _dept_employees.length) || 0,\n        dept.capacity,\n        \"\".concat(Math.round((((_dept_employees1 = dept.employees) === null || _dept_employees1 === void 0 ? void 0 : _dept_employees1.length) || 0) / dept.capacity * 100), \"%\")\n    ];\n}\nfunction _temp(resolve) {\n    return setTimeout(resolve, 500);\n}\nvar _c1;\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});
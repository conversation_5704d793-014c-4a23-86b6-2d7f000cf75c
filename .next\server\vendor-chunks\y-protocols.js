"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-protocols";
exports.ids = ["vendor-chunks/y-protocols"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-protocols/auth.js":
/*!******************************************!*\
  !*** ./node_modules/y-protocols/auth.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messagePermissionDenied: () => (/* binding */ messagePermissionDenied),\n/* harmony export */   readAuthMessage: () => (/* binding */ readAuthMessage),\n/* harmony export */   writePermissionDenied: () => (/* binding */ writePermissionDenied)\n/* harmony export */ });\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/./node_modules/lib0/decoding.js\");\n\n // eslint-disable-line\n\n\n\nconst messagePermissionDenied = 0\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {string} reason\n */\nconst writePermissionDenied = (encoder, reason) => {\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint(encoder, messagePermissionDenied)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarString(encoder, reason)\n}\n\n/**\n * @callback PermissionDeniedHandler\n * @param {any} y\n * @param {string} reason\n */\n\n/**\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} y\n * @param {PermissionDeniedHandler} permissionDeniedHandler\n */\nconst readAuthMessage = (decoder, y, permissionDeniedHandler) => {\n  switch (lib0_decoding__WEBPACK_IMPORTED_MODULE_1__.readVarUint(decoder)) {\n    case messagePermissionDenied: permissionDeniedHandler(y, lib0_decoding__WEBPACK_IMPORTED_MODULE_1__.readVarString(decoder))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveS1wcm90b2NvbHMvYXV0aC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDd0I7QUFDaUI7QUFDQTs7QUFFbEM7O0FBRVA7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQLEVBQUUsdURBQXFCO0FBQ3ZCLEVBQUUseURBQXVCO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQSxXQUFXLEtBQUs7QUFDaEIsV0FBVyxRQUFRO0FBQ25COztBQUVBO0FBQ0E7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QixXQUFXLE9BQU87QUFDbEIsV0FBVyx5QkFBeUI7QUFDcEM7QUFDTztBQUNQLFVBQVUsc0RBQW9CO0FBQzlCLDZEQUE2RCx3REFBc0I7QUFDbkY7QUFDQSIsInNvdXJjZXMiOlsiRzpcXEF1Z21lbnQgY29kZVxcbm9kZV9tb2R1bGVzXFx5LXByb3RvY29sc1xcYXV0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCAqIGFzIFkgZnJvbSAneWpzJyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG5pbXBvcnQgKiBhcyBlbmNvZGluZyBmcm9tICdsaWIwL2VuY29kaW5nJ1xuaW1wb3J0ICogYXMgZGVjb2RpbmcgZnJvbSAnbGliMC9kZWNvZGluZydcblxuZXhwb3J0IGNvbnN0IG1lc3NhZ2VQZXJtaXNzaW9uRGVuaWVkID0gMFxuXG4vKipcbiAqIEBwYXJhbSB7ZW5jb2RpbmcuRW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtzdHJpbmd9IHJlYXNvblxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVQZXJtaXNzaW9uRGVuaWVkID0gKGVuY29kZXIsIHJlYXNvbikgPT4ge1xuICBlbmNvZGluZy53cml0ZVZhclVpbnQoZW5jb2RlciwgbWVzc2FnZVBlcm1pc3Npb25EZW5pZWQpXG4gIGVuY29kaW5nLndyaXRlVmFyU3RyaW5nKGVuY29kZXIsIHJlYXNvbilcbn1cblxuLyoqXG4gKiBAY2FsbGJhY2sgUGVybWlzc2lvbkRlbmllZEhhbmRsZXJcbiAqIEBwYXJhbSB7YW55fSB5XG4gKiBAcGFyYW0ge3N0cmluZ30gcmVhc29uXG4gKi9cblxuLyoqXG4gKlxuICogQHBhcmFtIHtkZWNvZGluZy5EZWNvZGVyfSBkZWNvZGVyXG4gKiBAcGFyYW0ge1kuRG9jfSB5XG4gKiBAcGFyYW0ge1Blcm1pc3Npb25EZW5pZWRIYW5kbGVyfSBwZXJtaXNzaW9uRGVuaWVkSGFuZGxlclxuICovXG5leHBvcnQgY29uc3QgcmVhZEF1dGhNZXNzYWdlID0gKGRlY29kZXIsIHksIHBlcm1pc3Npb25EZW5pZWRIYW5kbGVyKSA9PiB7XG4gIHN3aXRjaCAoZGVjb2RpbmcucmVhZFZhclVpbnQoZGVjb2RlcikpIHtcbiAgICBjYXNlIG1lc3NhZ2VQZXJtaXNzaW9uRGVuaWVkOiBwZXJtaXNzaW9uRGVuaWVkSGFuZGxlcih5LCBkZWNvZGluZy5yZWFkVmFyU3RyaW5nKGRlY29kZXIpKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-protocols/auth.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-protocols/awareness.js":
/*!***********************************************!*\
  !*** ./node_modules/y-protocols/awareness.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Awareness: () => (/* binding */ Awareness),\n/* harmony export */   applyAwarenessUpdate: () => (/* binding */ applyAwarenessUpdate),\n/* harmony export */   encodeAwarenessUpdate: () => (/* binding */ encodeAwarenessUpdate),\n/* harmony export */   modifyAwarenessUpdate: () => (/* binding */ modifyAwarenessUpdate),\n/* harmony export */   outdatedTimeout: () => (/* binding */ outdatedTimeout),\n/* harmony export */   removeAwarenessStates: () => (/* binding */ removeAwarenessStates)\n/* harmony export */ });\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/./node_modules/lib0/decoding.js\");\n/* harmony import */ var lib0_time__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/time */ \"(ssr)/./node_modules/lib0/time.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/math */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n/* harmony import */ var lib0_function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/function */ \"(ssr)/./node_modules/lib0/function.js\");\n/**\n * @module awareness-protocol\n */\n\n\n\n\n\n\n\n // eslint-disable-line\n\nconst outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nclass Awareness extends lib0_observable__WEBPACK_IMPORTED_MODULE_0__.Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, lib0_math__WEBPACK_IMPORTED_MODULE_2__.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nconst removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nconst encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(state))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nconst modifyAwarenessUpdate = (update, modify) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const modifiedState = modify(state)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nconst applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const timestamp = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    let clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-protocols/awareness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-protocols/sync.js":
/*!******************************************!*\
  !*** ./node_modules/y-protocols/sync.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageYjsSyncStep1: () => (/* binding */ messageYjsSyncStep1),\n/* harmony export */   messageYjsSyncStep2: () => (/* binding */ messageYjsSyncStep2),\n/* harmony export */   messageYjsUpdate: () => (/* binding */ messageYjsUpdate),\n/* harmony export */   readSyncMessage: () => (/* binding */ readSyncMessage),\n/* harmony export */   readSyncStep1: () => (/* binding */ readSyncStep1),\n/* harmony export */   readSyncStep2: () => (/* binding */ readSyncStep2),\n/* harmony export */   readUpdate: () => (/* binding */ readUpdate),\n/* harmony export */   writeSyncStep1: () => (/* binding */ writeSyncStep1),\n/* harmony export */   writeSyncStep2: () => (/* binding */ writeSyncStep2),\n/* harmony export */   writeUpdate: () => (/* binding */ writeUpdate)\n/* harmony export */ });\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/./node_modules/lib0/decoding.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/**\n * @module sync-protocol\n */\n\n\n\n\n\n/**\n * @typedef {Map<number, number>} StateMap\n */\n\n/**\n * Core Yjs defines two message types:\n * • YjsSyncStep1: Includes the State Set of the sending client. When received, the client should reply with YjsSyncStep2.\n * • YjsSyncStep2: Includes all missing structs and the complete delete set. When received, the client is assured that it\n *   received all information from the remote client.\n *\n * In a peer-to-peer network, you may want to introduce a SyncDone message type. Both parties should initiate the connection\n * with SyncStep1. When a client received SyncStep2, it should reply with SyncDone. When the local client received both\n * SyncStep2 and SyncDone, it is assured that it is synced to the remote client.\n *\n * In a client-server model, you want to handle this differently: The client should initiate the connection with SyncStep1.\n * When the server receives SyncStep1, it should reply with SyncStep2 immediately followed by SyncStep1. The client replies\n * with SyncStep2 when it receives SyncStep1. Optionally the server may send a SyncDone after it received SyncStep2, so the\n * client knows that the sync is finished.  There are two reasons for this more elaborated sync model: 1. This protocol can\n * easily be implemented on top of http and websockets. 2. The server should only reply to requests, and not initiate them.\n * Therefore it is necessary that the client initiates the sync.\n *\n * Construction of a message:\n * [messageType : varUint, message definition..]\n *\n * Note: A message does not include information about the room name. This must to be handled by the upper layer protocol!\n *\n * stringify[messageType] stringifies a message definition (messageType is already read from the bufffer)\n */\n\nconst messageYjsSyncStep1 = 0\nconst messageYjsSyncStep2 = 1\nconst messageYjsUpdate = 2\n\n/**\n * Create a sync step 1 message based on the state of the current shared document.\n *\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n */\nconst writeSyncStep1 = (encoder, doc) => {\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint(encoder, messageYjsSyncStep1)\n  const sv = yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateVector(doc)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint8Array(encoder, sv)\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n * @param {Uint8Array} [encodedStateVector]\n */\nconst writeSyncStep2 = (encoder, doc, encodedStateVector) => {\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint(encoder, messageYjsSyncStep2)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint8Array(encoder, yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateAsUpdate(doc, encodedStateVector))\n}\n\n/**\n * Read SyncStep1 message and reply with SyncStep2.\n *\n * @param {decoding.Decoder} decoder The reply to the received message\n * @param {encoding.Encoder} encoder The received message\n * @param {Y.Doc} doc\n */\nconst readSyncStep1 = (decoder, encoder, doc) =>\n  writeSyncStep2(encoder, doc, lib0_decoding__WEBPACK_IMPORTED_MODULE_2__.readVarUint8Array(decoder))\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nconst readSyncStep2 = (decoder, doc, transactionOrigin) => {\n  try {\n    yjs__WEBPACK_IMPORTED_MODULE_1__.applyUpdate(doc, lib0_decoding__WEBPACK_IMPORTED_MODULE_2__.readVarUint8Array(decoder), transactionOrigin)\n  } catch (error) {\n    // This catches errors that are thrown by event handlers\n    console.error('Caught error while handling a Yjs update', error)\n  }\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Uint8Array} update\n */\nconst writeUpdate = (encoder, update) => {\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint(encoder, messageYjsUpdate)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_0__.writeVarUint8Array(encoder, update)\n}\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nconst readUpdate = readSyncStep2\n\n/**\n * @param {decoding.Decoder} decoder A message received from another client\n * @param {encoding.Encoder} encoder The reply message. Does not need to be sent if empty.\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nconst readSyncMessage = (decoder, encoder, doc, transactionOrigin) => {\n  const messageType = lib0_decoding__WEBPACK_IMPORTED_MODULE_2__.readVarUint(decoder)\n  switch (messageType) {\n    case messageYjsSyncStep1:\n      readSyncStep1(decoder, encoder, doc)\n      break\n    case messageYjsSyncStep2:\n      readSyncStep2(decoder, doc, transactionOrigin)\n      break\n    case messageYjsUpdate:\n      readUpdate(decoder, doc, transactionOrigin)\n      break\n    default:\n      throw new Error('Unknown message type')\n  }\n  return messageType\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-protocols/sync.js\n");

/***/ })

};
;
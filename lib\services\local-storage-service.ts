import localforage from 'localforage'
import { Employee, Department, User } from '@/lib/types'
import { toast } from 'sonner'

export interface StorageMetadata {
  version: string
  lastSync: Date
  userId: string
  deviceId: string
  isOnline: boolean
}

export interface SyncConflict {
  type: 'employee' | 'department'
  id: string
  localData: any
  remoteData: any
  timestamp: Date
}

export interface SyncResult {
  success: boolean
  conflicts: SyncConflict[]
  synced: {
    employees: number
    departments: number
  }
  errors: string[]
}

export class LocalStorageService {
  private static instance: LocalStorageService
  private employeeStore: LocalForage
  private departmentStore: LocalForage
  private metadataStore: LocalForage
  private conflictStore: LocalForage
  private deviceId: string

  private constructor() {
    // Initialize localforage stores
    this.employeeStore = localforage.createInstance({
      name: 'HR_Synergy',
      storeName: 'employees',
      description: 'Employee data storage'
    })

    this.departmentStore = localforage.createInstance({
      name: 'HR_Synergy',
      storeName: 'departments',
      description: 'Department data storage'
    })

    this.metadataStore = localforage.createInstance({
      name: 'HR_Synergy',
      storeName: 'metadata',
      description: 'Sync metadata storage'
    })

    this.conflictStore = localforage.createInstance({
      name: 'HR_Synergy',
      storeName: 'conflicts',
      description: 'Sync conflict storage'
    })

    // Generate or retrieve device ID
    this.deviceId = this.generateDeviceId()
  }

  static getInstance(): LocalStorageService {
    if (!LocalStorageService.instance) {
      LocalStorageService.instance = new LocalStorageService()
    }
    return LocalStorageService.instance
  }

  /**
   * Initialize storage with user data
   */
  async initialize(user: User): Promise<void> {
    try {
      const metadata: StorageMetadata = {
        version: '1.0.0',
        lastSync: new Date(),
        userId: user.id,
        deviceId: this.deviceId,
        isOnline: navigator.onLine
      }

      await this.metadataStore.setItem('metadata', metadata)
      
      // Set up online/offline event listeners
      this.setupNetworkListeners()
      
      console.log('Local storage initialized for user:', user.id)
    } catch (error) {
      console.error('Failed to initialize local storage:', error)
      throw new Error('فشل في تهيئة التخزين المحلي')
    }
  }

  /**
   * Save employees to local storage
   */
  async saveEmployees(employees: Employee[]): Promise<void> {
    try {
      // Clear existing employees
      await this.employeeStore.clear()
      
      // Save each employee with timestamp
      const savePromises = employees.map(employee => 
        this.employeeStore.setItem(employee.id, {
          ...employee,
          _localTimestamp: new Date(),
          _deviceId: this.deviceId
        })
      )
      
      await Promise.all(savePromises)
      await this.updateLastSync()
      
      console.log(`Saved ${employees.length} employees to local storage`)
    } catch (error) {
      console.error('Failed to save employees:', error)
      throw new Error('فشل في حفظ بيانات الموظفين')
    }
  }

  /**
   * Load employees from local storage
   */
  async loadEmployees(): Promise<Employee[]> {
    try {
      const employees: Employee[] = []
      
      await this.employeeStore.iterate((value: any, key: string) => {
        if (value && typeof value === 'object') {
          // Remove local metadata before returning
          const { _localTimestamp, _deviceId, ...employee } = value
          employees.push(employee as Employee)
        }
      })
      
      console.log(`Loaded ${employees.length} employees from local storage`)
      return employees
    } catch (error) {
      console.error('Failed to load employees:', error)
      return []
    }
  }

  /**
   * Save departments to local storage
   */
  async saveDepartments(departments: Department[]): Promise<void> {
    try {
      // Clear existing departments
      await this.departmentStore.clear()
      
      // Save each department with timestamp
      const savePromises = departments.map(department => 
        this.departmentStore.setItem(department.id, {
          ...department,
          _localTimestamp: new Date(),
          _deviceId: this.deviceId
        })
      )
      
      await Promise.all(savePromises)
      await this.updateLastSync()
      
      console.log(`Saved ${departments.length} departments to local storage`)
    } catch (error) {
      console.error('Failed to save departments:', error)
      throw new Error('فشل في حفظ بيانات الأقسام')
    }
  }

  /**
   * Load departments from local storage
   */
  async loadDepartments(): Promise<Department[]> {
    try {
      const departments: Department[] = []
      
      await this.departmentStore.iterate((value: any, key: string) => {
        if (value && typeof value === 'object') {
          // Remove local metadata before returning
          const { _localTimestamp, _deviceId, ...department } = value
          departments.push(department as Department)
        }
      })
      
      console.log(`Loaded ${departments.length} departments from local storage`)
      return departments
    } catch (error) {
      console.error('Failed to load departments:', error)
      return []
    }
  }

  /**
   * Save single employee (for real-time updates)
   */
  async saveEmployee(employee: Employee): Promise<void> {
    try {
      await this.employeeStore.setItem(employee.id, {
        ...employee,
        _localTimestamp: new Date(),
        _deviceId: this.deviceId
      })
      
      await this.updateLastSync()
    } catch (error) {
      console.error('Failed to save employee:', error)
      throw new Error('فشل في حفظ بيانات الموظف')
    }
  }

  /**
   * Save single department (for real-time updates)
   */
  async saveDepartment(department: Department): Promise<void> {
    try {
      await this.departmentStore.setItem(department.id, {
        ...department,
        _localTimestamp: new Date(),
        _deviceId: this.deviceId
      })
      
      await this.updateLastSync()
    } catch (error) {
      console.error('Failed to save department:', error)
      throw new Error('فشل في حفظ بيانات القسم')
    }
  }

  /**
   * Remove employee from local storage
   */
  async removeEmployee(employeeId: string): Promise<void> {
    try {
      await this.employeeStore.removeItem(employeeId)
      await this.updateLastSync()
    } catch (error) {
      console.error('Failed to remove employee:', error)
      throw new Error('فشل في حذف بيانات الموظف')
    }
  }

  /**
   * Remove department from local storage
   */
  async removeDepartment(departmentId: string): Promise<void> {
    try {
      await this.departmentStore.removeItem(departmentId)
      await this.updateLastSync()
    } catch (error) {
      console.error('Failed to remove department:', error)
      throw new Error('فشل في حذف بيانات القسم')
    }
  }

  /**
   * Get storage metadata
   */
  async getMetadata(): Promise<StorageMetadata | null> {
    try {
      return await this.metadataStore.getItem('metadata')
    } catch (error) {
      console.error('Failed to get metadata:', error)
      return null
    }
  }

  /**
   * Check if data exists in local storage
   */
  async hasData(): Promise<boolean> {
    try {
      const employeeCount = await this.employeeStore.length()
      const departmentCount = await this.departmentStore.length()
      return employeeCount > 0 || departmentCount > 0
    } catch (error) {
      console.error('Failed to check data existence:', error)
      return false
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    employees: number
    departments: number
    lastSync: Date | null
    storageSize: number
  }> {
    try {
      const employeeCount = await this.employeeStore.length()
      const departmentCount = await this.departmentStore.length()
      const metadata = await this.getMetadata()
      
      // Estimate storage size (rough calculation)
      let storageSize = 0
      await this.employeeStore.iterate(() => storageSize += 1)
      await this.departmentStore.iterate(() => storageSize += 1)
      
      return {
        employees: employeeCount,
        departments: departmentCount,
        lastSync: metadata?.lastSync || null,
        storageSize: storageSize * 1024 // Rough estimate in bytes
      }
    } catch (error) {
      console.error('Failed to get storage stats:', error)
      return {
        employees: 0,
        departments: 0,
        lastSync: null,
        storageSize: 0
      }
    }
  }

  /**
   * Clear all local storage
   */
  async clearAll(): Promise<void> {
    try {
      await Promise.all([
        this.employeeStore.clear(),
        this.departmentStore.clear(),
        this.metadataStore.clear(),
        this.conflictStore.clear()
      ])
      
      console.log('All local storage cleared')
      toast.success('تم مسح جميع البيانات المحلية')
    } catch (error) {
      console.error('Failed to clear storage:', error)
      throw new Error('فشل في مسح البيانات المحلية')
    }
  }

  /**
   * Export data for backup
   */
  async exportData(): Promise<{
    employees: Employee[]
    departments: Department[]
    metadata: StorageMetadata | null
  }> {
    try {
      const [employees, departments, metadata] = await Promise.all([
        this.loadEmployees(),
        this.loadDepartments(),
        this.getMetadata()
      ])
      
      return { employees, departments, metadata }
    } catch (error) {
      console.error('Failed to export data:', error)
      throw new Error('فشل في تصدير البيانات')
    }
  }

  /**
   * Import data from backup
   */
  async importData(data: {
    employees: Employee[]
    departments: Department[]
  }): Promise<void> {
    try {
      await Promise.all([
        this.saveEmployees(data.employees),
        this.saveDepartments(data.departments)
      ])
      
      toast.success('تم استيراد البيانات بنجاح')
    } catch (error) {
      console.error('Failed to import data:', error)
      throw new Error('فشل في استيراد البيانات')
    }
  }

  /**
   * Update last sync timestamp
   */
  private async updateLastSync(): Promise<void> {
    try {
      const metadata = await this.getMetadata()
      if (metadata) {
        metadata.lastSync = new Date()
        metadata.isOnline = navigator.onLine
        await this.metadataStore.setItem('metadata', metadata)
      }
    } catch (error) {
      console.error('Failed to update last sync:', error)
    }
  }

  /**
   * Generate unique device ID
   */
  private generateDeviceId(): string {
    const stored = localStorage.getItem('hr_synergy_device_id')
    if (stored) return stored
    
    const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem('hr_synergy_device_id', deviceId)
    return deviceId
  }

  /**
   * Setup network status listeners
   */
  private setupNetworkListeners(): void {
    const updateOnlineStatus = async () => {
      const metadata = await this.getMetadata()
      if (metadata) {
        metadata.isOnline = navigator.onLine
        await this.metadataStore.setItem('metadata', metadata)
      }
      
      if (navigator.onLine) {
        toast.success('تم الاتصال بالإنترنت - سيتم مزامنة البيانات')
      } else {
        toast.warning('انقطع الاتصال بالإنترنت - سيتم العمل في وضع عدم الاتصال')
      }
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
  }

  /**
   * Check if device is online
   */
  isOnline(): boolean {
    return navigator.onLine
  }

  /**
   * Get device ID
   */
  getDeviceId(): string {
    return this.deviceId
  }
  /**
   * Save sync conflicts for later resolution
   */
  async saveConflicts(conflicts: SyncConflict[]): Promise<void> {
    try {
      const timestamp = Date.now()
      await this.conflictStore.setItem(`conflicts_${timestamp}`, conflicts)
    } catch (error) {
      console.error('Failed to save conflicts:', error)
    }
  }

  /**
   * Load all sync conflicts
   */
  async loadConflicts(): Promise<SyncConflict[]> {
    try {
      const allConflicts: SyncConflict[] = []

      await this.conflictStore.iterate((value: any) => {
        if (Array.isArray(value)) {
          allConflicts.push(...value)
        }
      })

      return allConflicts
    } catch (error) {
      console.error('Failed to load conflicts:', error)
      return []
    }
  }

  /**
   * Clear resolved conflicts
   */
  async clearConflicts(): Promise<void> {
    try {
      await this.conflictStore.clear()
    } catch (error) {
      console.error('Failed to clear conflicts:', error)
    }
  }
}

// Export singleton instance
export const localStorageService = LocalStorageService.getInstance()

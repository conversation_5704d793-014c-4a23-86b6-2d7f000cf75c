"use client"

import React, { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { useDropzone } from 'react-dropzone'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useHRStore } from '@/lib/store/hr-store'
import { csvImportService, CSVImportProgress, CSVValidationError, CSVImportOptions } from '@/lib/services/csv-import-service'
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertTriangle,
  X,
  Eye,
  Settings,
  Users,
  Building2
} from 'lucide-react'
import { toast } from 'sonner'

interface CSVImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CSVImportDialog({ open, onOpenChange }: CSVImportDialogProps) {
  const { data: session } = useSession()
  const { departments, employees, setEmployees } = useHRStore()
  
  const [step, setStep] = useState<'upload' | 'configure' | 'validate' | 'import' | 'results'>('upload')
  const [file, setFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<any[]>([])
  const [validationErrors, setValidationErrors] = useState<CSVValidationError[]>([])
  const [importProgress, setImportProgress] = useState<CSVImportProgress | null>(null)
  const [importResults, setImportResults] = useState<any>(null)
  
  const [options, setOptions] = useState<CSVImportOptions>({
    skipFirstRow: true,
    departmentMapping: 'name',
    createMissingDepartments: false,
    defaultDepartmentId: '',
    onProgress: setImportProgress
  })

  const templates = csvImportService.getTemplates()

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error('يرجى اختيار ملف CSV صالح')
      return
    }

    setFile(file)
    setStep('configure')
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  const handleDownloadTemplate = (templateIndex: number) => {
    const template = templates[templateIndex]
    csvImportService.downloadTemplate(template)
    toast.success(`تم تحميل قالب: ${template.name}`)
  }

  const handleParseCSV = async () => {
    if (!file) return

    try {
      setStep('validate')
      const parseResult = await csvImportService.parseCSV(file, options)
      
      if (parseResult.errors.length > 0) {
        setValidationErrors(parseResult.errors)
        toast.error('تم العثور على أخطاء في ملف CSV')
        return
      }

      setCsvData(parseResult.data)
      
      // Validate data
      const validationErrors = await csvImportService.validateCSVData(
        parseResult.data,
        departments,
        employees,
        options
      )
      
      setValidationErrors(validationErrors)
      
      if (validationErrors.length === 0) {
        toast.success('تم التحقق من البيانات بنجاح')
      } else {
        toast.warning(`تم العثور على ${validationErrors.length} خطأ في التحقق`)
      }
    } catch (error) {
      toast.error('خطأ في تحليل ملف CSV')
      console.error(error)
    }
  }

  const handleImport = async () => {
    if (!session?.user?.id || csvData.length === 0) return

    try {
      setStep('import')
      
      const result = await csvImportService.importCSVData(
        csvData,
        departments,
        employees,
        options,
        session.user.id
      )
      
      setImportResults(result)
      setStep('results')
      
      // Update employees in store
      if (result.success.length > 0) {
        setEmployees([...employees, ...result.success])
        toast.success(`تم استيراد ${result.success.length} موظف بنجاح`)
      }
      
      if (result.errors.length > 0) {
        toast.error(`فشل في استيراد ${result.errors.length} موظف`)
      }
    } catch (error) {
      toast.error('خطأ في عملية الاستيراد')
      console.error(error)
    }
  }

  const handleClose = () => {
    setStep('upload')
    setFile(null)
    setCsvData([])
    setValidationErrors([])
    setImportProgress(null)
    setImportResults(null)
    onOpenChange(false)
  }

  const renderUploadStep = () => (
    <div className="container-spaced">
      {/* Templates */}
      <Card className="gradient-green-soft border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Download className="h-5 w-5 text-primary" />
            قوالب CSV
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          {templates.map((template, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white border border-border/50">
              <div>
                <h4 className="font-medium">{template.name}</h4>
                <p className="text-sm text-muted-foreground">{template.description}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadTemplate(index)}
              >
                <Download className="h-4 w-4 ml-1" />
                تحميل
              </Button>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="pt-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-border hover:border-primary/50 hover:bg-muted/50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {isDragActive ? 'اسقط الملف هنا' : 'اختر ملف CSV أو اسقطه هنا'}
            </h3>
            <p className="text-muted-foreground">
              الحد الأقصى لحجم الملف: 10 ميجابايت
            </p>
          </div>
          
          {file && (
            <div className="mt-4 p-3 rounded-lg gradient-green-soft border border-primary/20">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(file.size / 1024).toFixed(1)} كيلوبايت
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )

  const renderConfigureStep = () => (
    <div className="container-spaced">
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Settings className="h-5 w-5" />
            إعدادات الاستيراد
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          <div className="grid-spaced md:grid-cols-2">
            <div className="container-spaced">
              <Label>طريقة ربط الأقسام</Label>
              <Select
                value={options.departmentMapping}
                onValueChange={(value: 'name' | 'code' | 'id') =>
                  setOptions({ ...options, departmentMapping: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">بالاسم</SelectItem>
                  <SelectItem value="code">بالرمز</SelectItem>
                  <SelectItem value="id">بالمعرف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="container-spaced">
              <Label>القسم الافتراضي</Label>
              <Select
                value={options.defaultDepartmentId || ''}
                onValueChange={(value) =>
                  setOptions({ ...options, defaultDepartmentId: value || undefined })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر القسم الافتراضي" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">السلة المؤقتة</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name} ({dept.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="create-missing">إنشاء الأقسام المفقودة</Label>
            <Switch
              id="create-missing"
              checked={options.createMissingDepartments}
              onCheckedChange={(checked) =>
                setOptions({ ...options, createMissingDepartments: checked })
              }
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={() => setStep('upload')} variant="outline">
          السابق
        </Button>
        <Button onClick={handleParseCSV} className="gradient-primary">
          التحقق من البيانات
        </Button>
      </div>
    </div>
  )

  const renderValidateStep = () => (
    <div className="container-spaced">
      {/* Validation Summary */}
      <div className="grid-spaced md:grid-cols-3">
        <Card className="gradient-green-soft border-primary/20">
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm font-medium text-primary">إجمالي الصفوف</p>
                <p className="text-lg font-bold text-primary">{csvData.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className={validationErrors.length === 0 ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              {validationErrors.length === 0 ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-600" />
              )}
              <div>
                <p className="text-sm font-medium">الأخطاء</p>
                <p className="text-lg font-bold">{validationErrors.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">صالح للاستيراد</p>
                <p className="text-lg font-bold text-blue-900">
                  {csvData.length - validationErrors.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              أخطاء التحقق ({validationErrors.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-h-64 overflow-y-auto container-spaced">
              {validationErrors.slice(0, 10).map((error, index) => (
                <div key={index} className="p-3 rounded-lg bg-red-50 border border-red-200">
                  <div className="flex items-start gap-3">
                    <Badge variant="destructive">صف {error.row}</Badge>
                    <div className="flex-1">
                      <p className="font-medium text-red-900">{error.field}</p>
                      <p className="text-sm text-red-700">{error.error}</p>
                      <p className="text-xs text-red-600">القيمة: {error.value}</p>
                    </div>
                  </div>
                </div>
              ))}
              {validationErrors.length > 10 && (
                <p className="text-center text-muted-foreground">
                  و {validationErrors.length - 10} خطأ آخر...
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex gap-3">
        <Button onClick={() => setStep('configure')} variant="outline">
          السابق
        </Button>
        <Button 
          onClick={handleImport} 
          disabled={validationErrors.length > 0}
          className="gradient-primary"
        >
          بدء الاستيراد
        </Button>
      </div>
    </div>
  )

  const renderImportStep = () => (
    <div className="container-spaced">
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Upload className="h-5 w-5" />
            جاري الاستيراد...
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          {importProgress && (
            <>
              <div className="container-spaced">
                <div className="flex justify-between text-sm">
                  <span>التقدم</span>
                  <span>{importProgress.percentage}%</span>
                </div>
                <Progress value={importProgress.percentage} className="h-2" />
              </div>

              <div className="grid-spaced md:grid-cols-3">
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">{importProgress.processed}</p>
                  <p className="text-sm text-muted-foreground">تم معالجته</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{importProgress.successful}</p>
                  <p className="text-sm text-muted-foreground">نجح</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{importProgress.failed}</p>
                  <p className="text-sm text-muted-foreground">فشل</p>
                </div>
              </div>

              {importProgress.currentRow && (
                <p className="text-center text-muted-foreground">
                  معالجة الصف {importProgress.currentRow} من {importProgress.total}
                </p>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )

  const renderResultsStep = () => (
    <div className="container-spaced">
      {importResults && (
        <>
          {/* Results Summary */}
          <div className="grid-spaced md:grid-cols-2">
            <Card className="bg-green-50 border-green-200">
              <CardContent className="pt-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-green-900">تم الاستيراد بنجاح</p>
                    <p className="text-2xl font-bold text-green-900">{importResults.success.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-red-50 border-red-200">
              <CardContent className="pt-4">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                  <div>
                    <p className="text-sm font-medium text-red-900">فشل في الاستيراد</p>
                    <p className="text-2xl font-bold text-red-900">{importResults.errors.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Import Errors */}
          {importResults.errors.length > 0 && (
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-red-700">
                  <AlertTriangle className="h-5 w-5" />
                  أخطاء الاستيراد ({importResults.errors.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-64 overflow-y-auto container-spaced">
                  {importResults.errors.slice(0, 10).map((error: any, index: number) => (
                    <div key={index} className="p-3 rounded-lg bg-red-50 border border-red-200">
                      <div className="flex items-start gap-3">
                        <Badge variant="destructive">صف {error.row}</Badge>
                        <div className="flex-1">
                          <p className="text-sm text-red-700">{error.error}</p>
                          <p className="text-xs text-red-600">
                            البيانات: {JSON.stringify(error.data)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  {importResults.errors.length > 10 && (
                    <p className="text-center text-muted-foreground">
                      و {importResults.errors.length - 10} خطأ آخر...
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      <div className="flex gap-3">
        <Button onClick={handleClose} className="gradient-primary">
          إنهاء
        </Button>
      </div>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Upload className="h-6 w-6" />
            استيراد الموظفين من CSV
          </DialogTitle>
          <DialogDescription>
            استيراد بيانات الموظفين بشكل مجمع من ملف CSV
          </DialogDescription>
        </DialogHeader>

        {step === 'upload' && renderUploadStep()}
        {step === 'configure' && renderConfigureStep()}
        {step === 'validate' && renderValidateStep()}
        {step === 'import' && renderImportStep()}
        {step === 'results' && renderResultsStep()}
      </DialogContent>
    </Dialog>
  )
}

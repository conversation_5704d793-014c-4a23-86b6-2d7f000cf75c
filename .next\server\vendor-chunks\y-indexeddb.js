"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-indexeddb";
exports.ids = ["vendor-chunks/y-indexeddb"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-indexeddb/src/y-indexeddb.js":
/*!*****************************************************!*\
  !*** ./node_modules/y-indexeddb/src/y-indexeddb.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IndexeddbPersistence: () => (/* binding */ IndexeddbPersistence),\n/* harmony export */   PREFERRED_TRIM_SIZE: () => (/* binding */ PREFERRED_TRIM_SIZE),\n/* harmony export */   clearDocument: () => (/* binding */ clearDocument),\n/* harmony export */   fetchUpdates: () => (/* binding */ fetchUpdates),\n/* harmony export */   storeState: () => (/* binding */ storeState)\n/* harmony export */ });\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/indexeddb */ \"(ssr)/./node_modules/lib0/indexeddb.js\");\n/* harmony import */ var lib0_promise__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/promise */ \"(ssr)/./node_modules/lib0/promise.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n\n\n\n\n\nconst customStoreName = 'custom'\nconst updatesStoreName = 'updates'\n\nconst PREFERRED_TRIM_SIZE = 500\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {function(IDBObjectStore):void} [beforeApplyUpdatesCallback]\n * @param {function(IDBObjectStore):void} [afterApplyUpdatesCallback]\n */\nconst fetchUpdates = (idbPersistence, beforeApplyUpdatesCallback = () => {}, afterApplyUpdatesCallback = () => {}) => {\n  const [updatesStore] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(/** @type {IDBDatabase} */ (idbPersistence.db), [updatesStoreName]) // , 'readonly')\n  return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.getAll(updatesStore, lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createIDBKeyRangeLowerBound(idbPersistence._dbref, false)).then(updates => {\n    if (!idbPersistence._destroyed) {\n      beforeApplyUpdatesCallback(updatesStore)\n      yjs__WEBPACK_IMPORTED_MODULE_1__.transact(idbPersistence.doc, () => {\n        updates.forEach(val => yjs__WEBPACK_IMPORTED_MODULE_1__.applyUpdate(idbPersistence.doc, val))\n      }, idbPersistence, false)\n      afterApplyUpdatesCallback(updatesStore)\n    }\n  })\n    .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.getLastKey(updatesStore).then(lastKey => { idbPersistence._dbref = lastKey + 1 }))\n    .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n    .then(() => updatesStore)\n}\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {boolean} forceStore\n */\nconst storeState = (idbPersistence, forceStore = true) =>\n  fetchUpdates(idbPersistence)\n    .then(updatesStore => {\n      if (forceStore || idbPersistence._dbsize >= PREFERRED_TRIM_SIZE) {\n        lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateAsUpdate(idbPersistence.doc))\n          .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.del(updatesStore, lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createIDBKeyRangeUpperBound(idbPersistence._dbref, true)))\n          .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n      }\n    })\n\n/**\n * @param {string} name\n */\nconst clearDocument = name => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.deleteDB(name)\n\n/**\n * @extends Observable<string>\n */\nclass IndexeddbPersistence extends lib0_observable__WEBPACK_IMPORTED_MODULE_2__.Observable {\n  /**\n   * @param {string} name\n   * @param {Y.Doc} doc\n   */\n  constructor (name, doc) {\n    super()\n    this.doc = doc\n    this.name = name\n    this._dbref = 0\n    this._dbsize = 0\n    this._destroyed = false\n    /**\n     * @type {IDBDatabase|null}\n     */\n    this.db = null\n    this.synced = false\n    this._db = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.openDB(name, db =>\n      lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createStores(db, [\n        ['updates', { autoIncrement: true }],\n        ['custom']\n      ])\n    )\n    /**\n     * @type {Promise<IndexeddbPersistence>}\n     */\n    this.whenSynced = lib0_promise__WEBPACK_IMPORTED_MODULE_3__.create(resolve => this.on('synced', () => resolve(this)))\n\n    this._db.then(db => {\n      this.db = db\n      /**\n       * @param {IDBObjectStore} updatesStore\n       */\n      const beforeApplyUpdatesCallback = (updatesStore) => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateAsUpdate(doc))\n      const afterApplyUpdatesCallback = () => {\n        if (this._destroyed) return this\n        this.synced = true\n        this.emit('synced', [this])\n      }\n      fetchUpdates(this, beforeApplyUpdatesCallback, afterApplyUpdatesCallback)\n    })\n    /**\n     * Timeout in ms untill data is merged and persisted in idb.\n     */\n    this._storeTimeout = 1000\n    /**\n     * @type {any}\n     */\n    this._storeTimeoutId = null\n    /**\n     * @param {Uint8Array} update\n     * @param {any} origin\n     */\n    this._storeUpdate = (update, origin) => {\n      if (this.db && origin !== this) {\n        const [updatesStore] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(/** @type {IDBDatabase} */ (this.db), [updatesStoreName])\n        lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, update)\n        if (++this._dbsize >= PREFERRED_TRIM_SIZE) {\n          // debounce store call\n          if (this._storeTimeoutId !== null) {\n            clearTimeout(this._storeTimeoutId)\n          }\n          this._storeTimeoutId = setTimeout(() => {\n            storeState(this, false)\n            this._storeTimeoutId = null\n          }, this._storeTimeout)\n        }\n      }\n    }\n    doc.on('update', this._storeUpdate)\n    this.destroy = this.destroy.bind(this)\n    doc.on('destroy', this.destroy)\n  }\n\n  destroy () {\n    if (this._storeTimeoutId) {\n      clearTimeout(this._storeTimeoutId)\n    }\n    this.doc.off('update', this._storeUpdate)\n    this.doc.off('destroy', this.destroy)\n    this._destroyed = true\n    return this._db.then(db => {\n      db.close()\n    })\n  }\n\n  /**\n   * Destroys this instance and removes all data from indexeddb.\n   *\n   * @return {Promise<void>}\n   */\n  clearData () {\n    return this.destroy().then(() => {\n      lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.deleteDB(this.name)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<String | number | ArrayBuffer | Date | any>}\n   */\n  get (key) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName], 'readonly')\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.get(custom, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @param {String | number | ArrayBuffer | Date} value\n   * @return {Promise<String | number | ArrayBuffer | Date>}\n   */\n  set (key, value) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName])\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.put(custom, value, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<undefined>}\n   */\n  del (key) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName])\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.del(custom, key)\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-indexeddb/src/y-indexeddb.js\n");

/***/ })

};
;
{"version": 3, "file": "y-indexeddb.cjs", "sources": ["../src/y-indexeddb.js"], "sourcesContent": ["import * as Y from 'yjs'\nimport * as idb from 'lib0/indexeddb'\nimport * as promise from 'lib0/promise'\nimport { Observable } from 'lib0/observable'\n\nconst customStoreName = 'custom'\nconst updatesStoreName = 'updates'\n\nexport const PREFERRED_TRIM_SIZE = 500\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {function(IDBObjectStore):void} [beforeApplyUpdatesCallback]\n * @param {function(IDBObjectStore):void} [afterApplyUpdatesCallback]\n */\nexport const fetchUpdates = (idbPersistence, beforeApplyUpdatesCallback = () => {}, afterApplyUpdatesCallback = () => {}) => {\n  const [updatesStore] = idb.transact(/** @type {IDBDatabase} */ (idbPersistence.db), [updatesStoreName]) // , 'readonly')\n  return idb.getAll(updatesStore, idb.createIDBKeyRangeLowerBound(idbPersistence._dbref, false)).then(updates => {\n    if (!idbPersistence._destroyed) {\n      beforeApplyUpdatesCallback(updatesStore)\n      Y.transact(idbPersistence.doc, () => {\n        updates.forEach(val => Y.applyUpdate(idbPersistence.doc, val))\n      }, idbPersistence, false)\n      afterApplyUpdatesCallback(updatesStore)\n    }\n  })\n    .then(() => idb.getLastKey(updatesStore).then(lastKey => { idbPersistence._dbref = lastKey + 1 }))\n    .then(() => idb.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n    .then(() => updatesStore)\n}\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {boolean} forceStore\n */\nexport const storeState = (idbPersistence, forceStore = true) =>\n  fetchUpdates(idbPersistence)\n    .then(updatesStore => {\n      if (forceStore || idbPersistence._dbsize >= PREFERRED_TRIM_SIZE) {\n        idb.addAutoKey(updatesStore, Y.encodeStateAsUpdate(idbPersistence.doc))\n          .then(() => idb.del(updatesStore, idb.createIDBKeyRangeUpperBound(idbPersistence._dbref, true)))\n          .then(() => idb.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n      }\n    })\n\n/**\n * @param {string} name\n */\nexport const clearDocument = name => idb.deleteDB(name)\n\n/**\n * @extends Observable<string>\n */\nexport class IndexeddbPersistence extends Observable {\n  /**\n   * @param {string} name\n   * @param {Y.Doc} doc\n   */\n  constructor (name, doc) {\n    super()\n    this.doc = doc\n    this.name = name\n    this._dbref = 0\n    this._dbsize = 0\n    this._destroyed = false\n    /**\n     * @type {IDBDatabase|null}\n     */\n    this.db = null\n    this.synced = false\n    this._db = idb.openDB(name, db =>\n      idb.createStores(db, [\n        ['updates', { autoIncrement: true }],\n        ['custom']\n      ])\n    )\n    /**\n     * @type {Promise<IndexeddbPersistence>}\n     */\n    this.whenSynced = promise.create(resolve => this.on('synced', () => resolve(this)))\n\n    this._db.then(db => {\n      this.db = db\n      /**\n       * @param {IDBObjectStore} updatesStore\n       */\n      const beforeApplyUpdatesCallback = (updatesStore) => idb.addAutoKey(updatesStore, Y.encodeStateAsUpdate(doc))\n      const afterApplyUpdatesCallback = () => {\n        if (this._destroyed) return this\n        this.synced = true\n        this.emit('synced', [this])\n      }\n      fetchUpdates(this, beforeApplyUpdatesCallback, afterApplyUpdatesCallback)\n    })\n    /**\n     * Timeout in ms untill data is merged and persisted in idb.\n     */\n    this._storeTimeout = 1000\n    /**\n     * @type {any}\n     */\n    this._storeTimeoutId = null\n    /**\n     * @param {Uint8Array} update\n     * @param {any} origin\n     */\n    this._storeUpdate = (update, origin) => {\n      if (this.db && origin !== this) {\n        const [updatesStore] = idb.transact(/** @type {IDBDatabase} */ (this.db), [updatesStoreName])\n        idb.addAutoKey(updatesStore, update)\n        if (++this._dbsize >= PREFERRED_TRIM_SIZE) {\n          // debounce store call\n          if (this._storeTimeoutId !== null) {\n            clearTimeout(this._storeTimeoutId)\n          }\n          this._storeTimeoutId = setTimeout(() => {\n            storeState(this, false)\n            this._storeTimeoutId = null\n          }, this._storeTimeout)\n        }\n      }\n    }\n    doc.on('update', this._storeUpdate)\n    this.destroy = this.destroy.bind(this)\n    doc.on('destroy', this.destroy)\n  }\n\n  destroy () {\n    if (this._storeTimeoutId) {\n      clearTimeout(this._storeTimeoutId)\n    }\n    this.doc.off('update', this._storeUpdate)\n    this.doc.off('destroy', this.destroy)\n    this._destroyed = true\n    return this._db.then(db => {\n      db.close()\n    })\n  }\n\n  /**\n   * Destroys this instance and removes all data from indexeddb.\n   *\n   * @return {Promise<void>}\n   */\n  clearData () {\n    return this.destroy().then(() => {\n      idb.deleteDB(this.name)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<String | number | ArrayBuffer | Date | any>}\n   */\n  get (key) {\n    return this._db.then(db => {\n      const [custom] = idb.transact(db, [customStoreName], 'readonly')\n      return idb.get(custom, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @param {String | number | ArrayBuffer | Date} value\n   * @return {Promise<String | number | ArrayBuffer | Date>}\n   */\n  set (key, value) {\n    return this._db.then(db => {\n      const [custom] = idb.transact(db, [customStoreName])\n      return idb.put(custom, value, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<undefined>}\n   */\n  del (key) {\n    return this._db.then(db => {\n      const [custom] = idb.transact(db, [customStoreName])\n      return idb.del(custom, key)\n    })\n  }\n}\n"], "names": ["idb.transact", "idb.getAll", "idb.createIDBKeyRangeLowerBound", "<PERSON>.transact", "Y.applyUpdate", "idb.getLastKey", "idb.count", "idb.addAutoKey", "Y.encodeStateAsUpdate", "idb.del", "idb.createIDBKeyRangeUpperBound", "idb.deleteDB", "Observable", "idb.openDB", "idb.createStores", "promise.create", "idb.get", "idb.put"], "mappings": ";;;;;;;;;AAKA,MAAM,eAAe,GAAG,SAAQ;AAChC,MAAM,gBAAgB,GAAG,UAAS;AAClC;AACY,MAAC,mBAAmB,GAAG,IAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,YAAY,GAAG,CAAC,cAAc,EAAE,0BAA0B,GAAG,MAAM,EAAE,EAAE,yBAAyB,GAAG,MAAM,EAAE,KAAK;AAC7H,EAAE,MAAM,CAAC,YAAY,CAAC,GAAGA,YAAY,6BAA6B,cAAc,CAAC,EAAE,GAAG,CAAC,gBAAgB,CAAC,EAAC;AACzG,EAAE,OAAOC,UAAU,CAAC,YAAY,EAAEC,+BAA+B,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI;AACjH,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;AACpC,MAAM,0BAA0B,CAAC,YAAY,EAAC;AAC9C,MAAMC,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM;AAC3C,QAAQ,OAAO,CAAC,OAAO,CAAC,GAAG,IAAIC,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,EAAC;AACtE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAC;AAC/B,MAAM,yBAAyB,CAAC,YAAY,EAAC;AAC7C,KAAK;AACL,GAAG,CAAC;AACJ,KAAK,IAAI,CAAC,MAAMC,cAAc,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,cAAc,CAAC,MAAM,GAAG,OAAO,GAAG,EAAC,EAAE,CAAC,CAAC;AACtG,KAAK,IAAI,CAAC,MAAMC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,cAAc,CAAC,OAAO,GAAG,IAAG,EAAE,CAAC,CAAC;AACtF,KAAK,IAAI,CAAC,MAAM,YAAY,CAAC;AAC7B,EAAC;AACD;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,CAAC,cAAc,EAAE,UAAU,GAAG,IAAI;AAC5D,EAAE,YAAY,CAAC,cAAc,CAAC;AAC9B,KAAK,IAAI,CAAC,YAAY,IAAI;AAC1B,MAAM,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,IAAI,mBAAmB,EAAE;AACvE,QAAQC,cAAc,CAAC,YAAY,EAAEC,qBAAqB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AAC/E,WAAW,IAAI,CAAC,MAAMC,OAAO,CAAC,YAAY,EAAEC,+BAA+B,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1G,WAAW,IAAI,CAAC,MAAMJ,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,cAAc,CAAC,OAAO,GAAG,IAAG,EAAE,CAAC,EAAC;AAC5F,OAAO;AACP,KAAK,EAAC;AACN;AACA;AACA;AACA;AACY,MAAC,aAAa,GAAG,IAAI,IAAIK,YAAY,CAAC,IAAI,EAAC;AACvD;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAASC,qBAAU,CAAC;AACrD;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE;AAC1B,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,IAAG;AAClB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAI;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAC;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAC;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,MAAK;AAC3B;AACA;AACA;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,KAAI;AAClB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAK;AACvB,IAAI,IAAI,CAAC,GAAG,GAAGC,UAAU,CAAC,IAAI,EAAE,EAAE;AAClC,MAAMC,gBAAgB,CAAC,EAAE,EAAE;AAC3B,QAAQ,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;AAC5C,QAAQ,CAAC,QAAQ,CAAC;AAClB,OAAO,CAAC;AACR,MAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,CAAC,UAAU,GAAGC,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC;AACvF;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI;AACxB,MAAM,IAAI,CAAC,EAAE,GAAG,GAAE;AAClB;AACA;AACA;AACA,MAAM,MAAM,0BAA0B,GAAG,CAAC,YAAY,KAAKR,cAAc,CAAC,YAAY,EAAEC,qBAAqB,CAAC,GAAG,CAAC,EAAC;AACnH,MAAM,MAAM,yBAAyB,GAAG,MAAM;AAC9C,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI;AACxC,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAI;AAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAC;AACnC,QAAO;AACP,MAAM,YAAY,CAAC,IAAI,EAAE,0BAA0B,EAAE,yBAAyB,EAAC;AAC/E,KAAK,EAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,KAAI;AAC7B;AACA;AACA;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,KAAI;AAC/B;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;AAC5C,MAAM,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,KAAK,IAAI,EAAE;AACtC,QAAQ,MAAM,CAAC,YAAY,CAAC,GAAGR,YAAY,6BAA6B,IAAI,CAAC,EAAE,GAAG,CAAC,gBAAgB,CAAC,EAAC;AACrG,QAAQO,cAAc,CAAC,YAAY,EAAE,MAAM,EAAC;AAC5C,QAAQ,IAAI,EAAE,IAAI,CAAC,OAAO,IAAI,mBAAmB,EAAE;AACnD;AACA,UAAU,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AAC7C,YAAY,YAAY,CAAC,IAAI,CAAC,eAAe,EAAC;AAC9C,WAAW;AACX,UAAU,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,MAAM;AAClD,YAAY,UAAU,CAAC,IAAI,EAAE,KAAK,EAAC;AACnC,YAAY,IAAI,CAAC,eAAe,GAAG,KAAI;AACvC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAC;AAChC,SAAS;AACT,OAAO;AACP,MAAK;AACL,IAAI,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAC;AACvC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC;AAC1C,IAAI,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAC;AACnC,GAAG;AACH;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;AAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,eAAe,EAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAC;AAC7C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAC;AACzC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAI;AAC1B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B,MAAM,EAAE,CAAC,KAAK,GAAE;AAChB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AACrC,MAAMI,YAAY,CAAC,IAAI,CAAC,IAAI,EAAC;AAC7B,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;AACZ,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B,MAAM,MAAM,CAAC,MAAM,CAAC,GAAGX,YAAY,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,UAAU,EAAC;AACtE,MAAM,OAAOgB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACjC,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B,MAAM,MAAM,CAAC,MAAM,CAAC,GAAGhB,YAAY,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAC;AAC1D,MAAM,OAAOiB,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;AACxC,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;AACZ,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B,MAAM,MAAM,CAAC,MAAM,CAAC,GAAGjB,YAAY,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAC;AAC1D,MAAM,OAAOS,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACjC,KAAK,CAAC;AACN,GAAG;AACH;;;;;;;;"}
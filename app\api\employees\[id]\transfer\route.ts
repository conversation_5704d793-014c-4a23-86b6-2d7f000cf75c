import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// POST /api/employees/[id]/transfer - Transfer employee to different department
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { toDepartmentId, reason } = body

    // Find the employee
    const employee = await prisma.employee.findUnique({
      where: { id },
      include: { department: true }
    })

    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      )
    }

    // Validate target department if provided
    let targetDepartment = null
    if (toDepartmentId && toDepartmentId !== 'unassigned') {
      targetDepartment = await prisma.department.findUnique({
        where: { id: toDepartmentId }
      })

      if (!targetDepartment) {
        return NextResponse.json(
          { error: 'Target department not found' },
          { status: 404 }
        )
      }
    }

    // Check if it's actually a transfer (different department)
    if (employee.departmentId === (toDepartmentId === 'unassigned' ? null : toDepartmentId)) {
      return NextResponse.json(
        { error: 'Employee is already in this department' },
        { status: 400 }
      )
    }

    // Generate new employee ID if transferring to a different department
    let newEmployeeId = employee.id
    if (toDepartmentId && toDepartmentId !== 'unassigned') {
      // Get next sequence number for target department
      const lastEmployee = await prisma.employee.findFirst({
        where: {
          id: {
            startsWith: targetDepartment!.code + '-'
          }
        },
        orderBy: {
          id: 'desc'
        }
      })

      let nextSequence = 1
      if (lastEmployee) {
        const lastSequence = parseInt(lastEmployee.id.split('-')[1]) || 0
        nextSequence = lastSequence + 1
      }

      newEmployeeId = `${targetDepartment!.code}-${nextSequence.toString().padStart(3, '0')}`
    } else if (toDepartmentId === 'unassigned') {
      // Moving to free bucket
      const lastFreeEmployee = await prisma.employee.findFirst({
        where: {
          id: {
            startsWith: 'FB-'
          }
        },
        orderBy: {
          id: 'desc'
        }
      })

      let nextSequence = 1
      if (lastFreeEmployee) {
        const lastSequence = parseInt(lastFreeEmployee.id.split('-')[1]) || 0
        nextSequence = lastSequence + 1
      }

      newEmployeeId = `FB-${nextSequence.toString().padStart(3, '0')}`
    }

    // Create transfer record
    const transferRecord = {
      fromDepartmentId: employee.departmentId,
      toDepartmentId: toDepartmentId === 'unassigned' ? null : toDepartmentId,
      timestamp: new Date(),
      reason: reason || 'نقل موظف',
      userId: session.user.id
    }

    // Update employee with new department and transfer history
    const updatedEmployee = await prisma.employee.update({
      where: { id },
      data: {
        id: newEmployeeId,
        departmentId: toDepartmentId === 'unassigned' ? null : toDepartmentId,
        transferHistory: {
          push: transferRecord
        },
        updatedAt: new Date()
      },
      include: {
        department: true
      }
    })

    return NextResponse.json({
      employee: updatedEmployee,
      transfer: transferRecord
    })
  } catch (error) {
    console.error('Error transferring employee:', error)
    return NextResponse.json(
      { error: 'Failed to transfer employee' },
      { status: 500 }
    )
  }
}

import { test, expect } from '@playwright/test'

test.describe('Offline and Sync Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('should work in offline mode', async ({ page }) => {
    // Go offline
    await page.context().setOffline(true)
    
    // Navigate to sync status page
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Verify offline status is displayed
    await expect(page.locator('text=غير متصل')).toBeVisible()
    await expect(page.locator('[data-testid="offline-notice"]')).toBeVisible()
    
    // Test offline functionality
    await page.click('text=الموظفون')
    
    // Should still be able to view cached data
    await expect(page.locator('[data-testid="employee-list"]')).toBeVisible()
    
    // Try to add employee offline
    await page.click('text=إضافة موظف')
    await page.fill('[data-testid="employee-name"]', 'موظف أوفلاين')
    await page.fill('[data-testid="employee-email"]', '<EMAIL>')
    await page.click('text=حفظ')
    
    // Should save locally
    await expect(page.locator('text=تم حفظ البيانات محلياً')).toBeVisible()
  })

  test('should sync data when coming back online', async ({ page }) => {
    // Start offline
    await page.context().setOffline(true)
    
    // Add data offline
    await page.click('text=الموظفون')
    await page.click('text=إضافة موظف')
    await page.fill('[data-testid="employee-name"]', 'موظف للمزامنة')
    await page.fill('[data-testid="employee-email"]', '<EMAIL>')
    await page.click('text=حفظ')
    
    // Go back online
    await page.context().setOffline(false)
    
    // Navigate to sync page
    await page.click('text=المزامنة')
    
    // Verify online status
    await expect(page.locator('text=متصل')).toBeVisible()
    
    // Trigger manual sync
    await page.click('text=مزامنة يدوية')
    
    // Verify sync completed
    await expect(page.locator('text=تمت المزامنة بنجاح')).toBeVisible()
  })

  test('should display sync status and statistics', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Check sync status elements
    await expect(page.locator('[data-testid="connection-status"]')).toBeVisible()
    await expect(page.locator('[data-testid="last-sync"]')).toBeVisible()
    await expect(page.locator('[data-testid="pending-changes"]')).toBeVisible()
    
    // Check storage statistics
    await expect(page.locator('[data-testid="employees-stored"]')).toBeVisible()
    await expect(page.locator('[data-testid="departments-stored"]')).toBeVisible()
    await expect(page.locator('[data-testid="storage-size"]')).toBeVisible()
  })

  test('should enable/disable auto-sync', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Toggle auto-sync
    await page.click('[data-testid="auto-sync-toggle"]')
    
    // Verify auto-sync status changed
    await expect(page.locator('text=تم تفعيل المزامنة التلقائية')).toBeVisible()
    
    // Toggle off
    await page.click('[data-testid="auto-sync-toggle"]')
    await expect(page.locator('text=تم إيقاف المزامنة التلقائية')).toBeVisible()
  })

  test('should load data from local storage', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Click load from local storage
    await page.click('text=تحميل من المحلي')
    
    // Verify data loaded
    await expect(page.locator('text=تم تحميل البيانات من التخزين المحلي')).toBeVisible()
    
    // Check that data is displayed
    await page.click('text=الموظفون')
    await expect(page.locator('[data-testid="employee-list"]')).toBeVisible()
  })

  test('should save data to local storage', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Click save to local storage
    await page.click('text=حفظ في المحلي')
    
    // Verify data saved
    await expect(page.locator('text=تم حفظ البيانات في التخزين المحلي')).toBeVisible()
  })

  test('should clear local storage', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Click clear local storage
    await page.click('text=مسح البيانات المحلية')
    
    // Confirm action
    await page.click('text=موافق')
    
    // Verify data cleared
    await expect(page.locator('text=تم مسح جميع البيانات المحلية')).toBeVisible()
    
    // Check storage statistics reset
    await expect(page.locator('[data-testid="employees-stored"]')).toContainText('0')
    await expect(page.locator('[data-testid="departments-stored"]')).toContainText('0')
  })

  test('should handle sync conflicts', async ({ page }) => {
    // This test would require a more complex setup with actual server
    // For now, we'll test the UI elements for conflict resolution
    
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Check if conflict resolution UI exists
    const conflictSection = page.locator('[data-testid="sync-conflicts"]')
    if (await conflictSection.isVisible()) {
      await expect(conflictSection).toContainText('تعارضات في المزامنة')
      
      // Check conflict resolution options
      await expect(page.locator('text=حل التعارضات')).toBeVisible()
    }
  })

  test('should show network status changes', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Start online
    await expect(page.locator('text=متصل')).toBeVisible()
    
    // Go offline
    await page.context().setOffline(true)
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Should show offline status
    await expect(page.locator('text=غير متصل')).toBeVisible()
    await expect(page.locator('[data-testid="offline-notice"]')).toBeVisible()
    
    // Go back online
    await page.context().setOffline(false)
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Should show online status
    await expect(page.locator('text=متصل')).toBeVisible()
  })

  test('should persist data across browser sessions', async ({ page, context }) => {
    // Add some data
    await page.click('text=الموظفون')
    await page.click('text=إضافة موظف')
    await page.fill('[data-testid="employee-name"]', 'موظف دائم')
    await page.fill('[data-testid="employee-email"]', '<EMAIL>')
    await page.click('text=حفظ')
    
    // Save to local storage
    await page.click('text=المزامنة')
    await page.click('text=حفظ في المحلي')
    
    // Close and reopen browser
    await page.close()
    const newPage = await context.newPage()
    await newPage.goto('/')
    await newPage.waitForLoadState('networkidle')
    
    // Load from local storage
    await newPage.click('text=المزامنة')
    await newPage.click('text=تحميل من المحلي')
    
    // Verify data persisted
    await newPage.click('text=الموظفون')
    await expect(newPage.locator('text=موظف دائم')).toBeVisible()
  })
})

test.describe('Local Storage Performance', () => {
  test('should handle large datasets efficiently', async ({ page }) => {
    await page.goto('/')
    
    // Navigate to sync page
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Measure save performance
    const startTime = Date.now()
    await page.click('text=حفظ في المحلي')
    await page.waitForSelector('text=تم حفظ البيانات في التخزين المحلي')
    const saveTime = Date.now() - startTime
    
    // Should complete within reasonable time (5 seconds)
    expect(saveTime).toBeLessThan(5000)
    
    // Measure load performance
    const loadStartTime = Date.now()
    await page.click('text=تحميل من المحلي')
    await page.waitForSelector('text=تم تحميل البيانات من التخزين المحلي')
    const loadTime = Date.now() - loadStartTime
    
    // Should complete within reasonable time (5 seconds)
    expect(loadTime).toBeLessThan(5000)
  })

  test('should show storage usage statistics', async ({ page }) => {
    await page.click('text=المزامنة')
    await page.waitForLoadState('networkidle')
    
    // Check storage statistics
    const storageSize = page.locator('[data-testid="storage-size"]')
    await expect(storageSize).toBeVisible()
    
    const sizeText = await storageSize.textContent()
    expect(sizeText).toMatch(/(بايت|كيلوبايت|ميجابايت)/)
  })
})

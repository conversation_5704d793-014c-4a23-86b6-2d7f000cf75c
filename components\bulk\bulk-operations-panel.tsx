"use client"

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useHRStore } from '@/lib/store/hr-store'
import { csvImportService } from '@/lib/services/csv-import-service'
import { CSVImportDialog } from './csv-import-dialog'
import { 
  Upload, 
  Download, 
  Users, 
  Shuffle,
  Archive,
  FileText,
  BarChart3,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { toast } from 'sonner'

export function BulkOperationsPanel() {
  const { data: session } = useSession()
  const { 
    employees, 
    departments, 
    selectedEmployees, 
    freeBucket,
    clearSelection,
    executeBulkOperation
  } = useHRStore()
  
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // Statistics
  const totalEmployees = employees.length
  const activeEmployees = employees.filter(emp => emp.status === 'ACTIVE').length
  const freeBucketCount = freeBucket.length
  const selectedCount = selectedEmployees.length

  const handleExportEmployees = () => {
    if (employees.length === 0) {
      toast.warning('لا توجد موظفين للتصدير')
      return
    }

    csvImportService.exportEmployees(employees, departments)
    toast.success(`تم تصدير ${employees.length} موظف`)
  }

  const handleExportSelected = () => {
    if (selectedEmployees.length === 0) {
      toast.warning('يرجى اختيار موظفين للتصدير')
      return
    }

    const selectedEmployeeData = employees.filter(emp => 
      selectedEmployees.includes(emp.id)
    )
    
    csvImportService.exportEmployees(selectedEmployeeData, departments)
    toast.success(`تم تصدير ${selectedEmployees.length} موظف`)
  }

  const handleBulkArchive = async () => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    if (selectedEmployees.length === 0) {
      toast.warning('يرجى اختيار موظفين للأرشفة')
      return
    }

    const confirmed = window.confirm(
      `هل أنت متأكد من أرشفة ${selectedEmployees.length} موظف؟ هذا الإجراء لا يمكن التراجع عنه.`
    )
    
    if (!confirmed) return

    setIsProcessing(true)
    try {
      await executeBulkOperation({
        type: 'delete',
        employeeIds: selectedEmployees
      }, session.user.id)
      
      clearSelection()
      toast.success(`تم أرشفة ${selectedEmployees.length} موظف بنجاح`)
    } catch (error) {
      toast.error('حدث خطأ أثناء الأرشفة')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkTransfer = async (targetDepartmentId: string | null) => {
    if (!session?.user?.id) {
      toast.error('يجب تسجيل الدخول أولاً')
      return
    }

    if (selectedEmployees.length === 0) {
      toast.warning('يرجى اختيار موظفين للنقل')
      return
    }

    setIsProcessing(true)
    try {
      await executeBulkOperation({
        type: 'transfer',
        employeeIds: selectedEmployees,
        targetDepartmentId
      }, session.user.id)
      
      clearSelection()
      const targetName = targetDepartmentId 
        ? departments.find(d => d.id === targetDepartmentId)?.name || 'قسم غير معروف'
        : 'السلة المؤقتة'
      
      toast.success(`تم نقل ${selectedEmployees.length} موظف إلى ${targetName}`)
    } catch (error) {
      toast.error('حدث خطأ أثناء النقل')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container-spaced">
      {/* Statistics Overview */}
      <div className="grid-spaced md:grid-cols-4">
        <Card className="gradient-green-soft border-r-4 border-r-primary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الموظفين</CardTitle>
            <Users className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              {activeEmployees} نشط
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-secondary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">السلة المؤقتة</CardTitle>
            <Archive className="h-4 w-4 text-secondary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-secondary">{freeBucketCount}</div>
            <p className="text-xs text-muted-foreground">
              غير مخصص
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-accent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">محدد</CardTitle>
            <CheckCircle className="h-4 w-4 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-accent">{selectedCount}</div>
            <p className="text-xs text-muted-foreground">
              موظف محدد
            </p>
          </CardContent>
        </Card>

        <Card className="gradient-green-soft border-r-4 border-r-success">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأقسام</CardTitle>
            <BarChart3 className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{departments.length}</div>
            <p className="text-xs text-muted-foreground">
              قسم متاح
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Import/Export Operations */}
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center shadow-lg">
              <FileText className="h-4 w-4 text-white" />
            </div>
            عمليات الاستيراد والتصدير
          </CardTitle>
        </CardHeader>
        <CardContent className="container-spaced">
          <div className="grid-spaced md:grid-cols-2">
            {/* Import Section */}
            <div className="container-spaced">
              <h4 className="font-semibold text-foreground mb-3">الاستيراد</h4>
              <Button
                onClick={() => setShowImportDialog(true)}
                className="w-full gradient-primary hover:opacity-90 shadow-medium hover-lift"
              >
                <Upload className="h-4 w-4 ml-2" />
                استيراد من CSV
              </Button>
              <p className="text-xs text-muted-foreground">
                استيراد بيانات الموظفين بشكل مجمع من ملف CSV
              </p>
            </div>

            {/* Export Section */}
            <div className="container-spaced">
              <h4 className="font-semibold text-foreground mb-3">التصدير</h4>
              <div className="flex gap-2">
                <Button
                  onClick={handleExportEmployees}
                  variant="outline"
                  className="flex-1 shadow-soft hover-lift border-2 border-border/50"
                  disabled={totalEmployees === 0}
                >
                  <Download className="h-4 w-4 ml-1" />
                  تصدير الكل
                </Button>
                <Button
                  onClick={handleExportSelected}
                  variant="outline"
                  className="flex-1 shadow-soft hover-lift border-2 border-border/50"
                  disabled={selectedCount === 0}
                >
                  <Download className="h-4 w-4 ml-1" />
                  تصدير المحدد ({selectedCount})
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                تصدير بيانات الموظفين إلى ملف CSV
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedCount > 0 && (
        <Card className="shadow-xl border-0 bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-blue-900">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                <Users className="h-4 w-4 text-white" />
              </div>
              العمليات الجماعية
              <Badge variant="secondary">{selectedCount} محدد</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="container-spaced">
            <div className="grid-spaced md:grid-cols-2">
              {/* Transfer Operations */}
              <div className="container-spaced">
                <h4 className="font-semibold text-blue-900 mb-3">نقل جماعي</h4>
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={() => handleBulkTransfer(null)}
                    variant="outline"
                    size="sm"
                    disabled={isProcessing}
                    className="text-blue-700 border-blue-300 hover:bg-blue-100"
                  >
                    <Archive className="h-4 w-4 ml-1" />
                    إلى السلة المؤقتة
                  </Button>
                  {departments.slice(0, 3).map((dept) => (
                    <Button
                      key={dept.id}
                      onClick={() => handleBulkTransfer(dept.id)}
                      variant="outline"
                      size="sm"
                      disabled={isProcessing}
                      className="text-blue-700 border-blue-300 hover:bg-blue-100"
                    >
                      <Shuffle className="h-4 w-4 ml-1" />
                      إلى {dept.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Danger Operations */}
              <div className="container-spaced">
                <h4 className="font-semibold text-red-900 mb-3">عمليات خطيرة</h4>
                <Button
                  onClick={handleBulkArchive}
                  variant="destructive"
                  size="sm"
                  disabled={isProcessing}
                  className="w-full"
                >
                  <AlertTriangle className="h-4 w-4 ml-1" />
                  {isProcessing ? 'جاري الأرشفة...' : `أرشفة ${selectedCount} موظف`}
                </Button>
                <p className="text-xs text-red-600">
                  تحذير: الأرشفة لا يمكن التراجع عنها
                </p>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={clearSelection}
                variant="outline"
                size="sm"
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                إلغاء التحديد
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* CSV Import Dialog */}
      <CSVImportDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
      />
    </div>
  )
}

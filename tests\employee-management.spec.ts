import { test, expect } from '@playwright/test'

test.describe('Employee Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test('should display employee dashboard', async ({ page }) => {
    // Check if the main dashboard elements are visible
    await expect(page.locator('h1')).toContainText('لوحة التحكم')
    await expect(page.locator('[data-testid="employee-count"]')).toBeVisible()
    await expect(page.locator('[data-testid="department-count"]')).toBeVisible()
  })

  test('should create a new employee', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Click add employee button
    await page.click('text=إضافة موظف')
    
    // Fill employee form
    await page.fill('[data-testid="employee-name"]', 'أحمد محمد')
    await page.fill('[data-testid="employee-email"]', '<EMAIL>')
    await page.fill('[data-testid="employee-phone"]', '0501234567')
    await page.fill('[data-testid="employee-hire-date"]', '2024-01-15')
    
    // Select department
    await page.click('[data-testid="employee-department"]')
    await page.click('text=قسم تقنية المعلومات')
    
    // Submit form
    await page.click('text=حفظ')
    
    // Verify employee was created
    await expect(page.locator('text=تم إضافة الموظف بنجاح')).toBeVisible()
    await expect(page.locator('text=أحمد محمد')).toBeVisible()
  })

  test('should edit employee information', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Click edit button for first employee
    await page.click('[data-testid="edit-employee"]:first-child')
    
    // Update employee name
    await page.fill('[data-testid="employee-name"]', 'أحمد محمد المحدث')
    
    // Submit form
    await page.click('text=حفظ التغييرات')
    
    // Verify employee was updated
    await expect(page.locator('text=تم تحديث بيانات الموظف')).toBeVisible()
    await expect(page.locator('text=أحمد محمد المحدث')).toBeVisible()
  })

  test('should transfer employee to different department', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Select an employee
    await page.check('[data-testid="employee-checkbox"]:first-child')
    
    // Click bulk actions
    await page.click('text=العمليات الجماعية')
    
    // Click transfer
    await page.click('text=نقل')
    
    // Select target department
    await page.click('[data-testid="target-department"]')
    await page.click('text=قسم الموارد البشرية')
    
    // Add transfer reason
    await page.fill('[data-testid="transfer-reason"]', 'نقل إداري')
    
    // Submit transfer
    await page.click('text=تأكيد النقل')
    
    // Verify transfer was successful
    await expect(page.locator('text=تم نقل الموظف بنجاح')).toBeVisible()
  })

  test('should archive employee', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Select an employee
    await page.check('[data-testid="employee-checkbox"]:first-child')
    
    // Click bulk actions
    await page.click('text=العمليات الجماعية')
    
    // Click archive
    await page.click('text=أرشفة')
    
    // Confirm archive action
    await page.click('text=تأكيد الأرشفة')
    
    // Verify employee was archived
    await expect(page.locator('text=تم أرشفة الموظف')).toBeVisible()
  })

  test('should search for employees', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Use search functionality
    await page.fill('[data-testid="employee-search"]', 'أحمد')
    
    // Wait for search results
    await page.waitForTimeout(1000)
    
    // Verify search results
    await expect(page.locator('[data-testid="employee-list"] tr')).toContainText('أحمد')
  })

  test('should filter employees by department', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Apply department filter
    await page.click('[data-testid="department-filter"]')
    await page.click('text=قسم تقنية المعلومات')
    
    // Wait for filter to apply
    await page.waitForTimeout(1000)
    
    // Verify filtered results
    const employeeRows = page.locator('[data-testid="employee-list"] tr')
    await expect(employeeRows).toContainText('قسم تقنية المعلومات')
  })

  test('should display employee details', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Click view details for first employee
    await page.click('[data-testid="view-employee"]:first-child')
    
    // Verify employee details modal/page is displayed
    await expect(page.locator('[data-testid="employee-details"]')).toBeVisible()
    await expect(page.locator('text=تفاصيل الموظف')).toBeVisible()
    
    // Verify employee information is displayed
    await expect(page.locator('[data-testid="employee-name"]')).toBeVisible()
    await expect(page.locator('[data-testid="employee-email"]')).toBeVisible()
    await expect(page.locator('[data-testid="employee-department"]')).toBeVisible()
  })

  test('should validate required fields in employee form', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Click add employee button
    await page.click('text=إضافة موظف')
    
    // Try to submit empty form
    await page.click('text=حفظ')
    
    // Verify validation errors
    await expect(page.locator('text=الاسم مطلوب')).toBeVisible()
    await expect(page.locator('text=البريد الإلكتروني مطلوب')).toBeVisible()
  })

  test('should handle employee ID generation', async ({ page }) => {
    // Navigate to employees page
    await page.click('text=الموظفون')
    await page.waitForLoadState('networkidle')

    // Click add employee button
    await page.click('text=إضافة موظف')
    
    // Fill required fields
    await page.fill('[data-testid="employee-name"]', 'سارة أحمد')
    await page.fill('[data-testid="employee-email"]', '<EMAIL>')
    
    // Select department
    await page.click('[data-testid="employee-department"]')
    await page.click('text=قسم تقنية المعلومات')
    
    // Verify auto-generated ID is displayed
    await expect(page.locator('[data-testid="employee-id"]')).toHaveValue(/IT-\d{3}/)
    
    // Submit form
    await page.click('text=حفظ')
    
    // Verify employee was created with correct ID format
    await expect(page.locator('text=تم إضافة الموظف بنجاح')).toBeVisible()
  })
})

test.describe('Employee Management - RTL Layout', () => {
  test('should display Arabic text correctly in RTL layout', async ({ page }) => {
    await page.goto('/')
    
    // Check RTL direction
    const htmlElement = page.locator('html')
    await expect(htmlElement).toHaveAttribute('dir', 'rtl')
    
    // Check Arabic font
    const bodyElement = page.locator('body')
    const fontFamily = await bodyElement.evaluate(el => getComputedStyle(el).fontFamily)
    expect(fontFamily).toContain('Tajawal')
    
    // Check Arabic text rendering
    await expect(page.locator('text=الموظفون')).toBeVisible()
    await expect(page.locator('text=الأقسام')).toBeVisible()
  })

  test('should have proper spacing and margins', async ({ page }) => {
    await page.goto('/')
    
    // Check container spacing
    const container = page.locator('.container-spaced').first()
    const marginTop = await container.evaluate(el => getComputedStyle(el).marginTop)
    expect(parseInt(marginTop)).toBeGreaterThan(0)
    
    // Check grid spacing
    const gridElement = page.locator('.grid-spaced').first()
    const gap = await gridElement.evaluate(el => getComputedStyle(el).gap)
    expect(gap).toBeTruthy()
  })
})

test.describe('Employee Management - Responsive Design', () => {
  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Check mobile navigation
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
    
    // Test mobile employee list
    await page.click('text=الموظفون')
    await expect(page.locator('[data-testid="employee-list"]')).toBeVisible()
    
    // Check responsive layout
    const employeeCard = page.locator('[data-testid="employee-card"]').first()
    const width = await employeeCard.evaluate(el => el.getBoundingClientRect().width)
    expect(width).toBeLessThan(400) // Should fit mobile screen
  })

  test('should work on tablet devices', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.goto('/')
    
    // Check tablet layout
    await page.click('text=الموظفون')
    
    // Verify grid layout adapts to tablet
    const gridContainer = page.locator('.grid-spaced')
    const gridColumns = await gridContainer.evaluate(el => 
      getComputedStyle(el).gridTemplateColumns
    )
    expect(gridColumns).toContain('repeat') // Should have responsive grid
  })
})

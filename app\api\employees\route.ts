import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/employees - Fetch all employees
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const departmentId = searchParams.get('departmentId')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const where: any = {}
    
    if (departmentId) {
      where.departmentId = departmentId === 'null' ? null : departmentId
    }
    
    if (status) {
      where.status = status
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { id: { contains: search, mode: 'insensitive' } }
      ]
    }

    const employees = await prisma.employee.findMany({
      where,
      include: {
        department: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(employees)
  } catch (error) {
    console.error('Error fetching employees:', error)
    return NextResponse.json(
      { error: 'Failed to fetch employees' },
      { status: 500 }
    )
  }
}

// POST /api/employees - Create new employee
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, departmentId, hireDate } = body

    // Validate required fields
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingEmployee = await prisma.employee.findUnique({
      where: { email }
    })

    if (existingEmployee) {
      return NextResponse.json(
        { error: 'Employee with this email already exists' },
        { status: 409 }
      )
    }

    // Generate employee ID
    let employeeId: string
    if (departmentId && departmentId !== 'unassigned') {
      const department = await prisma.department.findUnique({
        where: { id: departmentId }
      })
      
      if (!department) {
        return NextResponse.json(
          { error: 'Department not found' },
          { status: 404 }
        )
      }

      // Get next sequence number for department
      const lastEmployee = await prisma.employee.findFirst({
        where: {
          id: {
            startsWith: department.code + '-'
          }
        },
        orderBy: {
          id: 'desc'
        }
      })

      let nextSequence = 1
      if (lastEmployee) {
        const lastSequence = parseInt(lastEmployee.id.split('-')[1]) || 0
        nextSequence = lastSequence + 1
      }

      employeeId = `${department.code}-${nextSequence.toString().padStart(3, '0')}`
    } else {
      // Free bucket employee
      const lastFreeEmployee = await prisma.employee.findFirst({
        where: {
          id: {
            startsWith: 'FB-'
          }
        },
        orderBy: {
          id: 'desc'
        }
      })

      let nextSequence = 1
      if (lastFreeEmployee) {
        const lastSequence = parseInt(lastFreeEmployee.id.split('-')[1]) || 0
        nextSequence = lastSequence + 1
      }

      employeeId = `FB-${nextSequence.toString().padStart(3, '0')}`
    }

    // Create employee
    const employee = await prisma.employee.create({
      data: {
        id: employeeId,
        name: name.trim(),
        email: email.toLowerCase().trim(),
        departmentId: departmentId === 'unassigned' ? null : departmentId,
        hireDate: hireDate ? new Date(hireDate) : new Date(),
        status: 'ACTIVE',
        transferHistory: departmentId && departmentId !== 'unassigned' ? [
          {
            fromDepartmentId: null,
            toDepartmentId: departmentId,
            timestamp: new Date(),
            reason: 'تعيين أولي',
            userId: session.user.id
          }
        ] : []
      },
      include: {
        department: true
      }
    })

    return NextResponse.json(employee, { status: 201 })
  } catch (error) {
    console.error('Error creating employee:', error)
    return NextResponse.json(
      { error: 'Failed to create employee' },
      { status: 500 }
    )
  }
}

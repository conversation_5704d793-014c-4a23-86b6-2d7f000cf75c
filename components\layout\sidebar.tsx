"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Users,
  Building2,
  Upload,
  Settings,
  Archive,
  UserCheck,
  User
} from "lucide-react"

const navigation = [
  {
    name: "لوحة التحكم",
    href: "/dashboard",
    icon: LayoutDashboard,
    roles: ["ADMI<PERSON>", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "الموظفون",
    href: "/employees",
    icon: Users,
    roles: ["ADMIN", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "الأقسام",
    href: "/departments",
    icon: Building2,
    roles: ["ADMIN", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "العمليات المجمعة",
    href: "/bulk",
    icon: Upload,
    roles: ["ADMIN", "HR_MANAGER"]
  },
  {
    name: "السلة المؤقتة",
    href: "/free-bucket",
    icon: Archive,
    roles: ["ADMIN", "HR_MANAGER"]
  },
  {
    name: "إدارة المستخدمين",
    href: "/admin/users",
    icon: UserCheck,
    roles: ["ADMIN"]
  },
  {
    name: "الإعدادات",
    href: "/settings",
    icon: Settings,
    roles: ["ADMIN", "HR_MANAGER"]
  }
]

export function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const userRole = session?.user?.role

  const filteredNavigation = navigation.filter(item =>
    item.roles.includes(userRole as string)
  )

  return (
    <div className="flex h-full w-72 flex-col bg-gradient-to-b from-slate-50 to-white border-l border-border/50 shadow-lg">
      <div className="flex h-20 items-center px-8 border-b border-border/50">
        <div className="flex items-center rtl-space-x">
          <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg">
            <div className="text-white text-lg font-bold">HR</div>
          </div>
          <div className="mr-3">
            <h1 className="text-2xl font-bold gradient-primary bg-clip-text text-transparent">
              HR Synergy
            </h1>
            <p className="text-xs text-muted-foreground font-medium">
              إدارة الموظفين
            </p>
          </div>
        </div>
      </div>

      <nav className="flex-1 container-spaced p-6">
        <div className="mb-6">
          <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
            التنقل
          </p>
        </div>

        <div className="flex flex-col gap-2">
          {filteredNavigation.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + "/")

            return (
              <Link key={item.name} href={item.href}>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start h-12 px-4 rounded-xl font-medium transition-all duration-200 group",
                    isActive
                      ? "gradient-primary text-white shadow-lg hover:opacity-90"
                      : "hover:bg-muted/50 hover:translate-x-1 text-muted-foreground hover:text-foreground"
                  )}
                >
                  <item.icon className={cn(
                    "ml-3 h-5 w-5 transition-colors",
                    isActive ? "text-white" : "text-muted-foreground group-hover:text-foreground"
                  )} />
                  <span className="text-sm">{item.name}</span>
                  {isActive && (
                    <div className="mr-auto w-2 h-2 bg-white rounded-full opacity-80" />
                  )}
                </Button>
              </Link>
            )
          })}
        </div>
      </nav>

      <div className="p-6 border-t border-border/50">
        <div className="gradient-green-soft rounded-xl p-4 border border-primary/20">
          <div className="flex items-center rtl-space-x">
            <div className="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center shadow-md">
              <User className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1 min-w-0 mr-3">
              <p className="text-sm font-medium text-foreground truncate">
                {session?.user?.name}
              </p>
              <p className="text-xs text-muted-foreground">
                {session?.user?.role?.replace('_', ' ')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
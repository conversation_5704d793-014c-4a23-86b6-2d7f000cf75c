import { Department, Employee } from '@/lib/types'
import { toast } from 'sonner'

export interface CreateDepartmentData {
  name: string
  code: string
  capacity: number
  userId: string // For audit trail
}

export interface UpdateDepartmentData {
  name?: string
  code?: string
  capacity?: number
  userId: string // For audit trail
}

export interface DepartmentServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
  validationErrors?: Record<string, string>
}

export interface DepartmentStats {
  id: string
  name: string
  code: string
  capacity: number
  currentEmployees: number
  utilizationPercentage: number
  availableSlots: number
  employees: Employee[]
  isOverCapacity: boolean
}

export class DepartmentService {
  private static instance: DepartmentService

  private constructor() {}

  static getInstance(): DepartmentService {
    if (!DepartmentService.instance) {
      DepartmentService.instance = new DepartmentService()
    }
    return DepartmentService.instance
  }

  /**
   * Create a new department
   */
  async createDepartment(
    data: CreateDepartmentData,
    existingDepartments: Department[]
  ): Promise<DepartmentServiceResult<Department>> {
    try {
      // Validate input data
      const validation = this.validateDepartmentData(data, existingDepartments)
      if (!validation.success) {
        return validation
      }

      // Create department object
      const now = new Date()
      const department: Department = {
        id: crypto.randomUUID(),
        name: data.name.trim(),
        code: data.code.toUpperCase().trim(),
        capacity: data.capacity,
        employees: [],
        createdAt: now,
        updatedAt: now
      }

      return {
        success: true,
        data: department
      }
    } catch (error) {
      console.error('Error creating department:', error)
      return {
        success: false,
        error: 'خطأ في إنشاء القسم'
      }
    }
  }

  /**
   * Update department information
   */
  async updateDepartment(
    departmentId: string,
    data: UpdateDepartmentData,
    existingDepartments: Department[],
    employees: Employee[]
  ): Promise<DepartmentServiceResult<Department>> {
    try {
      const department = existingDepartments.find(d => d.id === departmentId)
      if (!department) {
        return {
          success: false,
          error: 'القسم غير موجود'
        }
      }

      // Validate unique constraints if being updated
      const validationErrors: Record<string, string> = {}

      if (data.name && data.name !== department.name) {
        const nameExists = existingDepartments.some(d => 
          d.id !== departmentId && d.name.toLowerCase() === data.name!.toLowerCase()
        )
        if (nameExists) {
          validationErrors.name = 'اسم القسم مستخدم بالفعل'
        }
      }

      if (data.code && data.code !== department.code) {
        const codeExists = existingDepartments.some(d => 
          d.id !== departmentId && d.code.toUpperCase() === data.code!.toUpperCase()
        )
        if (codeExists) {
          validationErrors.code = 'رمز القسم مستخدم بالفعل'
        }
      }

      // Validate capacity reduction
      if (data.capacity !== undefined && data.capacity < department.capacity) {
        const currentEmployees = employees.filter(emp => 
          emp.departmentId === departmentId && emp.status === 'ACTIVE'
        ).length
        
        if (data.capacity < currentEmployees) {
          validationErrors.capacity = `لا يمكن تقليل السعة إلى ${data.capacity} لأن القسم يحتوي على ${currentEmployees} موظف نشط`
        }
      }

      if (Object.keys(validationErrors).length > 0) {
        return {
          success: false,
          validationErrors
        }
      }

      // Create updated department
      const updatedDepartment: Department = {
        ...department,
        name: data.name?.trim() || department.name,
        code: data.code?.toUpperCase().trim() || department.code,
        capacity: data.capacity ?? department.capacity,
        updatedAt: new Date()
      }

      return {
        success: true,
        data: updatedDepartment
      }
    } catch (error) {
      console.error('Error updating department:', error)
      return {
        success: false,
        error: 'خطأ في تحديث القسم'
      }
    }
  }

  /**
   * Delete department (with employee reassignment check)
   */
  async deleteDepartment(
    departmentId: string,
    existingDepartments: Department[],
    employees: Employee[]
  ): Promise<DepartmentServiceResult<boolean>> {
    try {
      const department = existingDepartments.find(d => d.id === departmentId)
      if (!department) {
        return {
          success: false,
          error: 'القسم غير موجود'
        }
      }

      // Check if department has active employees
      const activeEmployees = employees.filter(emp => 
        emp.departmentId === departmentId && emp.status === 'ACTIVE'
      )

      if (activeEmployees.length > 0) {
        return {
          success: false,
          error: `لا يمكن حذف القسم لأنه يحتوي على ${activeEmployees.length} موظف نشط. يرجى نقل الموظفين أولاً.`
        }
      }

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Error deleting department:', error)
      return {
        success: false,
        error: 'خطأ في حذف القسم'
      }
    }
  }

  /**
   * Get department statistics
   */
  getDepartmentStats(
    departments: Department[],
    employees: Employee[]
  ): DepartmentStats[] {
    return departments.map(dept => {
      const departmentEmployees = employees.filter(emp => 
        emp.departmentId === dept.id && emp.status === 'ACTIVE'
      )
      
      const currentEmployees = departmentEmployees.length
      const utilizationPercentage = dept.capacity > 0 
        ? Math.round((currentEmployees / dept.capacity) * 100)
        : 0
      const availableSlots = Math.max(0, dept.capacity - currentEmployees)
      const isOverCapacity = currentEmployees > dept.capacity

      return {
        id: dept.id,
        name: dept.name,
        code: dept.code,
        capacity: dept.capacity,
        currentEmployees,
        utilizationPercentage,
        availableSlots,
        employees: departmentEmployees,
        isOverCapacity
      }
    })
  }

  /**
   * Get department utilization summary
   */
  getUtilizationSummary(
    departments: Department[],
    employees: Employee[]
  ) {
    const stats = this.getDepartmentStats(departments, employees)
    
    const totalCapacity = departments.reduce((sum, dept) => sum + dept.capacity, 0)
    const totalEmployees = employees.filter(emp => 
      emp.departmentId && emp.status === 'ACTIVE'
    ).length
    const unassignedEmployees = employees.filter(emp => 
      !emp.departmentId && emp.status === 'ACTIVE'
    ).length

    const overCapacityDepartments = stats.filter(stat => stat.isOverCapacity)
    const underUtilizedDepartments = stats.filter(stat => 
      stat.utilizationPercentage < 50 && stat.capacity > 0
    )
    const fullDepartments = stats.filter(stat => stat.utilizationPercentage >= 100)

    return {
      totalCapacity,
      totalEmployees,
      unassignedEmployees,
      overallUtilization: totalCapacity > 0 ? Math.round((totalEmployees / totalCapacity) * 100) : 0,
      availableSlots: totalCapacity - totalEmployees,
      departmentCount: departments.length,
      overCapacityCount: overCapacityDepartments.length,
      underUtilizedCount: underUtilizedDepartments.length,
      fullDepartmentCount: fullDepartments.length,
      overCapacityDepartments,
      underUtilizedDepartments,
      fullDepartments
    }
  }

  /**
   * Validate department data
   */
  private validateDepartmentData(
    data: CreateDepartmentData,
    existingDepartments: Department[]
  ): DepartmentServiceResult {
    const errors: Record<string, string> = {}

    // Validate name
    if (!data.name || data.name.trim().length < 2) {
      errors.name = 'اسم القسم يجب أن يكون على الأقل حرفين'
    } else {
      // Check name uniqueness
      const nameExists = existingDepartments.some(d => 
        d.name.toLowerCase() === data.name.toLowerCase()
      )
      if (nameExists) {
        errors.name = 'اسم القسم مستخدم بالفعل'
      }
    }

    // Validate code
    const codeRegex = /^[A-Z]{2,5}$/
    if (!data.code || !codeRegex.test(data.code.toUpperCase())) {
      errors.code = 'رمز القسم يجب أن يكون من 2-5 أحرف كبيرة'
    } else {
      // Check code uniqueness
      const codeExists = existingDepartments.some(d => 
        d.code.toUpperCase() === data.code.toUpperCase()
      )
      if (codeExists) {
        errors.code = 'رمز القسم مستخدم بالفعل'
      }
    }

    // Validate capacity
    if (!data.capacity || data.capacity < 1 || data.capacity > 1000) {
      errors.capacity = 'سعة القسم يجب أن تكون بين 1 و 1000'
    }

    if (Object.keys(errors).length > 0) {
      return {
        success: false,
        validationErrors: errors
      }
    }

    return { success: true }
  }

  /**
   * Suggest department code based on name
   */
  suggestDepartmentCode(name: string, existingDepartments: Department[]): string {
    if (!name) return ''

    // Extract first letters of words
    const words = name.trim().split(/\s+/)
    let code = ''

    if (words.length === 1) {
      // Single word - take first 3 characters
      code = words[0].substring(0, 3).toUpperCase()
    } else {
      // Multiple words - take first letter of each word
      code = words.map(word => word.charAt(0)).join('').toUpperCase()
    }

    // Ensure minimum length of 2
    if (code.length < 2) {
      code = name.substring(0, 2).toUpperCase()
    }

    // Ensure maximum length of 5
    if (code.length > 5) {
      code = code.substring(0, 5)
    }

    // Check if code exists and modify if needed
    let finalCode = code
    let counter = 1
    
    while (existingDepartments.some(d => d.code === finalCode)) {
      if (code.length < 4) {
        finalCode = code + counter
      } else {
        finalCode = code.substring(0, 3) + counter
      }
      counter++
      
      // Prevent infinite loop
      if (counter > 99) break
    }

    return finalCode
  }
}

// Export singleton instance
export const departmentService = DepartmentService.getInstance()
